import {request, BaseResponseType, urlPrefix} from './apiFunction';
import {getSqlFileName} from '@pages/WorkArea/utils';

// 提交作业参数接口
interface SubmitJobParams {
  computeId: string;
  jobType: string;
  connectionId: string;
  statement: string;
}

// 提交作业返回结果接口
interface SubmitJobResult {
  jobId: string;
}

// 停止作业参数接口
interface StopJobParams {
  computeId: string;
}

// 停止作业返回结果接口
interface StopJobResult {
  status: string;
}

// 作业状态
export enum JobStatus {
  RUNNING = 'RUNNING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  KILLED = 'KILLED'
}
// 作业状态返回结果接口
export interface JobStatusResult {
  startTime: string;
  endTime: string;
  status: JobStatus;
}

// 新建文件参数接口
interface CreateFileParams {
  parentId: string;
  name?: string;
}

// 文件信息接口
interface FileInfo {
  id: string;
  name: string;
  path: string;
  parentId?: string;
  creator: string;
  createdAt: string;
  type?: string;
  status?: string;
  nodeType?: string;
}

// 保存文件参数接口
interface SaveFileParams {
  content: string;
  connectionId: string;
  connectionType: string;
  jobType: string;
  fileId: string;
}

// 文件内容接口
interface FileContentResult {
  content: string;
  connectionId: string;
  connectionType: string;
  jobType: string;
  name: string;
}

// 作业日志参数接口
export interface JobLogParams {
  index: string;
  level?: string;
}

// 作业日志返回结果接口
interface JobLogResult {
  nextIndex: string;
  logContent: string[];
  index: string;
  logFinished: boolean;
}

// 作业执行结果接口
export interface JobOutputResult {
  columns: string[];
  rows: any[][];
}

/**
 * 提交作业
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param params 提交作业参数
 * @returns
 */
export function submitJob(
  workspaceId: string,
  fileId: string,
  params: SubmitJobParams
): BaseResponseType<SubmitJobResult> {
  // return Promise.resolve({
  //   result: {
  //     jobId: '123'
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/start`,
    method: 'POST',
    data: params
  });
}

/**
 * 停止作业
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @param params 停止作业参数
 * @returns
 */
export function stopJob(
  workspaceId: string,
  fileId: string,
  jobId: string,
  params: StopJobParams
): BaseResponseType<StopJobResult> {
  // return Promise.resolve({
  //   result: {
  //     status: 'KILLED'
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/stop`,
    method: 'POST',
    data: params
  });
}

/**
 * 获取作业状态
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @param computeId 计算实例ID
 * @returns
 */
export function getJobStatus(
  workspaceId: string,
  fileId: string,
  jobId: string,
  computeId?: string
): BaseResponseType<JobStatusResult> {
  // return Promise.resolve({
  //   result: {
  //     startTime: '2025-08-21 10:31:53',
  //     endTime: '2025-08-21 10:31:53',
  //     status: JobStatus.RUNNING
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/status`,
    method: 'GET',
    params: {computeId}
  });
}

/**
 * 新建文件
 * @param workspaceId 工作空间ID
 * @param params 新建文件参数
 * @returns
 */
export function createFile(workspaceId: string, params: CreateFileParams): BaseResponseType<FileInfo> {
  // return Promise.resolve({
  //   result: {
  //     id: '1',
  //     name: 'test.sql',
  //     path: 'test',
  //     parentId: 'test',
  //     nodeType: 'FILE',
  //     creator: 'test',
  //     createdAt: 'test'
  //   },
  //   success: true,
  //   status: 200
  // });
  const finalParams = {
    ...params,
    name: params.name || getSqlFileName()
  };
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/files`,
    method: 'POST',
    data: finalParams
  });
}

/**
 * 保存文件
 * @param workspaceId 工作空间ID
 * @param params 保存文件参数
 * @returns
 */
export function saveFile(workspaceId: string, params: SaveFileParams): BaseResponseType<FileInfo> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/files`,
    method: 'PUT',
    data: params
  });
}

/**
 * 查看文件
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @returns
 */
export function getFile(workspaceId: string, fileId: string): BaseResponseType<FileContentResult> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/files`,
    method: 'GET',
    params: {fileId}
  });
}

/**
 * 获取作业日志
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @param params 日志参数
 * @returns
 */
export function getJobLog(
  workspaceId: string,
  fileId: string,
  jobId: string,
  params: JobLogParams
): BaseResponseType<JobLogResult> {
  // return Promise.resolve({
  //   result: {
  //     logContent: ['2025-08-21 10:31:53', '2025-08-21 10:31:54', '2025-08-21 10:31:55'],
  //     index: '0',
  //     logFinished: false,
  //     nextIndex: '0'
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/log`,
    method: 'GET',
    params
  });
}

/**
 * 获取作业执行结果
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @returns
 */
export function getJobOutput(
  workspaceId: string,
  fileId: string,
  jobId: string
): BaseResponseType<JobOutputResult> {
  // return Promise.resolve({
  //   result: {
  //     columns: ['id', 'name'],
  //     rows: [[1, 'test']]
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/output`,
    method: 'GET'
  });
}

/**
 * 下载作业日志
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @returns
 */
export function downloadJobLog(workspaceId: string, fileId: string, jobId: string): BaseResponseType<any> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/log/download`,
    method: 'GET'
  });
}

/**
 * 下载作业执行结果
 * @param workspaceId 工作空间ID
 * @param fileId 文件ID
 * @param jobId 作业ID
 * @returns
 */
export function downloadJobOutput(workspaceId: string, fileId: string, jobId: string): BaseResponseType<any> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/editor/${fileId}/job/${jobId}/output/download`,
    method: 'GET'
  });
}
