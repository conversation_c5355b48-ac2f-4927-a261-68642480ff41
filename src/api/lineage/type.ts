import {ResourceType} from '@api/permission/type';
/** 血缘方向 */
export enum LineageDirection {
  Upstream = 'UPSTREAM',
  Downstream = 'DOWNSTREAM'
}

/** 资产类型 */
export enum AssetType {
  TABLE = ResourceType.Table,
  VOLOME = ResourceType.Volume
}

/** 表级血缘请求参数 */
export interface QueryLineageRequest {
  /** 需要查询的数据集 (uc表: catalog.schema.table | uc卷: catalog.schema.volume) */
  fullName: string;
  /** 资产类型: TABLE 表 | VOLUME 卷 */
  type: AssetType;
  /** VOLUME 卷时传递，为空则查询整个卷 */
  path?: string;
  /** 血缘方向: UPSTREAM 上游 | DOWNSTREAM 下游 */
  lineageDirection: LineageDirection;
  /** 起始时间戳 (用于时间范围过滤) */
  startTimestamp: number;
  /** 当前页数 (默认 1) */
  pageNo?: number;
  /** 每页条数 (默认 1000) */
  pageSize?: number;
}
export enum ExternalAssetType {
  EXTERNAL_PATH = 'EXTERNAL_PATH',
  EXTERNAL_TABLE = 'EXTERNAL_TABLE'
}
/* 外部路径/表 血缘请求参数 */
export interface QueryExternalLineageRequest {
  /**资产类型 */
  type: ExternalAssetType;
  /** 数据源名 */
  dataSourceName: string;
  /** 数据表名 外部表时传递 database.table */
  tableName?: string;
  /** 数据卷路径 外部文件时传递 */
  path?: string;
  /** 血缘方向: UPSTREAM 上游 | DOWNSTREAM 下游 */
  lineageDirection: LineageDirection;
  /** 起始时间戳 (用于时间范围过滤) */
  startTimestamp: number;
  /** 当前页数 (默认 1) */
  pageNo?: number;
  /** 每页条数 (默认 1000) */
  pageSize?: number;
}

/** 表级血缘响应 */
export interface QueryLineageResponse {
  /** 血缘信息列表 */
  lineages: LineageInfo[];
  /** 当前页数 */
  pageNo: number;
  /** 每页条数 */
  pageSize: number;
  /** 记录总数 */
  totalCount: number;
}

/** 单条血缘信息 */
export interface LineageInfo {
  /** UC表信息 */
  tableInfo?: TableInfo | null;
  /** UC卷信息 */
  volumeInfo?: VolumeInfo | null;
  /** 外部表信息 */
  externalTableInfo?: ExternalTableInfo | null;
  /** 外部文件信息 */
  externalFileInfo?: ExternalFileInfo | null;
  /** 工作流信息列表 */
  workflowInfos?: WorkflowInfo[];
  /** 集成作业信息列表 */
  integrationJobInfos?: IntegrationJobInfo[];
  /** Notebook 信息列表 */
  notebookInfos?: NotebookInfo[];
  /** 血缘产生的时间戳 */
  lineageTimestamp?: number | null;
}

/** Unity Catalog 表 */
export interface TableInfo {
  /** Catalog 名 */
  catalogName: string;
  /** Schema 名 */
  schemaName: string;
  /** 表名 */
  tableName: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** Unity Catalog 卷 */
export interface VolumeInfo {
  /** Catalog 名 */
  catalogName: string;
  /** Schema 名 */
  schemaName: string;
  /** Volume 名 */
  volumeName: string;
  /** 文件路径 */
  path: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 外部表 */
export interface ExternalTableInfo {
  /** 数据源名称 */
  datasourceName: string;
  /** Schema 名 */
  schemaName: string;
  /** 表名 */
  tableName: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 外部文件 */
export interface ExternalFileInfo {
  /** 数据源名称 */
  datasourceName: string;
  /** 文件路径 (如 schema://xxxxx/xxx/xx) */
  path: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 工作流信息 */
export interface WorkflowInfo {
  /** 工作空间ID */
  workspaceId: string;
  /** 工作流ID */
  workflowJobId: string;
  /** 工作流实例ID */
  workflowInstanceId: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 集成作业信息 */
export interface IntegrationJobInfo {
  /** 工作空间ID */
  workspaceId: string;
  /** 集成作业 ID */
  integrationJobId: string;
  /** 集成作业实例 ID */
  integrationRunId: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** Notebook 信息 */
export interface NotebookInfo {
  /** 工作空间ID */
  workspaceId: string;
  /** Notebook ID */
  notebookId: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 字段级血缘请求参数 */
export interface QueryColumnLineageRequest {
  /** 完整目标表名 (catalog.schema.table) */
  tableFullName: string;
  /** 目标字段名 */
  columnName: string;
  /** 限定的来源或目标表列表拼接成的字符串
   *  - uc表: catalog.schema.table
   *  - 外部数据源表: <EMAIL>
   */
  acceptTables: string;
  /** 血缘方向: UPSTREAM 上游 | DOWNSTREAM 下游 */
  lineageDirection: LineageDirection;
  /** 起始时间戳 */
  startTimestamp: number;
}

/** 字段级血缘响应 */
export interface QueryColumnLineageResponse {
  /** 字段血缘信息列表 */
  columnLineageInfos: ColumnLineageInfo[];
  /** 外部表的字段血缘信息列表 */
  externalColumnLineageInfos: ExternalColumnLineageInfo[];
}

/** 字段血缘信息 */
export interface ColumnLineageInfo {
  /** Catalog 名 */
  catalogName: string;
  /** Schema 名 */
  schemaName: string;
  /** 表名 */
  tableName: string;
  /** 字段名 */
  columnName: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

/** 外部表字段血缘信息 */
export interface ExternalColumnLineageInfo {
  /** 外部数据源名称 */
  datasourceName: string;
  /** Schema 名 */
  schemaName: string;
  /** 表名 */
  tableName: string;
  /** 字段名 */
  columnName: string;
  /** 血缘时间戳 */
  lineageTimestamp: number;
}

export interface queryVolumeLineagePathReq {
  fullName: string;
  lineageDirection: LineageDirection;
  startTimestamp: number;
}
