/**
 * 数据血缘接口
 */
import {request, urlPrefix, BaseResponseType} from '@api/apiFunction';
import {
  QueryLineageRequest,
  QueryLineageResponse,
  QueryColumnLineageRequest,
  QueryColumnLineageResponse,
  queryVolumeLineagePathReq,
  QueryExternalLineageRequest
} from './type';
/**
 * 查询数据表血缘or数据卷血缘
 * 可以查询血缘上游/下游，获取列表页需要调用上游&下游两次接口
 * 支持返回全部/上下一层
 */
export function queryLineage(
  workspaceId: string,
  params: QueryLineageRequest
): BaseResponseType<QueryLineageResponse> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/lineage`,
    method: 'GET',
    params
  });
}
/**
 * 查询外部表和外部路径的血缘 （返回内容和queryLineage一样，仅传参差异）
 */
export function queryExternalLineage(
  workspaceId: string,
  params: QueryExternalLineageRequest
): BaseResponseType<QueryLineageResponse> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/lineage/external`,
    method: 'GET',
    params
  });
}

/**
 * 查询字段血缘
 */
export function queryColumnLineage(
  workspaceId: string,
  params: QueryColumnLineageRequest
): BaseResponseType<QueryColumnLineageResponse> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/lineage/column`,
    method: 'GET',
    params
  });
}

/**
 * 查询数据卷下有血缘的路径列表
 */
export function queryVolumeLineagePath(
  workspaceId: string,
  params: queryVolumeLineagePathReq
): BaseResponseType<string[]> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/lineage/paths`,
    method: 'GET',
    params
  });
}
