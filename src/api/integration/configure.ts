import {request} from '@api/apiFunction';
import {API_PREFIX, Request_Method} from '@api/common';
import {BaseResponseType} from '@baidu/bce-react-toolkit';

// 配置类型
export enum ConfigType {
  HOCON = 'HOCON',
  JSON = 'JSON',
  SQL = 'SQL'
}

// 配置模式
export enum ConfigMode {
  Batch = 'batch',
  Stream = 'streaming'
}

/** 排序字段（目前仅支持按 createTime） */
export enum JobOrderBy {
  CreateTime = 'createTime'
}

export interface ConfigJobListReq {
  pageNo?: number;
  pageSize?: number;
  orderBy?: JobOrderBy;
  order?: string;
  mode?: ConfigMode;
  namePattern?: string;
}

export interface ConfigJobItem {
  computeName: string;
  configContent: string;
  configMode: ConfigMode;
  configFormat: ConfigType;
  createTime: string;
  creatorName: string;
  jobId: string;
  name: string;
  updaterName: string;
  updateTime: string;
  description: string;
  // 是否可运行
  canExecute: boolean;
}

export interface ConfigJobListRes {
  jobs?: ConfigJobItem[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
}

// 获取config job列表
export function getConfigJobList(
  workspaceId: string,
  params: ConfigJobListReq
): BaseResponseType<ConfigJobListRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/config-job`,
    params,
    method: Request_Method.Get
  });
}

/** 配置模式job的工作流类别 */
export enum ConfigCategory {
  // 库表采集
  TABLE = 'TABLE',
  // 文件采集
  FILE = 'FILE'
}

export interface CheckConfigureJobContentReq {
  /**
   * 配置模式内容
   */
  configContent: string;
  /**
   * 配置模式类型
   */
  configFormat: string;
  /**
   * config
   */
  type: 'config';
  /**
   * 配置模式job的工作流类别
   */
  configCategory: ConfigCategory;
}

// 校验合法性
export function checkConfigureJobContent(
  workspaceId: string,
  params: CheckConfigureJobContentReq
): BaseResponseType<{checkPass: boolean; errorInfo: string}> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/config-job/check`,
    data: params,
    method: Request_Method.Post
  });
}

// 暂停任务实例
export function suspendConfigureExecution(workspaceId: string, runId: string): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/execution/${runId}/suspend`,
    method: Request_Method.Post
  });
}

// 恢复任务实例
export function restoreConfigureExecution(workspaceId: string, runId: string): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/execution/${runId}/restore`,
    method: Request_Method.Post
  });
}
