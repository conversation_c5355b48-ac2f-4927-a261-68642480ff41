import {useState, useEffect, useContext, useRef, useMemo} from 'react';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {Allotment as ResizePanel, AllotmentHandle} from 'allotment';
import LeftSidebar from '@pages/WorkArea/editor/components/LeftSidebar';
import Workbench from '@pages/WorkArea/editor/components/Workbench';
import Tab from '@pages/WorkArea/editor/components/Tab';
const cx = classNames.bind(styles);

export default function WorkAreaEditor() {
  const leftSidebarRef = useRef(null);
  const onTabRename = () => {
    leftSidebarRef.current.refreshFileList();
  };
  const onAddTab = () => {
    leftSidebarRef.current.refreshFileList();
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const resizePanelRef = useRef<AllotmentHandle>(null);
  const initMinSize = 40; // icon区域的宽度
  const defaultSize = [initMinSize, 10000];
  const defaultLeftSize = 300 + initMinSize; // 打开时默认宽度
  const minLeftSize = 280; // 最小宽度
  const maxLeftSize = 560; // 最大宽度
  const [leftMin, setLeftMin] = useState(initMinSize);
  const [leftMax, setLeftMax] = useState(initMinSize);
  const handleCloseLeftSidebar = () => {
    // 关闭时禁止拖动
    setLeftMin(initMinSize);
    setLeftMax(initMinSize);
    setTimeout(() => {
      resizePanelRef.current?.resize(defaultSize);
    }, 100);
  };
  const handleOpenLeftSidebar = () => {
    setLeftMin(minLeftSize);
    setLeftMax(maxLeftSize);
    const containerWidth = containerRef.current?.clientWidth;
    setTimeout(() => {
      resizePanelRef.current?.resize([defaultLeftSize, containerWidth - defaultLeftSize]);
    }, 100);
  };

  return (
    <div ref={containerRef} className={cx('db-workspace-wrapper', 'work-area-editor')}>
      <ResizePanel ref={resizePanelRef} separator={false} defaultSizes={defaultSize}>
        <ResizePanel.Pane minSize={leftMin} maxSize={leftMax}>
          <div className={cx('left-sidebar')}>
            <LeftSidebar
              ref={leftSidebarRef}
              onClose={handleCloseLeftSidebar}
              onOpen={handleOpenLeftSidebar}
            />
          </div>
        </ResizePanel.Pane>
        <ResizePanel.Pane>
          <div className={cx('tab-workbench')}>
            <div className={cx('tab-wrap')}>
              <Tab onTabRename={onTabRename} onAddTab={onAddTab} />
            </div>
            <div className={cx('workbench-wrap')}>
              <Workbench onAddTab={onAddTab} />
            </div>
          </div>
        </ResizePanel.Pane>
      </ResizePanel>
    </div>
  );
}
