import React, {useState, useEffect, useRef} from 'react';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import useUrlState from '@ahooksjs/use-url-state';
import {getFile} from '@api/workEditor';
import {useWorkEditor} from '@store/workEditorStatehooks';
import JdbcEditor from './components/JdbcEditor';
import Empty from './components/Empty';
import {Loading} from 'acud';
const cx = classNames.bind(styles);

interface WorkbenchProps {
  onAddTab?: () => void;
}
export default function Workbench({onAddTab}: WorkbenchProps) {
  const [urlState, setUrlState] = useUrlState();
  const tabRef = useRef(null);

  const {tabList, activeTabId, setActiveTabId, resetState, setTabList} = useWorkEditor();

  const [loading, setLoading] = useState(false);
  useEffect(() => {
    async function initFirstTab() {
      if (!urlState.fileId) {
        return;
      }
      setLoading(true);
      try {
        const res = await getFile(urlState.workspaceId, urlState.fileId);
        if (res.success) {
          setTabList([res.result]);
          setActiveTabId(urlState.fileId);
        }
      } catch (error) {
        console.error('获取文件失败:', error);
      } finally {
        setLoading(false);
      }
    }
    initFirstTab();
    return () => {
      resetState();
    };
  }, []);

  if (loading) {
    return (
      <div className={cx('workbench')}>
        <Loading />
      </div>
    );
  }

  if (tabList.length === 0) {
    return <Empty onAdd={onAddTab} />;
  }

  return (
    <div className={cx('workbench')}>
      <div className={cx('viewport')}>
        {tabList.map((item) => {
          const isJdbc = item.nodeType === 'FILE' && item.name.endsWith('.sql');
          return (
            <div className={cx('viewport-item', {active: item.id === activeTabId})} key={item.id}>
              {isJdbc ? <JdbcEditor isActive={item.id === activeTabId} datasource={item} /> : null}
            </div>
          );
        })}
      </div>
    </div>
  );
}
