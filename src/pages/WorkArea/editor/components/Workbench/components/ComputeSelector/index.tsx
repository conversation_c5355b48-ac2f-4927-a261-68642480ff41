import React, {useState, useContext} from 'react';
import {Dropdown, Button, Tooltip, Loading, Link} from 'acud';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import type {ComputeResourceItem} from '@api/Compute';
import {getComputeResourceList} from '@api/Compute';
import {useRequest} from 'ahooks';
import {WorkspaceContext} from '@pages/index';
import useUrlState from '@ahooksjs/use-url-state';
import {STATUS} from '@pages/Compute/config';
import Arrow from '@assets/originSvg/notebook/arrow.svg';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import urls from '@utils/urls';
import LinkIcon from '@assets/originSvg/link.svg';

const cx = classNames.bind(styles);

interface ComputeSelectorProps {
  disabled?: boolean;
  onChange?: (computeId: string) => void;
  value?: string;
  engineFilter?: string[];
}
const ComputeSelector: React.FC<ComputeSelectorProps> = (props) => {
  const {engineFilter = ['Ray', 'Spark', 'JobAgent']} = props;
  const authList = useWorkspaceAuth([Privilege.ComputeMenu]);
  const hasComputeMenu = authList[Privilege.ComputeMenu];

  const [urlState, setUrlState] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const [activeCompute, setActiveCompute] = useState<ComputeResourceItem | null>(null);

  // 常驻实例
  const [computeList, setComputeList] = useState<ComputeResourceItem[]>([]);

  const {runAsync: getComputeList, loading: getComputeListLoading} = useRequest(getComputeResourceList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        const RaySparkList = res.result.computes.filter((item) => engineFilter.includes(item.engine));
        // 排序逻辑：status为RUNNING的放在前面
        const sortedRayList = [...RaySparkList].sort((a, b) => {
          if (a.status === 'RUNNING' && b.status !== 'RUNNING') return -1;
          if (a.status !== 'RUNNING' && b.status === 'RUNNING') return 1;
          return 0;
        });
        setComputeList(sortedRayList);
      }
    }
  });

  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      getComputeList({workspaceId});
    }
  };

  const handleComputeClick = (compute: ComputeResourceItem) => {
    return (e: React.MouseEvent) => {
      if (disabledCompute(compute)) {
        e.stopPropagation();
        return;
      }
      setActiveCompute(compute);
      props.onChange?.(compute.computeId);
    };
  };

  // 渲染相关
  const renderComputeStatus = (compute: ComputeResourceItem) => {
    if (!compute) {
      return <div className={cx('status-icon', 'none')}></div>;
    }
    const statusObj = STATUS.fromValue(compute.status);
    return <div className={cx('status-icon', statusObj.className)}></div>;
  };

  const renderTag = (compute: ComputeResourceItem) => {
    const {engine, mirrorVersion} = compute;
    const tagMap =
      {
        JobAgent: 'JDBC',
        Ray: `Ray ${mirrorVersion || ''}`,
        Spark: `Spark ${mirrorVersion || ''}`
      }[engine] || engine;
    return <span className={cx('tag')}>{tagMap}</span>;
  };

  const disabledCompute = (compute: ComputeResourceItem) => {
    return compute.status !== 'RUNNING';
  };

  const menu = (
    <div className={cx('compute-selector')}>
      {getComputeListLoading && <Loading loading size="small" />}
      <div className={cx('compute-list')}>
        <div className={cx('compute-list-title')}>常驻实例</div>
        {computeList.map((compute) => (
          <div
            className={cx('compute-item', {
              disabled: disabledCompute(compute),
              selected: compute.computeId === props.value
            })}
            key={compute.computeId}
            onClick={handleComputeClick(compute)}
          >
            <span className={cx('flex items-center')}>
              {renderComputeStatus(compute)}
              <span className={cx('item-text')} title={compute.name}>
                {compute.name}
              </span>
            </span>
            {renderTag(compute)}
          </div>
        ))}
      </div>
      <div className={cx('footer')}>
        <AuthComponents tooltipType={TooltipType.Function} isAuth={hasComputeMenu}>
          <Link href={`#${urls.compute}?workspaceId=${workspaceId}`} target="_blank">
            前往计算资源管理
            <LinkIcon className={cx('link-icon')} />
          </Link>
        </AuthComponents>
      </div>
    </div>
  );
  return (
    <div className={cx('compute-selector-dropdown')}>
      <Dropdown
        {...props}
        overlayStyle={{width: '320px'}}
        overlay={menu}
        trigger={['click']}
        placement="bottomRight"
        onVisibleChange={handleVisibleChange}
        overlayClassName={cx('selector-dropdown')}
      >
        <Button className={cx('compute-btn')} disabled={props.disabled}>
          {renderComputeStatus(activeCompute)}
          <Tooltip title={activeCompute?.name || '请选择'} placement="bottom">
            <span className={cx('item-text')}>{activeCompute?.name || '请选择'}</span>
          </Tooltip>
          <Arrow className={cx('arrow')} />
        </Button>
      </Dropdown>
    </div>
  );
};

ComputeSelector.__ACUD_BUTTON = true;
export default ComputeSelector;
