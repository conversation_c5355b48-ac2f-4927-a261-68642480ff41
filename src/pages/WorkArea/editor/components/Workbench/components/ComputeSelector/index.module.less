.compute-selector-dropdown {
  :global(.acud-btn-default:hover) {
    color: #151b26;
    border-color: #d4d6d9;
  }
  :global(.acud-dropdown-open) {
    .arrow {
      transform: rotate(0deg);
    }
  }
}

.selector-dropdown {
  position: relative;
  overflow: hidden;
  padding: 0;
  padding-bottom: 32px;
  border: 0.5px solid #eaeef2;
}

.compute-btn {
  border: 1px solid #e5e9ed;
  .item-text {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.compute-selector {
  width: 100%;
  .split-line {
    border-bottom: 1px solid #e8e9eb;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .compute-selected {
    .selected-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 8px;

      .connect-status {
        color: #84868c;
      }

      .disconnect {
        color: #2468f2;
        cursor: pointer;
      }
    }
  }

  .compute-list {
    padding: 12px;
    .compute-list-title {
      color: #000;
      font-weight: 500;
      font-size: 13px;
      margin-bottom: 8px;
    }

    .compute-item {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      padding: 5px 8px;
      margin-bottom: 4px;
      &:last-child {
        margin-bottom: 0;
      }
      .item-text {
        max-width: 170px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .tag {
        background-color: #f7f9fc;
        color: #5c5f66;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        padding: 0 6px;
        border-radius: 4px;
        flex: none;
        border: 0.5px solid #eaeef2;
      }
      &:hover {
        background-color: #f7f9fc;
        border-radius: 4px;
        .tag {
          background-color: #fff;
        }
      }

      &.selected {
        background-color: #f2f5fa;
        border-radius: 4px;
        color: #333aff;
        font-weight: 500;
        .tag {
          background-color: #fff;
        }
      }

      &.disabled {
        cursor: not-allowed;
        background-color: #f7f7f9;
        color: #b8babf;
        border-radius: 4px;
        .tag {
          color: #b8babf;
        }
        &:hover {
          .tag {
            background-color: #f7f7f9;
          }
        }
      }
    }
  }

  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f2f5fa;

    :global {
      .acud-link-default {
        color: #495366;
        display: flex;
        align-items: center;
        &:hover {
          color: #818999;
        }
        &:active {
          color: #000;
        }
      }
    }

    .link-icon {
      margin-left: 4px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;

  &.none {
    background: #5c5f66;
  }

  &.running {
    background: #13b982;
  }

  &.deploying {
    background: #2468f2;
  }

  &.invalid {
    background: #5c5f66;
  }

  &.created-fail {
    background: #f33e3e;
  }

  &.connecting {
    width: 14px;
    height: 14px;
    background-image: url('~@assets/originSvg/notebook/compute-loading.svg?url');
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: center;
    animation: rotate 1s linear infinite;
  }
}

.arrow {
  margin-left: 12px;
  transform: rotate(180deg);
}
