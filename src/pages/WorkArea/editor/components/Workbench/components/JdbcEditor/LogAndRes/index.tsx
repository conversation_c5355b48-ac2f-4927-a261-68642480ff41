import {useEffect, useState} from 'react';
import {Tabs, Tooltip} from 'acud';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import LogView from '../LogView';
import OutputView from '../OutputView';
import {JobOutputResult, JobStatusResult} from '@api/workEditor';
import ActionBtn from '@components/ActionBtn';
import DownloadIcon from '@assets/originSvg/download.svg';
import CloseIcon from '@assets/originSvg/close.svg';
import {JDBC_STATUS_ENUM} from '@pages/WorkArea/config';
import {calculateDuration} from '@utils/moment';
import GoTopIcon from '@assets/originSvg/go-top.svg';
import GoBottomIcon from '@assets/originSvg/go-bottom.svg';
const cx = classNames.bind(styles);

interface LogAndResProps {
  logs: string[];
  output: JobOutputResult;
  jobDetail: JobStatusResult;
  onDownload?: (type: string) => void;
  onGoTop?: () => void;
  onGoBottom?: () => void;
  onClose?: () => void;
  showGoTop?: boolean;
  activeKey?: string;
}

export default function LogAndRes({
  logs,
  output,
  jobDetail,
  onDownload,
  onGoTop,
  onGoBottom,
  onClose,
  showGoTop,
  activeKey
}: LogAndResProps) {
  const [_activeKey, setActiveKey] = useState(activeKey || 'log');
  const {startTime, endTime, status} = jobDetail;
  const duration = calculateDuration(startTime, endTime);

  useEffect(() => {
    setActiveKey(activeKey || 'log');
  }, [activeKey]);

  return (
    <div className={cx('log-and-res')}>
      <div className={cx('job-detail')}>
        {status ? (
          <>
            <div className={cx('start-time')}>{startTime}</div>
            <div className={cx('duration')}>{duration}</div>
            <div className={cx('status', 'status-' + status)}>
              {JDBC_STATUS_ENUM.getTextFromAlias(status)}
            </div>
            <div className={cx('divider')}></div>
          </>
        ) : null}
        <Tooltip title="下载">
          <div>
            <ActionBtn
              type="dark"
              icon={<DownloadIcon />}
              disabled={!status || status === 'RUNNING'}
              onClick={() => {
                onDownload?.(_activeKey);
              }}
            />
          </div>
        </Tooltip>
        <Tooltip title={showGoTop ? '展开' : '收起'}>
          <div>
            <ActionBtn
              type="dark"
              icon={showGoTop ? <GoTopIcon /> : <GoBottomIcon />}
              onClick={() => {
                showGoTop ? onGoTop?.() : onGoBottom?.();
              }}
            />
          </div>
        </Tooltip>
        <Tooltip title="关闭">
          <div>
            <ActionBtn
              type="dark"
              icon={<CloseIcon />}
              onClick={() => {
                onClose?.();
              }}
            />
          </div>
        </Tooltip>
      </div>
      <Tabs className={cx('tabs')} activeKey={_activeKey} onChange={setActiveKey}>
        <Tabs.TabPane tab="执行信息" key="log">
          <LogView logs={logs} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="执行结果" key="output">
          <OutputView output={output} />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
}
