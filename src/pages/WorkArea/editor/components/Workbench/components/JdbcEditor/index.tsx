import React, {useContext, useEffect, useState, useRef, useMemo, useCallback} from 'react';
import {debounce} from 'lodash';
import MonacoSqlEditor from '@components/MonacoSqlEditor';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import ActionBtn from '@components/ActionBtn';
import SaveIcon from '@assets/originSvg/notebook/save.svg';
import ExportIcon from '@assets/originSvg/notebook/export.svg';
import UndoIcon from '@assets/originSvg/notebook/undo.svg';
import RedoIcon from '@assets/originSvg/notebook/redo.svg';
import {Button, Modal, Select, Tooltip} from 'acud';
import ComputeSelector from '../ComputeSelector';
import {Allotment as ResizePanel, AllotmentHandle} from 'allotment';
import LogAndRes from './LogAndRes';
import {ConnectionTypeMap} from '@pages/MetaData/Connection/constants';
import {queryConnectionList} from '@api/connection';
import {
  submitJob,
  JobStatus,
  getJobStatus,
  stopJob,
  getJobLog,
  JobLogParams,
  getJobOutput,
  saveFile,
  JobStatusResult
} from '@api/workEditor';
import {deleteFile} from '@api/WorkArea';
import {getFile} from '@api/workEditor';
import {urlPrefix} from '@api/apiFunction';
import {WorkspaceContext} from '@pages/index';
import {toast} from 'acud';
import {Poll} from '@lumino/polling';
import StartIcon from '@assets/originSvg/start.svg';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {getFilePrivilege, FilePrivilege} from '@pages/WorkArea/utils';
import {useWorkEditor} from '@store/workEditorStatehooks';
import {useHotkey} from '@hooks/useHotkey';
import MySqlIcon from '@assets/svg/mysql.svg?url';
import PostgreSqlIcon from '@assets/svg/postgresql.svg?url';
import OracleIcon from '@assets/svg/oracle.svg?url';
import HanaIcon from '@assets/svg/hana.svg?url';
import SqlServerIcon from '@assets/svg/sqlserver.svg?url';
import StopIcon from '@assets/originSvg/stop.svg';

const cx = classNames.bind(styles);

const connectionTypeList = ConnectionTypeMap[0].typeList.map((item) => ({
  label: item.type,
  value: item.type
}));
// .filter((item) => item.value === 'MySQL'); // 一期只支持MySQL

interface JdbcEditorProps {
  datasource: any;
  isActive?: boolean;
}

export default function JdbcEditor({datasource, isActive}: JdbcEditorProps) {
  const [sql, setSql] = useState('');
  const [computeId, setComputeId] = useState('');
  const {workspaceId} = useContext(WorkspaceContext);
  const [connectionType, setConnectionType] = useState(null);
  const [connectionId, setConnectionId] = useState(null);
  const [connectionList, setConnectionList] = useState([]);
  const resizePanelRef = useRef<AllotmentHandle>(null);
  const [notebookPrivilege, setNotebookPrivilege] = useState<FilePrivilege>({
    privilege: null,
    canManage: false,
    canModify: false,
    canExecute: false,
    canView: false
  });

  const {updateTabProperty} = useWorkEditor();

  const {canExecute, canModify, canManage} = notebookPrivilege;

  useEffect(() => {
    getFile(workspaceId, datasource.id).then(async (res) => {
      if (res.success) {
        const {connectionType, connectionId, content, privileges} = res.result;
        const connectType = connectionType || connectionTypeList[0].value;
        setSql(content);
        setConnectionType(connectType);
        setConnectionId(connectionId);
        const privilege = getFilePrivilege(privileges || []);
        setNotebookPrivilege(privilege);

        const connections = await getConnectionList(connectType);
        // 如果没有连接，设置第一个连接
        if (connections.length > 0 && !connectionId) {
          setConnectionId(connections[0].name);
        }
        // 如果连接不存在，设置为空
        const connectionIds = connections.map((item) => item.name);
        if (connectionId && !connectionIds.includes(connectionId)) {
          setConnectionId(null);
        }
      }
    });
  }, [datasource.id, workspaceId]);

  const [connectionLoading, setConnectionLoading] = useState(false);
  async function getConnectionList(connectType: string) {
    setConnectionLoading(true);
    try {
      const res = await queryConnectionList(workspaceId, {
        type: connectType,
        pageNo: 1,
        pageSize: -1
      });
      if (res.success) {
        setConnectionLoading(false);
        const connections = res.result.connections;
        setConnectionList(connections.map((item) => ({label: item.name, value: item.name})));
        return connections;
      }
    } catch (error) {
      console.error(error);
    } finally {
      setConnectionLoading(false);
    }
  }

  function handelConnectionTypeChange(value: string) {
    setConnectionType(value);
    setConnectionId(null);
    getConnectionList(value);
    updateTabProperty(datasource.id, {
      isDirty: true
    });
  }

  // 运行
  const [runLoading, setRunLoading] = useState(false);
  const [runStatus, setRunStatus] = useState('');
  const jobIdRef = useRef('');
  const computeIdRef = useRef('');
  async function handleRun() {
    if (runLoading) {
      return;
    }
    setRunLoading(true);
    setRunStatus(JobStatus.RUNNING);
    computeIdRef.current = computeId;
    setLogs([]);
    setOutput({columns: [], rows: []});
    setActiveKey('log');
    setJobDetail({status: '', startTime: '', endTime: ''});
    jobIdRef.current = '';
    clearStatusPoll();
    clearLogPoll();

    if (sizeRef.current[1] < 382) {
      const containerHeight = editorContentRef.current?.clientHeight;
      const editorHeight = containerHeight - 382;
      resizePanelRef.current?.resize([editorHeight, 382]);
    }

    try {
      const res = await submitJob(workspaceId, datasource.id, {
        computeId,
        connectionId: connectionId,
        statement: sql,
        jobType: 'JDBC_SQL'
      });
      if (res.success) {
        toast.success({
          message: '作业提交成功',
          duration: 5
        });
        jobIdRef.current = res.result.jobId;
        queryJobStatus(jobIdRef.current);
        queryJobLog(jobIdRef.current);
        return;
      }
      computeIdRef.current = '';
      jobIdRef.current = '';
      setRunStatus('');
    } catch (error) {
      toast.error({
        message: '作业提交失败',
        duration: 5
      });
      computeIdRef.current = '';
      jobIdRef.current = '';
      setRunStatus('');
    } finally {
      setRunLoading(false);
    }
  }

  const resetState = () => {
    computeIdRef.current = '';
    setRunStatus('');
  };

  // 作业状态轮询
  const statusPollRef = useRef<Poll | null>(null);
  const [jobDetail, setJobDetail] = useState({status: '', startTime: '', endTime: ''});
  function clearStatusPoll() {
    statusPollRef.current?.dispose();
    statusPollRef.current = null;
  }
  useEffect(() => {
    if (isActive) {
      statusPollRef.current?.start();
    } else {
      statusPollRef.current?.stop();
    }
  }, [isActive]);
  const queryJobStatus = async (currentJobId: string) => {
    clearStatusPoll();
    async function statusFactory() {
      try {
        const res = await getJobStatus(workspaceId, datasource.id, currentJobId, computeIdRef.current);
        if (!res.success) {
          throw new Error('Failed to get compute session');
        }
        if (currentJobId !== jobIdRef.current) {
          return;
        }

        const {status} = res.result;
        setJobDetail(res.result);

        if ([JobStatus.SUCCEEDED, JobStatus.FAILED, JobStatus.KILLED].includes(status)) {
          resetState();
          clearStatusPoll();
        }

        if (res.result.hasResult) {
          const res = await getJobOutput(workspaceId, datasource.id, jobIdRef.current);
          if (res.success) {
            toast.success({
              message: '作业输出成功',
              duration: 5
            });
            setOutput(res.result);
            setActiveKey('output');
          }
        }
      } catch (error) {
        console.error(error);
      }
    }
    statusPollRef.current = new Poll({
      auto: false,
      factory: () => statusFactory(),
      frequency: {
        interval: 5 * 1000,
        backoff: true,
        max: 300 * 1000
      },
      name: `db-status-polling-${jobIdRef.current}`
    });
    statusPollRef.current!.start();
  };

  // 作业日志轮询
  const logPollRef = useRef<Poll | null>(null);
  const [logs, setLogs] = useState([]);
  const [output, setOutput] = useState({columns: [], rows: []});
  const [activeKey, setActiveKey] = useState('log');
  function clearLogPoll() {
    logPollRef.current?.dispose();
    logPollRef.current = null;
  }
  useEffect(() => {
    if (isActive) {
      logPollRef.current?.start();
    } else {
      logPollRef.current?.stop();
    }
  }, [isActive]);
  const queryJobLog = async (currentJobId: string) => {
    clearLogPoll();
    async function logFactory(params: JobLogParams) {
      try {
        const res = await getJobLog(workspaceId, datasource.id, currentJobId, params);
        const {logContent = []} = res.result;
        if (currentJobId !== jobIdRef.current) {
          return;
        }
        setLogs((prev) => [...prev, ...logContent]);
        return res.result;
      } catch (error) {
        console.error(error);
      }
    }

    logPollRef.current = new Poll({
      auto: false,
      factory: async (state) => {
        const lastResponse = state?.payload;
        if (lastResponse && lastResponse.logFinished) {
          clearLogPoll();
          return lastResponse;
        }
        let params = {index: '0'};
        if (lastResponse && lastResponse.nextIndex) {
          params = {index: lastResponse.nextIndex};
        }
        const res = await logFactory(params);
        return res;
      },
      frequency: {
        interval: 3 * 1000,
        backoff: true,
        max: 300 * 1000
      },
      name: `db-log-polling-${jobIdRef.current}`
    });
    logPollRef.current!.start();
  };

  // 关闭后清理轮询
  useEffect(() => {
    return () => {
      clearStatusPoll();
      clearLogPoll();
    };
  }, []);

  // 停止作业
  const handleStop = async () => {
    try {
      const res = await stopJob(workspaceId, datasource.id, jobIdRef.current, {
        computeId: computeIdRef.current
      });
      if (res.success) {
        toast.success({
          message: '作业停止成功',
          duration: 5
        });
        setRunStatus('');
        return;
      }
    } catch (error) {
      toast.error({
        message: '作业停止失败',
        duration: 5
      });
    }
  };

  const [saveLoading, setSaveLoading] = useState(false);
  const handleSave = useCallback(async () => {
    if (saveLoading) {
      return;
    }
    try {
      setSaveLoading(true);
      const res = await saveFile(workspaceId, {
        fileId: datasource.id,
        content: sql,
        connectionId,
        connectionType,
        jobType: 'JDBC_SQL'
      });
      if (res.success) {
        toast.success({
          message: '保存成功',
          duration: 5
        });
        updateTabProperty(datasource.id, {
          isDirty: false
        });
      }
    } catch (error) {
      toast.error({
        message: '保存失败',
        duration: 5
      });
    } finally {
      setSaveLoading(false);
    }
  }, [saveLoading, sql, workspaceId, datasource.id, connectionId, connectionType, updateTabProperty]);

  async function handleExport() {
    const blob = new Blob([sql || ''], {type: 'text/plain'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = datasource.name || 'jdbc.sql';
    a.click();
    URL.revokeObjectURL(url);
  }

  async function handleDownload(type: string) {
    if (!jobIdRef.current) {
      return;
    }
    if (type === 'log') {
      window.open(
        `${urlPrefix}/workspaces/${workspaceId}/editor/${datasource.id}/job/${jobIdRef.current}/log/download`
      );
    }
    if (type === 'output') {
      window.open(
        `${urlPrefix}/workspaces/${workspaceId}/editor/${datasource.id}/job/${jobIdRef.current}/output/download`
      );
    }
  }

  const editorRef = useRef(null);
  function handleUndo() {
    editorRef.current?.undo();
  }

  function handleRedo() {
    editorRef.current?.redo();
  }

  function onConnectionVisibleChange(visible: boolean) {
    if (visible) {
      getConnectionList(connectionType);
    }
  }

  const selectDisabled = runStatus === JobStatus.RUNNING;

  const defaultSize = [10000, 0];
  const logFullSize = [0, 10000];
  const sizeRef = useRef(defaultSize);
  const [showGoTop, setShowGoTop] = useState(true);
  const editorContentRef = useRef(null);
  const handleChange = useMemo(
    () =>
      debounce((sizes) => {
        sizeRef.current = sizes;
        if (sizes[0] > 0) {
          setShowGoTop(true);
        } else {
          setShowGoTop(false);
        }
      }, 100),
    []
  );

  function handlePanelClose() {
    resizePanelRef.current?.resize(defaultSize);
  }

  function handleGoTop() {
    resizePanelRef.current?.resize(logFullSize);
  }

  function handleGoBottom() {
    resizePanelRef.current?.resize(defaultSize);
  }

  function renderConnectionTypeIcon(type: string) {
    const iconName = type?.toLocaleLowerCase();
    const iconMap = {
      mysql: MySqlIcon,
      postgresql: PostgreSqlIcon,
      oracle: OracleIcon,
      hana: HanaIcon,
      sqlserver: SqlServerIcon
    };
    return (
      <div className={cx('connection-type-icon')}>
        <img src={iconMap[iconName]} alt="" />
      </div>
    );
  }

  useHotkey('sql:save', {
    keys: ['Accel S'],
    selector: `.hotkey-${datasource.id}`,
    execute: handleSave
  });

  useHotkey('sql:run', {
    keys: ['Accel Enter'],
    selector: `.hotkey-${datasource.id}`,
    execute: handleRun
  });

  const runDisabled = !computeId || !connectionId || !sql;
  const hasRunPermission = canExecute || canModify || canManage;

  return (
    <div className={cx('jdbc-editor', `hotkey-${datasource.id}`)}>
      <div className={cx('editor-toolbar')} style={{height: '52px'}}>
        <div className={cx('toolbar-left')}>
          <AuthComponents isAuth={canModify || canManage}>
            <ActionBtn icon={<SaveIcon />} onClick={handleSave} disabled={saveLoading}>
              保存
            </ActionBtn>
          </AuthComponents>
          <AuthComponents isAuth={canManage}>
            <ActionBtn icon={<ExportIcon />} onClick={handleExport}>
              导出
            </ActionBtn>
          </AuthComponents>
          <AuthComponents isAuth={canModify || canManage}>
            <ActionBtn icon={<UndoIcon />} onClick={handleUndo}>
              撤销
            </ActionBtn>
          </AuthComponents>
          <AuthComponents isAuth={canModify || canManage}>
            <ActionBtn icon={<RedoIcon />} onClick={handleRedo}>
              重做
            </ActionBtn>
          </AuthComponents>
        </div>
        <div className={cx('toolbar-right')}>
          <div className={cx('file-type')}>JDBC SQL</div>
          <AuthComponents isAuth={canExecute || canModify || canManage}>
            <ComputeSelector
              disabled={selectDisabled}
              value={computeId}
              engineFilter={['JobAgent']}
              onChange={setComputeId}
            />
          </AuthComponents>
          <div className={cx('divider')}></div>
          {runStatus === JobStatus.RUNNING ? (
            <Button type="highlight" className={cx('run-btn')} onClick={handleStop}>
              <StopIcon className={cx('start-icon')} />
              中断
            </Button>
          ) : (
            <AuthComponents isAuth={hasRunPermission}>
              <Tooltip
                title={hasRunPermission && runDisabled ? '请先选择计算资源、数据源并编写SQL语句后运行' : null}
                placement="topRight"
              >
                <Button type="primary" className={cx('run-btn')} onClick={handleRun} disabled={runDisabled}>
                  <StartIcon className={cx('start-icon')} />
                  运行
                </Button>
              </Tooltip>
            </AuthComponents>
          )}
        </div>
      </div>
      <div className={cx('editor-subtoolbar')} style={{height: '44px'}}>
        <AuthComponents isAuth={canExecute || canModify || canManage}>
          <Select
            className={cx('connection-type-select')}
            value={connectionType}
            onChange={handelConnectionTypeChange}
            disabled={selectDisabled}
          >
            {connectionTypeList.map((item) => {
              return (
                <Select.Option key={item.value} value={item.value}>
                  <div className={cx('connection-type-wrap')}>
                    {renderConnectionTypeIcon(item.value)}
                    {item.label}
                  </div>
                </Select.Option>
              );
            })}
          </Select>
        </AuthComponents>
        <AuthComponents isAuth={canExecute || canModify || canManage}>
          <Select
            showSearch
            loading={connectionLoading}
            value={connectionId}
            onChange={(value) => {
              setConnectionId(value);
              updateTabProperty(datasource.id, {
                isDirty: true
              });
            }}
            onDropdownVisibleChange={onConnectionVisibleChange}
            disabled={selectDisabled}
          >
            {connectionList.map((item) => {
              return (
                <Select.Option key={item.value} value={item.value}>
                  <span title={item.label}>{item.label}</span>
                </Select.Option>
              );
            })}
          </Select>
        </AuthComponents>
      </div>
      <div ref={editorContentRef} className={cx('editor-content')} style={{height: 'calc(100% - 96px)'}}>
        <ResizePanel
          ref={resizePanelRef}
          separator={showGoTop}
          vertical
          defaultSizes={defaultSize}
          onChange={handleChange}
        >
          <ResizePanel.Pane minSize={0}>
            <MonacoSqlEditor
              ref={editorRef}
              value={sql}
              onChange={(value) => {
                setSql(value);
                updateTabProperty(datasource.id, {
                  isDirty: true
                });
              }}
              readOnly={!canModify && !canManage}
            />
          </ResizePanel.Pane>
          <ResizePanel.Pane preferredSize={382} minSize={40}>
            <LogAndRes
              activeKey={activeKey}
              logs={logs}
              output={output}
              showGoTop={showGoTop}
              jobDetail={jobDetail as JobStatusResult}
              onDownload={handleDownload}
              onClose={handlePanelClose}
              onGoTop={handleGoTop}
              onGoBottom={handleGoBottom}
            />
          </ResizePanel.Pane>
        </ResizePanel>
      </div>
    </div>
  );
}
