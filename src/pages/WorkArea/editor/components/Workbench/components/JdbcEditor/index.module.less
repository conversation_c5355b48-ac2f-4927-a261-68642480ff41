.jdbc-editor {
  height: 100%;
  .editor-toolbar {
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #eaeef2;
    box-sizing: border-box;
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
      .file-type {
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #f7f9fc;
        height: 28px;
        padding: 2px 8px;
        font-weight: 500;
        font-size: 12px;
        color: #151b26;
      }
      .divider {
        width: 1px;
        height: 28px;
        background-color: #e4e7eb;
      }

      .run-btn {
        width: 74px;
        .start-icon {
          margin-right: 6px;
        }
      }
    }
  }
  .editor-subtoolbar {
    padding: 0 16px;
    height: 44px;
    box-sizing: border-box;
    border-bottom: 1px solid #eaeef2;
    display: flex;
    align-items: center;

    .connection-type-select {
      margin-right: 8px;
    }

    :global {
      .acud-select {
        min-width: 120px;
        .acud-select-selector {
          &:hover {
            background-color: #f2f5fa !important;
          }
          &:active {
            background-color: #e4e8f0 !important;
          }
        }
      }
      .acud-select-selector {
        border: none !important;
      }
      .acud-select-selection-item {
        font-weight: 500;
      }
    }
  }

  :global {
    .monaco-editor .view-overlays .current-line-exact {
      border: none;
      background: rgba(247, 249, 252, 1);
    }
    .monaco-editor .margin-view-overlays .current-line {
      background: rgba(247, 249, 252, 1);
    }

    .monaco-editor .line-numbers {
      color: rgba(129, 137, 153, 1);
    }

    .monaco-editor .line-numbers.active-line-number {
      color: #000;
      font-weight: 500;
    }

    .monaco-editor .wordHighlightText {
      background-color: rgba(188, 196, 252, 1);
    }

    .monaco-editor .selected-text {
      background-color: #e8e9ff;
    }
  }
}

.connection-type-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
  .connection-type-icon {
    border: 0.5px solid #e4e8f0;
    flex: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f9fc;
    overflow: hidden;
  }
}
