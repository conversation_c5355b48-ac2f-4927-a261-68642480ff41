import {JobOutputResult} from '@api/workEditor';
import ResultEmpty from '@assets/png/workarea/result-empty.png';
import {Table} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
const cx = classNames.bind(styles);

interface OutputViewProps {
  output: JobOutputResult;
}

export default function OutputView({output}: OutputViewProps) {
  const columns = output.columns?.map((item, index) => ({
    title: item,
    dataIndex: `${item}-${index}`,
    width: '120px',
    key: `${item}-${index}`
  }));
  const dataSource = output.rows?.map((item) => {
    return item.reduce((acc, cur, index) => {
      acc[columns[index].dataIndex] = cur;
      return acc;
    }, {});
  });

  const hasData = columns?.length; // 模拟常规 SQL 语句运行输出展示

  return (
    <div className={cx('output-view')}>
      {hasData ? (
        <div className={cx('table')}>
          <div className={cx('table-title')}>
            <span className={cx('table-title-bold')}>数据表</span>
            <span className={cx('table-title-text')}>仅支持预览前1000行数据</span>
          </div>
          <Table columns={columns} dataSource={dataSource} pagination={false} scroll={{x: 'max-content'}} />
        </div>
      ) : (
        <div className={cx('empty')}>
          <img className={cx('empty-img')} src={ResultEmpty} alt="" />
          <div className={cx('empty-text')}>暂无数据</div>
        </div>
      )}
    </div>
  );
}
