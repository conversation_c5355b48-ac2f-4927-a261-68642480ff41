.log-and-res {
  position: relative;
  height: 100%;
  .job-detail {
    z-index: 100;
    position: absolute;
    top: 8px;
    right: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495366;

    .start-time {
    }
    .duration {
    }
    .divider {
      width: 1px;
      height: 14px;
      background-color: #e4e7eb;
    }
    .status {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      border: 0.5px solid transparent;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      padding: 0 8px;
      user-select: none;
      &.status-RUNNING {
        color: #333aff;
        border-color: #c7c9ff;
        background-color: #f6f6ff;
      }
      &.status-SUCCEEDED {
        color: #00b97c;
        border-color: #b6f0da;
        background-color: #e6faf1;
      }
      &.status-FAILED {
        color: #fa423c;
        border-color: #ffc3bd;
        background-color: #ffeceb;
      }
      &.status-KILLED {
        color: #495366;
        border-color: #e4e8f0;
        background-color: #f7f9fc;
      }
    }
  }

  .tabs {
    height: 100%;
    :global {
      .acud-tabs-nav {
        margin-bottom: 0;
        height: 40px;
        padding: 0 28px;
        background-color: #f7f9fc;
        &::before {
          border-bottom: none;
        }
      }
      .acud-tabs-content {
        height: 100%;
      }
      .acud-tabs-content-holder {
        height: calc(100% - 40px);
      }
    }
  }
}
