import React, {useMemo, useState} from 'react';
import _ from 'lodash';
import LogVirtualScroll from '@components/LogVirtualScroll';
import {Radio} from 'acud';
import ResultEmpty from '@assets/png/workarea/result-empty.png';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);

interface LogViewProps {
  logs: string[];
}

export default function LogView({logs}: LogViewProps) {
  const [levelFilter, setLevelFilter] = useState('INFO');
  const logList = useMemo(
    () =>
      _.map(logs, (log, index) => {
        // log的格式是: INFO 2025-08-20 19:03:48.034 [main:c.b.i.r.j.j.JDBCSubmit@56] JDBC job finished
        // 行首是level，共有三种: INFO WARN ERROR
        // level后是timestamp，格式是: 2025-08-20 19:03:48.034
        // 剩余的为日志内容
        // 正则表达式匹配日志格式
        const logRegex = /^(INFO|WARN|ERROR)\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})\s+([\s\S]+)$/;
        const match = log.match(logRegex);

        if (match) {
          const [, level, timestamp, content] = match;
          // 将WARNING映射到warn，保持与LogItem接口一致
          return {
            id: index,
            content: content,
            timestamp: timestamp,
            level
          };
        }

        // 如果无法匹配格式，返回原始日志内容，默认level为info
        return {
          id: index,
          content: log,
          timestamp: '',
          level: ''
        };
      }).filter((log) => {
        if (levelFilter === 'INFO') {
          return true;
        }
        if (levelFilter === 'WARN') {
          return log.level === 'WARN' || log.level === 'ERROR';
        }
        if (levelFilter === 'ERROR') {
          return log.level === 'ERROR';
        }
      }),
    [logs, levelFilter]
  );

  return (
    <div className={cx('log-view')}>
      {logs.length === 0 ? (
        <div className={cx('empty')}>
          <img className={cx('empty-img')} src={ResultEmpty} alt="" />
          <div className={cx('empty-text')}>暂无日志</div>
        </div>
      ) : (
        <>
          <div className={cx('log-filter')}>
            <Radio.Group
              value={levelFilter}
              optionType="button"
              onChange={(e) => setLevelFilter(e.target.value)}
              options={[
                {label: 'INFO', value: 'INFO'},
                {label: 'WARN', value: 'WARN'},
                {label: 'ERROR', value: 'ERROR'}
              ]}
            />
          </div>
          <div className={cx('log-content')}>
            {logList.length === 0 ? (
              <div className={cx('empty')}>
                <img className={cx('empty-img')} src={ResultEmpty} alt="" />
                <div className={cx('empty-text')}>暂无日志</div>
              </div>
            ) : (
              <LogVirtualScroll
                logs={logList}
                estimateSize={60}
                hasNextPage={false}
                isLoading={false}
                showTimestamp={true}
                wrapLines={true}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
