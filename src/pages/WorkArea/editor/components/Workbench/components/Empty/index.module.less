.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #495366;
  font-size: 14px;
  .empty-title {
    font-weight: 500;
    font-size: 20px;
    color: #000;
    line-height: 30px;
    margin-bottom: 8px;
  }
  .empty-sub-title {
    font-size: 14px;
    line-height: 22px;
    color: #818999;
    margin-bottom: 24px;
  }
  .empty-inner {
    display: flex;
    gap: 32px;
    .empty-card {
      box-sizing: border-box;
      overflow: hidden;
      width: 200px;
      border-radius: 12px;
      border: 1.7px solid transparent;
      box-shadow: 0 4px 12px 0 rgba(0, 25, 126, 0.02);
      cursor: pointer;
      background-color: #fff;
      &:hover {
        border: 1.7px solid #341bfa;
        box-shadow:
          8px -16px 22.9px 0 rgba(183, 181, 205, 0.03),
          0 76px 21px 0 rgba(204, 203, 222, 0.01),
          0 49px 19px 0 rgba(213, 212, 226, 0.01),
          -14px 27px 16px 0 rgba(169, 166, 203, 0.05),
          4px 12px 12px 0 rgba(215, 213, 230, 0.09),
          0 3px 7px 0 rgba(78, 74, 132, 0.1);
      }

      &.disabled {
        cursor: not-allowed;
      }

      .empty-card-wrap {
        border-radius: 12px;
        overflow: hidden;
        border: 0.5px solid #eceaff;
      }

      .empty-card-img {
        width: 100%;
        height: 160px;
        background-size: cover;
        background-position: center;
        &.notebook {
          background-image: url('~@assets/png/workarea/cr-nb.png');
        }
        &.file {
          background-image: url('~@assets/png/workarea/cr-file.png');
        }
        &.workflow {
          background-image: url('~@assets/png/workarea/cr-workflow.png');
        }
      }
      .empty-card-content {
        box-sizing: border-box;
        padding: 20px 16px;
        width: 100%;

        .empty-card-text {
          .empty-card-title {
            font-weight: 500;
            font-size: 14px;
            color: #000;
            margin-bottom: 4px;
            line-height: 22px;
            display: flex;
            align-items: center;
            gap: 8px;
            .empty-card-action {
              width: 16px;
              height: 16px;
              background-color: #000;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              box-shadow:
                0 0.5px 1px 0 rgba(3, 3, 27, 0.15),
                0 1px 2px 0 rgba(3, 3, 27, 0.07);
            }
          }
          .empty-card-desc {
            font-size: 12px;
            color: #495366;
            line-height: 18px;
          }
        }
      }
    }
  }
}
