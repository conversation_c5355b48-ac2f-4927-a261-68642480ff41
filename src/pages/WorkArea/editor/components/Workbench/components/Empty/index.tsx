import React, {useContext, useEffect, useState} from 'react';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import IconSvg from '@components/IconSvg';
import {useWorkEditor} from '@store/workEditorStatehooks';
import {createNotebook} from '@api/WorkArea';
import {createJob} from '@api/job';
import {createFile} from '@api/workEditor';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {useAddBtnDisabled} from '@hooks/useFile';
const cx = classNames.bind(styles);

interface EmptyProps {
  onAdd?: () => void;
}
export default function Empty({onAdd}: EmptyProps) {
  const {curFolderId, curFolder, curFolderPrivilege, pushTab, setActiveTabId} = useWorkEditor();
  const workspaceId = useContext(WorkspaceContext).workspaceId;
  const {canManage} = curFolderPrivilege;
  const authList = useWorkspaceAuth([Privilege.WorkflowCreate]);

  const addFileDisabled = useAddBtnDisabled(curFolder);

  const onAddNotebook = () => {
    createNotebook({workspaceId, parentId: curFolderId}).then((res) => {
      if (res.success) {
        window.open(
          `#${urls.notebook}?workspaceId=${workspaceId}&folderId=${curFolderId}&notebookId=${res.result.id}`
        );
        onAdd?.();
      }
    });
  };
  const onAddFile = () => {
    createFile(workspaceId, {
      parentId: curFolderId
    }).then((res) => {
      if (res.success) {
        pushTab(res.result);
        setActiveTabId(res.result.id);
        onAdd?.();
      }
    });
  };
  const onAddWorkflow = () => {
    createJob({parentDirId: curFolderId}, workspaceId).then((res) => {
      if (res.success && res.result) {
        window.open(`#${urls.jobDetail}?workspaceId=${workspaceId}&jobId=${res.result}`);
        onAdd?.();
      }
    });
  };

  return (
    <div className={cx('empty')}>
      <div className={cx('empty-title')}>暂无数据</div>
      <div className={cx('empty-sub-title')}>可以选择以下形式进行创建</div>
      <div className={cx('empty-inner')}>
        {/* <AuthComponents isAuth={canManage}>
          <Card title="创建笔记本" desc="交互式开发所见即所得" imgClass="notebook" onClick={onAddNotebook} />
        </AuthComponents> */}
        <AuthComponents isAuth={canManage}>
          <Card
            title="创建文件"
            desc="自由编辑文本与代码"
            imgClass="file"
            onClick={onAddFile}
            disabled={addFileDisabled}
          />
        </AuthComponents>
        {/* <AuthComponents isAuth={canManage && authList[Privilege.WorkflowCreate]}>
          <Card title="创建工作流" desc="自动化数据处理与分析" imgClass="workflow" onClick={onAddWorkflow} />
        </AuthComponents> */}
      </div>
    </div>
  );
}

interface CardProps {
  title: string;
  desc: string;
  imgClass: string;
  disabled?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}
function Card({title, desc, imgClass, disabled, onClick}: CardProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (disabled) {
      return;
    }
    onClick?.(e);
  };

  return (
    <div className={cx('empty-card', {disabled: disabled})} onClick={handleClick}>
      <div className={cx('empty-card-wrap')}>
        <div className={cx('empty-card-img', imgClass)}></div>
        <div className={cx('empty-card-content')}>
          <div className={cx('empty-card-text')}>
            <div className={cx('empty-card-title')}>
              <div className={cx('empty-card-action')}>
                <IconSvg type="add" color="#fff" size={10} />
              </div>
              {title}
            </div>
            <div className={cx('empty-card-desc')}>{desc}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

Card.__ACUD_BUTTON = true;
