import React, {useContext, useEffect, useState} from 'react';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {<PERSON><PERSON>, DialogBox} from 'acud';
import {createFile} from '@api/workEditor';
import {WorkspaceContext} from '@pages/index';
import {useWorkEditor} from '@store/workEditorStatehooks';
import {useFileIcon, useAddBtnDisabled} from '@hooks/useFile';
import {getFilePrivilege, getSqlFileName} from '@pages/WorkArea/utils';
import FileName from './FileName';
import {renameFile} from '@api/WorkArea';
import {useFileUnsavedPrompt} from '@pages/WorkArea/editor/hooks/useUnsavePrompt';
import CloseIcon from '@assets/originSvg/close.svg';
import ActionBtn from '@components/ActionBtn';
import IconSvg from '@components/IconSvg';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import AddIcon from '@assets/svg/add.svg';
const cx = classNames.bind(styles);

interface TabProps {
  onTabRename: () => void;
  onAddTab: () => void;
}
export default function Tab({onTabRename, onAddTab}: TabProps) {
  const {workspaceId} = useContext(WorkspaceContext);
  const {
    tabList,
    activeTabId,
    curFolderId,
    curFolder,
    curFolderPrivilege,
    pushTab,
    setActiveTabId,
    removeTab,
    updateTabProperty
  } = useWorkEditor();

  const {canManage} = curFolderPrivilege;
  const {blocker, hasUnsavedChanges} = useFileUnsavedPrompt(tabList);

  function handleTabClick(id: string) {
    setActiveTabId?.(id);
  }

  const [addLoading, setAddLoading] = useState(false);
  async function handleAdd() {
    if (addLoading) return;
    setAddLoading(true);
    try {
      const res = await createFile(workspaceId, {
        parentId: curFolderId
      });
      if (res.success) {
        pushTab(res.result);
        setActiveTabId(res.result.id);
        onAddTab();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setAddLoading(false);
    }
  }

  function closeTab(id: string) {
    removeTab(id);
    if (id === activeTabId) {
      const index = tabList.findIndex((item) => item.id === id);
      if (index > 0) {
        setActiveTabId(tabList[index - 1].id);
      } else if (tabList.length > 1) {
        handleTabClick(tabList[index + 1].id);
      }
    }
  }
  const [preCloseItem, setPreCloseItem] = useState(null);
  function handleTabClose(item) {
    return (e: React.MouseEvent) => {
      e.stopPropagation();
      if (item.isDirty) {
        setTabVisible(true);
        setPreCloseItem(item);
        return;
      }
      closeTab(item.id);
    };
  }

  const handleNameChange = (item) => {
    return async (newName: string) => {
      if (item.name !== newName) {
        try {
          const {success, result} = await renameFile({
            id: item.id,
            workspaceId,
            name: newName
          });
          if (success) {
            const finalName = result.name;
            updateTabProperty(item.id, {name: finalName});
            onTabRename();
          }
        } catch (error) {
          console.error('更新笔记本名称失败:', error);
        }
      }
    };
  };

  const {renderIcon} = useFileIcon();

  const isFileNameReadOnly = (item) => {
    const {canManage} = getFilePrivilege(item.privileges || []);
    return !canManage;
  };

  // 跳转、关闭浏览器页签未保存提示
  const [visible, setVisible] = useState<boolean>(false);
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setVisible(true);
    }
  }, [blocker.state]);
  const exitText = `当前存在未保存文件，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭`;

  // 关闭tab未保存提示
  const [tabVisible, setTabVisible] = useState<boolean>(false);
  const exitTextTab = `当前文件未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭`;

  const isDividerHide = (item, index, activeTabId) => {
    const lastOne = index === tabList.length - 1;
    const activeOne = item.id === activeTabId;
    const preActiveOne = tabList[index + 1]?.id === activeTabId;
    return lastOne || activeOne || preActiveOne;
  };

  const addTabDisabled = useAddBtnDisabled(curFolder);
  return (
    <div className={cx('tab')}>
      <div className={cx('tab-list')}>
        {tabList.map((item, index) => {
          return (
            <>
              <div
                key={item.id}
                className={cx('tab-item', {active: item.id === activeTabId})}
                onClick={() => handleTabClick(item.id)}
              >
                <div className={cx('tab-item-inner')}>
                  <div className={cx('tab-item-icon')}>{renderIcon(item)}</div>
                  <div className={cx('tab-item-name')}>
                    <span className={cx('hidden-name')}>{item.name}</span>
                    <FileName
                      className={cx('file-name')}
                      name={item.name}
                      readOnly={isFileNameReadOnly(item)}
                      onNameChange={handleNameChange(item)}
                    />
                  </div>
                  <div className={cx('tab-item-action', {dirty: item.isDirty})}>
                    <div className={cx('tab-item-dirty')}></div>
                    <ActionBtn
                      className={cx('tab-item-close')}
                      icon={<CloseIcon />}
                      onClick={handleTabClose(item)}
                    />
                  </div>
                </div>
              </div>
              <div className={cx('divider', {hide: isDividerHide(item, index, activeTabId)})}></div>
            </>
          );
        })}
      </div>
      <div className={cx('tab-add')}>
        <AuthComponents isAuth={canManage}>
          <ActionBtn
            type="dark"
            disabled={addTabDisabled}
            icon={<AddIcon width={16} height={16} />}
            onClick={handleAdd}
          ></ActionBtn>
        </AuthComponents>
      </div>
      <DialogBox
        visible={visible}
        title="未保存"
        content={exitText}
        type="warning"
        onCancel={() => {
          blocker.reset();
          setVisible(false);
        }}
        footer={
          <div>
            <Button
              onClick={() => {
                blocker.reset();
                setVisible(false);
              }}
            >
              取消
            </Button>
            <Button
              onClick={() => {
                blocker.proceed();
                setVisible(false);
              }}
            >
              立即关闭
            </Button>
          </div>
        }
      ></DialogBox>
      <DialogBox
        visible={tabVisible}
        title="未保存"
        content={exitTextTab}
        type="warning"
        onCancel={() => {
          setTabVisible(false);
        }}
        footer={
          <div>
            <Button
              onClick={() => {
                setTabVisible(false);
              }}
            >
              取消
            </Button>
            <Button
              onClick={() => {
                if (preCloseItem) {
                  closeTab(preCloseItem.id);
                }
                setTabVisible(false);
              }}
            >
              立即关闭
            </Button>
          </div>
        }
      ></DialogBox>
    </div>
  );
}
