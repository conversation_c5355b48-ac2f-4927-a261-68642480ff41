.tab {
  display: flex;
  align-items: center;
  background-color: #f2f5fa;
  height: 48px;

  .tab-list {
    height: 100%;
    display: flex;
    align-items: center;
    padding-top: 8px;
    padding-left: 8px;
    // padding-right: 8px;
    box-sizing: border-box;
    overflow: auto;
    // 隐藏滚动条
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .tab-item {
      position: relative;
      box-sizing: border-box;
      cursor: pointer;
      height: 100%;
      min-width: 148px;
      max-width: 339px;
      padding-bottom: 8px;

      .tab-item-inner {
        display: flex;
        align-items: center;
        height: 100%;
        border-radius: 6px;
        padding: 0 8px;
        &:hover {
          background-color: #e0e6f3;
        }
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        transform: translateX(-100%);
        background-color: transparent;
        width: 6px;
        height: 6px;
        clip-path: path('M6 0 V6 H0 A6 6 0 0 0 6 0 Z');
      }

      &::after {
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        transform: translateX(100%);
        background-color: transparent;
        width: 6px;
        height: 6px;
        clip-path: path('M0 0 V6 H6 A6 6 0 0 1 0 0 Z');
      }

      &.active {
        background-color: #fff;
        border-radius: 6px 6px 0 0;
        &::before {
          background-color: #fff;
        }
        &::after {
          background-color: #fff;
        }
        .tab-item-inner {
          &:hover {
            background-color: #fff;
          }
        }
      }

      .tab-item-icon {
        margin-right: 8px;
        height: 16px;
        width: 16px;
      }
      .tab-item-name {
        position: relative;
        min-width: 0;
        flex: auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .hidden-name {
          visibility: hidden;
        }

        .file-name {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
        }
      }
      .tab-item-action {
        flex: none;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 12px;
        width: 24px;
        height: 24px;
        color: #5c5f66;
        .tab-item-dirty {
          width: 8px;
          height: 8px;
          background-color: #818999;
          border-radius: 50%;
          display: none;
        }
        .tab-item-close {
          color: #5c5f66;
          padding: 2px;
        }

        &.dirty {
          .tab-item-dirty {
            display: block;
          }
          .tab-item-close {
            display: none;
          }
          &:hover {
            .tab-item-dirty {
              display: none;
            }
            .tab-item-close {
              display: block;
            }
          }
        }
      }
    }

    .divider {
      flex: none;
      position: relative;
      width: 8px;
      height: 100%;
      background-color: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 8px;
      box-sizing: border-box;
      &::before {
        content: '';
        background-color: #d1d5de;
        width: 1px;
        height: 14px;
      }

      &.hide {
        &::before {
          display: none;
        }
      }
    }
  }
  .tab-add {
    padding-left: 8px;
    padding-right: 8px;
  }
}
