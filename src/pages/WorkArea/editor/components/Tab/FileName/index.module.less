.notebook-name {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
  .input-container {
    display: flex;
    width: 100%;
  }
  .name-input {
    box-sizing: border-box;
    font-size: 12px;
    color: #151b26;
    background-color: #fff;
    width: 100%;
    outline: none;
    border: none;
    font-family: inherit;

    &:focus {
    }

    &.error {
      border-color: #f33e3e;

      &:focus {
        border-color: #f33e3e;
      }
    }
  }
  .name-display {
    font-size: 12px;
    color: #151b26;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: background-color 0.2s;

    &.readonly {
      cursor: text;
    }
  }
}
.error-message {
  font-weight: 400;
  color: #f33e3e;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
  white-space: nowrap;
}

.title-tooltip {
  max-width: 280px;
}
