import React, {useState, useRef, useEffect} from 'react';
import {Tooltip} from 'acud';
import {validatePath, FILE_NAME_ERROR_MESSAGE} from '@pages/WorkArea/config';
import {getFileName} from '@pages/WorkArea/utils';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

interface NotebookNameProps {
  className?: string;
  name: string;
  readOnly?: boolean;
  onNameChange?: (newName: string) => void;
}

const NotebookName: React.FC<NotebookNameProps> = ({name, readOnly, className, onNameChange}) => {
  const {fileName, extension} = getFileName(name);
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(fileName);
  const [fileExtension, setFileExtension] = useState(extension);
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const {fileName, extension} = getFileName(name);
    setInputValue(fileName);
    setFileExtension(extension);
  }, [name]);

  // 进入编辑模式时自动聚焦输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // 校验输入名称
  const validateInput = (value: string) => {
    const trimmedValue = value.trim();
    if (!trimmedValue) {
      setError(true);
      setErrorMessage('名称不能为空');
      return false;
    }

    if (!validatePath(trimmedValue)) {
      setError(true);
      setErrorMessage(FILE_NAME_ERROR_MESSAGE);
      return false;
    }

    setError(false);
    setErrorMessage('');
    return true;
  };

  // 处理名称保存
  const handleSave = () => {
    const trimmedValue = inputValue.trim();

    const fullName = trimmedValue + fileExtension; // fileExtension 已包含点号
    // 校验名称合法性
    if (!validateInput(fullName)) {
      return; // 校验不通过，不退出编辑状态
    }

    if (fullName !== name && onNameChange) {
      // 只有当名称发生变化时才触发回调
      onNameChange(fullName);
    }
    setIsEditing(false);
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      // 取消编辑，恢复原名称
      const {fileName, extension} = getFileName(name);
      setInputValue(fileName);
      setFileExtension(extension);
      setError(false);
      setErrorMessage('');
      setIsEditing(false);
    }
  };

  return (
    <div className={cx('notebook-name', className)}>
      {isEditing ? (
        <div className={cx('input-container')}>
          <Tooltip visible={error} title={errorMessage} placement="bottom">
            <input
              ref={inputRef}
              className={cx('name-input', {error: error})}
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
                validateInput(e.target.value + fileExtension);
              }}
              onBlur={() => {
                if (!error) {
                  handleSave();
                }
              }}
              onKeyDown={handleKeyDown}
            />
          </Tooltip>
          {fileExtension}
        </div>
      ) : (
        <Tooltip title={name} placement="top" overlayClassName={cx('title-tooltip')}>
          <div
            className={cx('name-display', {readonly: readOnly})}
            // 双击编辑
            onDoubleClick={() => !readOnly && setIsEditing(true)}
            title="点击编辑名称"
          >
            {name}
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default NotebookName;
