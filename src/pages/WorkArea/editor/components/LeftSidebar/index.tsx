import React, {useState, forwardRef, useImperativeHandle, useRef, useEffect} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MetaDataTree from './components/MetaDataTree';
import HotkeysHelp from './components/HotkeysHelp';
import {Tooltip} from 'acud';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import MetaIcon from '@assets/originSvg/notebook/meta.svg';
import {TooltipType} from '@components/AuthComponents/constants';
import IconSvg from '@components/IconSvg';
import FileList, {FileListRef} from './components/FileList';
const cx = classNames.bind(styles);

interface LeftSidebarRef {
  refreshFileList: () => void;
}

interface LeftSidebarProps {
  onClose: () => void;
  onOpen: () => void;
}

export default forwardRef<LeftSidebarRef, LeftSidebarProps>(function LeftSidebar(props, ref) {
  const authList = useWorkspaceAuth([Privilege.CatalogMenu]);
  const canViewCatalog = authList[Privilege.CatalogMenu];
  const [activeKey, setActiveKey] = useState('');

  // 快捷键帮助弹窗状态
  const [hotkeysHelpVisible, setHotkeysHelpVisible] = useState(false);

  const sidebarItems = [
    {
      key: 'file-list',
      label: '文件列表',
      // disabled: !canViewCatalog,
      icon: <IconSvg type="workarea-folder" size={16} />
    }
    // {
    //   key: 'meta-data',
    //   label: '元数据',
    //   disabled: !canViewCatalog,
    //   icon: <MetaIcon />
    // }
  ];

  function onIconClick(key: string) {
    setActiveKey((pre) => (pre === key ? '' : key));
  }

  const fileListRef = useRef<FileListRef>(null);

  useImperativeHandle(ref, () => ({
    refreshFileList: () => {
      fileListRef.current?.refresh();
    }
  }));

  useEffect(() => {
    if (activeKey) {
      props.onOpen();
    } else {
      props.onClose();
    }
  }, [activeKey]);

  return (
    <div className={cx('left-sidebar')}>
      <div className={cx('sidebar-icons-wrap')}>
        <div className={cx('sidebar-icons')}>
          {sidebarItems.map((item) => {
            return (
              <AuthComponents
                key={item.key}
                isAuth={!item.disabled}
                tooltipType={TooltipType.Function}
                placement="topLeft"
              >
                <Tooltip key={item.key} title={item.label} placement="right">
                  <div
                    className={cx('sidebar-icon', 'mb-[14px]', item.key, {
                      active: activeKey === item.key,
                      disabled: item.disabled
                    })}
                    onClick={() => {
                      if (!item.disabled) {
                        onIconClick(item.key);
                      }
                    }}
                  >
                    {item.icon}
                  </div>
                </Tooltip>
              </AuthComponents>
            );
          })}
        </div>
        <Tooltip title="快捷键" placement="right">
          <div
            className={cx('sidebar-icon', 'sidebar-keymap')}
            onClick={() => setHotkeysHelpVisible(true)}
          ></div>
        </Tooltip>
      </div>
      <div className={cx('sidebar-content', {hide: !activeKey})}>
        <div className={cx('content-inner', {hide: activeKey !== 'file-list'})}>
          <FileList onClose={() => setActiveKey('')} ref={fileListRef} />
        </div>
        {/* <div className={cx('content-inner', {hide: activeKey !== 'meta-data'})}>
          <MetaDataTree onClose={() => setActiveKey('')} />
        </div> */}
      </div>
      <HotkeysHelp visible={hotkeysHelpVisible} onClose={() => setHotkeysHelpVisible(false)} />
    </div>
  );
});
