.left-sidebar {
  display: flex;
  height: 100%;
  .sidebar-icons-wrap {
    flex: none;
    display: flex;
    flex-direction: column;
    width: 40px;
    height: 100%;
    justify-content: space-between;
    border-right: 1px solid #eaeef2;
    align-items: center;
    padding: 8px 0;
    box-sizing: border-box;
  }
  .sidebar-icons {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .sidebar-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
    color: #151b26;
    display: flex;
    align-items: center;
    justify-content: center;

    &.disabled {
      cursor: not-allowed;
      color: #b8babf;
      &:hover {
        background-color: transparent;
      }
    }

    &:hover {
      background-color: rgba(#070c14, 0.06);
    }

    &.active {
      background-color: rgba(#070c14, 0.06);
    }
  }

  // .meta-data {
  //   background-image: url('~@assets/originSvg/notebook/meta.svg?url');
  // }

  .sidebar-keymap {
    background-image: url('~@assets/originSvg/notebook/keymap.svg?url');
  }

  .sidebar-content {
    box-sizing: border-box;
    flex: auto;
    min-width: 0;
    border-right: 1px solid #eaeef2;
    .content-inner {
      height: 100%;
    }
  }
  .hide {
    display: none;
  }
}
