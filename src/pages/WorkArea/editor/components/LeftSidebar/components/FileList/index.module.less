.file-list {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;

    .title {
      color: #151b26;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      .back-btn {
        flex: none;
      }
      .folder-name {
        width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .back-icon {
        transform: rotate(-90deg);
        cursor: pointer;
      }
    }

    .action-btn-wrap {
      display: flex;
      gap: 8px;
      :global(.db-action-btn-wrap) {
        display: flex;
      }
    }
  }

  .list-wrap {
    flex: auto;
    min-height: 0;
    overflow-y: auto;
    padding: 0 16px 12px 16px;
    scrollbar-color: #e5e9ed transparent;

    .list-item {
      display: flex;
      height: 32px;
      cursor: pointer;
      align-items: center;
      border-radius: 6px;
      padding: 0 8px;
      gap: 8px;

      .list-item-right {
        display: none;
      }
      &:hover {
        background-color: #f7f9fc;
        .list-item-right {
          display: inline-block;
          position: relative;
          top: 1px;
          left: 1px;
        }
      }

      &.active {
        background-color: #f2f5fa;
      }

      :global(.acud-dropdown-open) {
        .more-btn {
          display: inline-block;
        }
      }
    }
    .list-item-name {
      flex: auto;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
