import {useState, useContext, useEffect, forwardRef, useImperativeHandle, useMemo} from 'react';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {Button, Loading, Dropdown, Menu} from 'acud';
import IconSvg from '@components/IconSvg';
import IconArrow from '@assets/originSvg/notebook/arrow.svg';
import {getWorkspaceFileList, getFileDetailById, GetWorkspaceFileListResult} from '@api/WorkArea';
import {createFile} from '@api/workEditor';
import {useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import {useAddBtnDisabled, useFileIcon, useFileMenu} from '@hooks/useFile';
import urls from '@utils/urls';
import {useWorkEditor} from '@store/workEditorStatehooks';
import ActionBtn from '@components/ActionBtn';
import {FOLDER_NAME_MAP} from '@pages/WorkArea/config';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import AddIcon from '@assets/svg/add.svg';
import _ from 'lodash';
const cx = classNames.bind(styles);

interface FileListProps {
  onClose?: () => void;
  onFileClick?: (record: GetWorkspaceFileListResult) => void;
}
export interface FileListRef {
  refresh: () => void;
}
export default forwardRef<FileListRef, FileListProps>(function FileList(
  {onClose, onFileClick}: FileListProps,
  ref
) {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState, setUrlState] = useUrlState();

  const {
    tabList,
    activeTabId,
    curFolder,
    curFolderPrivilege,
    pushTab,
    setActiveTabId,
    setCurFolderId,
    setCurFolder
  } = useWorkEditor();

  const {canManage, canModify, canView} = curFolderPrivilege;

  useImperativeHandle(ref, () => ({
    refresh: () => {
      onRefresh();
    }
  }));

  const onRefresh = () => {
    handleFolderChange(curFolder?.id);
  };

  const [fileList, setFileList] = useState([]);

  const {run: getFileList, loading} = useRequest(getWorkspaceFileList, {
    manual: true,
    onSuccess: (res) => {
      const list = _.filter(res.result, (item) => item.type !== 'TRASH');
      setFileList(list);
    }
  });

  const {run: getFolderDetail} = useRequest(getFileDetailById, {
    manual: true,
    onSuccess: (res) => {
      setCurFolder(res.result);
      setCurFolderId(res.result.id);
    }
  });

  function handleFolderChange(folderId: string) {
    if (!folderId) {
      return;
    }
    getFileList({workspaceId, parentId: folderId});
    getFolderDetail({workspaceId, fileId: folderId});
  }

  // 初始加载
  useEffect(() => {
    handleFolderChange(urlState.folderId);
  }, [workspaceId, urlState.folderId]);

  const {renderIcon} = useFileIcon();
  function onItemClick(item) {
    if (item.nodeType === 'FOLDER') {
      handleFolderChange(item.id);
    }

    if (item.nodeType === 'NOTEBOOK') {
      window.open(
        `#${urls.notebook}?workspaceId=${workspaceId}&folderId=${item.parentId}&notebookId=${item.id}`
      );
    }

    if (item.nodeType === 'FILE' && item.name.endsWith('.sql')) {
      const tab = tabList.find((tab) => tab.id === item.id);
      if (tab) {
        setActiveTabId(item.id);
        return;
      }
      pushTab(item);
      setActiveTabId(item.id);
    }

    if (item.nodeType === 'WORKFLOW') {
      window.open(`#${urls.jobDetail}?workspaceId=${workspaceId}&jobId=${item?.linkId}`);
    }
  }

  function onBack() {
    handleFolderChange(curFolder?.parentId);
  }

  const addMenu = (
    <Menu key="addMenu">
      <AuthMenuItem isAuth={canManage} onClick={onAddFile}>
        文件
      </AuthMenuItem>
    </Menu>
  );

  async function onAddFile() {
    const res = await createFile(workspaceId, {
      parentId: curFolder?.id
    });
    if (res.success) {
      pushTab(res.result);
      setActiveTabId(res.result.id);
      onRefresh();
    }
  }

  function handleOpenBlank(record: GetWorkspaceFileListResult) {
    window.open(`#${urls.workAreaEditor}?workspaceId=${workspaceId}&folderId=${record.id}`);
  }

  const {renderFileMenu, renderModals} = useFileMenu({
    workspaceId,
    folderMenuHide: ['rename', 'permission', 'delete'],
    fileMenuHide: ['rename', 'permission', 'delete'],
    onOpenBlank: handleOpenBlank,
    onDeleteSuccess: onRefresh,
    onPermanentDeleteSuccess: onRefresh,
    onCreateFileSuccess: (result) => {
      handleFolderChange(result.parentId);
      pushTab(result);
      setActiveTabId(result.id);
    },
    onCopySuccess: onRefresh,
    onCreateFolderSuccess: onRefresh,
    onImportFileSuccess: onRefresh,
    onMoveSuccess: onRefresh,
    onRenameSuccess: onRefresh
  });

  const renderFolderName = (folder: GetWorkspaceFileListResult) => {
    return FOLDER_NAME_MAP[folder?.type] || folder?.name;
  };

  const renderListFolderName = (folder: GetWorkspaceFileListResult) => {
    const subMap = _.pick(FOLDER_NAME_MAP, ['TRASH', 'USERS', 'SHARED']);
    return subMap[folder?.type] || folder?.name;
  };

  const addFolderDisabled = useAddBtnDisabled(curFolder);

  return (
    <div className={cx('file-list')}>
      <Loading loading={loading}></Loading>
      <header className={cx('header')}>
        <h3 className={cx('title')}>
          <ActionBtn
            className={cx('back-btn')}
            icon={<IconArrow className={cx('back-icon')} />}
            onClick={onBack}
          ></ActionBtn>
          <div className={cx('folder-name')} title={renderFolderName(curFolder)}>
            {renderFolderName(curFolder)}
          </div>
        </h3>
        <div className={cx('action-btn-wrap')}>
          <Dropdown overlay={addMenu} disabled={addFolderDisabled}>
            <ActionBtn disabled={addFolderDisabled} icon={<AddIcon width={16} height={16} />}></ActionBtn>
          </Dropdown>
          <ActionBtn
            icon={<IconSvg type="refresh-2-arrow" color="#151b26" size={16} />}
            onClick={onRefresh}
          ></ActionBtn>
          <ActionBtn icon={<IconSvg type="close" color="#151b26" size={15} />} onClick={onClose}></ActionBtn>
        </div>
      </header>
      <div className={cx('list-wrap')}>
        {fileList.map((item) => {
          return (
            <div
              className={cx('list-item', {active: item.id === activeTabId})}
              key={item.id}
              onClick={() => onItemClick(item)}
            >
              {renderIcon(item)}
              <div className={cx('list-item-name')} title={renderListFolderName(item)}>
                {renderListFolderName(item)}
              </div>
              <div className={cx('list-item-right')} onClick={(e) => e.stopPropagation()}>
                <Dropdown overlay={renderFileMenu(item)}>
                  <ActionBtn
                    type="dark"
                    className={cx('more-btn')}
                    icon={<IconSvg type="more" color="#333" size={14} />}
                  />
                </Dropdown>
              </div>
            </div>
          );
        })}
      </div>
      {renderModals()}
    </div>
  );
});
