import React, {useState, useEffect} from 'react';
import {Modal} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {IS_MAC} from '@utils/platform';

const cx = classNames.bind(styles);

interface HotkeysHelpProps {
  visible: boolean;
  language?: string;
  onClose: () => void;
}
export interface HotkeyConfig {
  key: string; // 快捷键
  description: string; // 快捷键描述
}

const sqlHotkeys = [
  {key: 'CtrlCmd + S', description: '保存'},
  {key: 'CtrlCmd + Enter', description: '运行'},
  {key: 'CtrlCmd + Z', description: '撤销'},
  {key: 'CtrlCmd + Shift + Z', description: '重做'},
  {key: 'CtrlCmd + Shift + F', description: '格式化'}
];

const hotkeyMap = {
  sql: sqlHotkeys
};

const HotkeysHelp: React.FC<HotkeysHelpProps> = ({visible, language = 'sql', onClose}) => {
  const [hotkeys, setHotkeys] = useState<HotkeyConfig[]>([]);

  // 获取当前激活的所有快捷键
  useEffect(() => {
    if (language) {
      setHotkeys(hotkeyMap[language]);
    }
  }, [language]);

  // 格式化快捷键显示
  const formatHotkey = (hotkey: HotkeyConfig): string => {
    const finalKey = hotkey.key.replace('CtrlCmd', IS_MAC ? '⌘' : 'Ctrl');
    return finalKey;
  };

  return (
    <Modal title="快捷键" visible={visible} onCancel={onClose} footer={null} width={328}>
      {hotkeys.map((hotkey) => (
        <div className={cx('hotkey-item')} key={hotkey.key}>
          <span className={cx('hotkey-description')}>{hotkey.description}</span>
          <span className={cx('hotkey-key')}>
            <span className={cx('hotkey-key-text')}>{formatHotkey(hotkey)}</span>
          </span>
        </div>
      ))}
    </Modal>
  );
};

export default HotkeysHelp;
