import React, {
  forwardRef,
  useImperative<PERSON>andle,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {Button, Loading, Search, Tree, Dropdown, Menu, toast} from 'acud';
import IconSvg from '@components/IconSvg';
import {useAsyncEffect, usePrevious, useRequest} from 'ahooks';
import {throttle} from 'lodash';

import * as http from '@api/metaRequest';

import {WorkspaceContext} from '@pages/index';
import {EnumNodeType} from '@pages/MetaData/index';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {CatalogType} from '@api/metaRequest';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {MetaCnNameMap} from '@pages/MetaData/config';

const cx = classNames.bind(styles);

interface DataNode {
  title: string | React.ReactNode;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}

// 请求 catalog list
const httpCatalogFun = async (workspaceId) => {
  // 根据类型排过序 后续开发需注意
  const res = await http.getCatalogList(workspaceId);
  const catalogList = res.result.catalogs;
  return catalogList || [];
};

// 请求 schema list
const httpScheamFun = async (workspaceId, catalogName) => {
  const schemaRes = await http.getSchemaList(workspaceId, {catalogName});
  const schemaList = schemaRes.result.schemas;
  return schemaList || [];
};

// 请求 table list
const httpTableFun = async (workspaceId, catalogName, schemaName) => {
  const tableRes = await http.getTableList(workspaceId, {catalogName, schemaName});
  const tableList = tableRes.result.tables;
  return tableList || [];
};

// 请求 volume list
const httpVolumeFun = async (workspaceId, catalogName, schemaName) => {
  const volumeRes = await http.getVolumeList(workspaceId, {catalogName, schemaName});
  const volumeList = volumeRes.result.volumes;
  return volumeList || [];
};

// 请求 operator list
const httpOperatorFun = async (workspaceId, catalogName, schemaName) => {
  const operatorRes = await http.getOperatorList(workspaceId, {catalogName, schemaName, workspaceId});
  const operatorList = operatorRes.result.operators;
  return operatorList || [];
};

// 更新对应 Tree Node 的 children
const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
  list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });

// Tree节点显示对应图标
export const iconSelectFun = (e) => {
  const str = e.data.key;
  const separator = '#>';
  let count = 0;
  let pos = str.indexOf(separator);
  let type = '';

  while (pos !== -1) {
    count++;
    const i = str.indexOf(separator, pos + separator.length);
    if (count == 2) {
      type = 'meta-' + str.slice(pos + 2, i);
    }
    pos = i;
  }

  const imageName = e.data.isDoris ? 'meta-doris' : 'meta-iceberg';
  // 通过 #> 定位是哪一层级节点
  switch (count) {
    case 0:
      return str === CatalogType.SYSTEM ? (
        <IconSvg type="meta-system" size={16} color="#6c6d70" />
      ) : (
        <IconSvg type={imageName} size={16} color="#6c6d70" />
      );
    case 1:
      return <IconSvg type="meta-schema" size={16} color="#6c6d70" />;
    case 2:
      return null;
    case 3:
      return <IconSvg type={type} size={16} color="#6c6d70" />;
  }
};

function transformToacudTreeData(
  data: http.ISearchMetastoreRes['catalogs'],
  keyword: string,
  renderTitle: (item, key, typeName) => React.ReactNode
): DataNode[] {
  const buildTree = (node, parentKey = '', keyword: string) => {
    if (node.name) {
      const key = parentKey ? `${parentKey}#>${node.name}` : node.name;
      const title = renderTitle(node.name, key, 'Catalog');
      const children = [];

      if (node.schemas) {
        node.schemas.forEach((schema) => {
          const schemaNode = {
            title: renderTitle(schema.name, `${key}#>${schema.name}`, 'Schema'),
            key: `${key}#>${schema.name}`,
            children: []
          };

          if (schema.tables) {
            const tablesNode = {
              title: MetaCnNameMap['Table'],
              key: `${schemaNode.key}#>table`,
              children: schema.tables.map((table) => ({
                title: renderTitle(table.name, `${schemaNode.key}#>table#>${table.name}`, 'Table'),
                key: `${schemaNode.key}#>table#>${table.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(tablesNode);
          }

          if (schema.volumes) {
            const volumesNode = {
              title: MetaCnNameMap['Volume'],
              key: `${schemaNode.key}#>volume`,
              children: schema.volumes.map((volume) => ({
                title: renderTitle(volume.name, `${schemaNode.key}#>volume#>${volume.name}`, 'Volume'),
                key: `${schemaNode.key}#>volume#>${volume.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(volumesNode);
          }

          if (schema.operators) {
            const operatorsNode = {
              title: MetaCnNameMap['Operator'],
              key: `${schemaNode.key}#>operator`,
              children: schema.operators.map((operator) => ({
                title: renderTitle(
                  `${operator.alias}(${operator.name})`,
                  `${schemaNode.key}#>operator#>${operator.name}`,
                  'Operator'
                ),
                key: `${schemaNode.key}#>operator#>${operator.name}`,
                isLeaf: true
              }))
            };
            schemaNode.children.push(operatorsNode);
          }

          children.push(schemaNode);
        });
      }

      return {title, key, children, isDoris: node.type === CatalogType.DORIS};
    }
    return null;
  };

  const acudTreeData = [];
  data.forEach((item) => {
    const treeNode = buildTree(item, undefined, keyword);
    if (treeNode) {
      acudTreeData.push(treeNode);
    }
  });

  return acudTreeData;
}

interface IUrlStateHandler {
  onClose?: () => void;
}
const LeftTree = (props: IUrlStateHandler, ref: any) => {
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [treeData, setTreeData] = useState<any>([]);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  // 搜索值
  const [searchVal, setSearchVal] = useState<string>('');

  // 搜索值变更监听
  const onSearchChange = useCallback((e) => {
    setSearchVal(e.target.value);
  }, []);

  // 初始化 url 中未有 catalog 参数情况
  useAsyncEffect(async () => {
    const catalogList = await httpCatalogFun(workspaceId);
    setTreeData(transApiData2TreeData(catalogList));
  }, []);

  // 树节点搜索
  const {loading: treeLoading, run: onSearch} = useRequest(
    async (value) => {
      if (value) {
        const res = await http.searchMetastore(workspaceId, {filter: value, workspaceId});
        const data = res?.result?.catalogs || [];
        const treeData = transformToacudTreeData(data, value, renderTitle);
        setTreeData(treeData);
      } else {
        onRefresh();
      }
    },
    {
      manual: true
    }
  );

  // trans API 数据为 Tree 数据格式
  const transApiData2TreeData = (arr: http.ICatalogRes['catalogs']) => {
    return arr.map(({name, type}) => ({
      title: renderTitle(name, name, 'Catalog'),
      key: name,
      isDoris: type === CatalogType.DORIS
    }));
  };

  // 树展开
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  function renderTitle(item, key, typeName) {
    const getMenu = () => {
      const [catalog, schema, type, node] = key.split('#>');
      const queryObj = {
        workspaceId,
        catalog,
        schema,
        type,
        node
      };
      const queryString = Object.entries(queryObj).reduce((pre, cur) => {
        return cur[1] ? `${pre}&${cur[0]}=${cur[1]}` : pre;
      }, '');

      const openInMetadata = () => {
        window.open(`#/workspace/meta/data?${queryString}`);
      };

      const copyText = () => {
        // 如果是doris，需要加上反引号
        const curCatalogItem = treeData.find((item) => item.key === catalog);
        let newSchema = schema;
        if (curCatalogItem?.isDoris && schema === 'default') {
          newSchema = `\`${schema}\``;
        }
        const text = [catalog, newSchema, node].filter(Boolean).join('.');

        navigator.clipboard.writeText(text).then(() => {
          toast.success({message: '复制成功', duration: 5});
        });
      };

      return (
        <Menu>
          <Menu.Item onClick={openInMetadata}>在元数据管理中打开</Menu.Item>
          <Menu.Item onClick={copyText}>复制{typeName}名称</Menu.Item>
        </Menu>
      );
    };

    return (
      <>
        <span className={cx('title')}>
          <span className={cx('title-text')}>{item}</span>
        </span>
        <div className={cx('title-right')}>
          <Dropdown overlay={getMenu()}>
            <div className={cx('more')}></div>
          </Dropdown>
        </div>
      </>
    );
  }

  // 异步加载 Tree Data
  const onLoadData = async ({key, children}: any) => {
    if (children) {
      return;
    }
    //根据 key 中 的 #> 分割出来判断第几层, 也用 #> 来拼接 key
    if (key.indexOf('#>') < 0) {
      // 请求 第二层 schema
      const schema = await httpScheamFun(workspaceId, key);
      setTreeData((origin) => {
        return updateTreeData(
          origin,
          key,
          schema.map((item) => {
            return {
              title: renderTitle(item, `${key}#>${item}`, 'Schema'),
              key: `${key}#>${item}`
            };
          })
        );
      });
    } else {
      const [catalogName, schemaName] = key.split('#>');

      // 使用 Promise.allSettled 处理多个异步请求
      const [tableResult, volumeResult, operatorResult] = await Promise.allSettled([
        httpTableFun(workspaceId, catalogName, schemaName),
        httpVolumeFun(workspaceId, catalogName, schemaName),
        httpOperatorFun(workspaceId, catalogName, schemaName)
      ]);

      // 处理每个请求的结果，如果失败则设置为空数组
      const tableList = tableResult.status === 'fulfilled' ? tableResult.value : [];
      const volumeList = volumeResult.status === 'fulfilled' ? volumeResult.value : [];
      const operatorList = operatorResult.status === 'fulfilled' ? operatorResult.value : [];

      setTreeData((origin) => {
        return updateTreeData(origin, key, [
          ...(tableList?.length
            ? [
                {
                  title: `${MetaCnNameMap['Table']}${tableList?.length ? `（${tableList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.TABLE}`,
                  children: tableList.map((item) => {
                    const itemKey = `${key}#>${EnumNodeType.TABLE}#>${item}`;
                    return {
                      title: renderTitle(item, itemKey, 'Table'),
                      key: itemKey,
                      isLeaf: true
                    };
                  })
                }
              ]
            : []),
          ...(volumeList?.length
            ? [
                {
                  title: `${MetaCnNameMap['Volume']}${volumeList?.length ? `（${volumeList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.VOLUME}`,
                  children: volumeList.map((item) => {
                    const itemKey = `${key}#>${EnumNodeType.VOLUME}#>${item}`;
                    return {
                      title: renderTitle(item, itemKey, 'Volume'),
                      key: itemKey,
                      isLeaf: true
                    };
                  })
                }
              ]
            : []),
          ...(operatorList?.length
            ? [
                {
                  title: `${MetaCnNameMap['Operator']}${operatorList?.length ? `（${operatorList.length}）` : ''}`,
                  key: `${key}#>${EnumNodeType.OPERATOR}`,
                  children: operatorList.map((item) => {
                    const itemKey = `${key}#>${EnumNodeType.OPERATOR}#>${item.name}`;
                    return {
                      title: renderTitle(
                        item?.alias ? `${item.alias}(${item.name})` : item.name,
                        itemKey,
                        'Operator'
                      ),
                      key: itemKey,
                      isLeaf: true
                    };
                  })
                }
              ]
            : [])
        ]);
      });
    }
  };

  const requestTreeChildren = async () => {
    const catalogList = await httpCatalogFun(workspaceId);
    setTreeData(transApiData2TreeData(catalogList));
  };

  // 重新加载
  const onRefresh = useCallback(
    throttle(async () => {
      // 先置空
      setTreeData([]);
      if (searchVal) {
        // 存在搜索条件，转给搜索逻辑处理
        onSearch('');
        return;
      }
      await requestTreeChildren();
    }, 1000),
    [searchVal]
  );

  const treeProps = useMemo(
    () => ({
      ...(searchVal ? {} : {expandedKeys: expandedKeys}),
      defaultExpandAll: searchVal ? true : false
    }),
    [searchVal, expandedKeys]
  );

  useImperativeHandle(ref, () => ({
    refresh: onRefresh
  }));

  const onClose = () => {
    props.onClose?.();
  };

  return (
    <div className={cx('meta-data-tree')}>
      <header className={cx('tree-header')}>
        <h3>元数据</h3>
        <div>
          <Button
            type="text"
            icon={<IconSvg type="refresh-2-arrow" color="#151b26" size={16} />}
            onClick={onRefresh}
          ></Button>
          <Button
            type="text"
            icon={<IconSvg type="close" color="#151b26" size={16} />}
            onClick={onClose}
          ></Button>
        </div>
      </header>
      <Search
        className={cx('search')}
        value={searchVal}
        onChange={onSearchChange}
        placeholder="请输入名称搜索"
        allowClear
        onSearch={onSearch}
      />
      <Loading loading={treeLoading}>
        {treeLoading ? null : (
          <Tree
            titleEllipsis
            treeData={treeData}
            // selectedKeys={selectedKeys}
            {...treeProps}
            icon={iconSelectFun}
            // onSelect={onTreeSelect}
            loadData={onLoadData}
            onExpand={onExpand}
          />
        )}
      </Loading>
    </div>
  );
};

export default forwardRef(LeftTree);
