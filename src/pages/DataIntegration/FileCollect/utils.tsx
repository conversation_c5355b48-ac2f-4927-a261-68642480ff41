import {FileSourceType} from '@api/integration/file-type';
import {BatchOperateType, Job, JobConfigStatus, IJobItem} from '@api/integration/type';
import {EditMode, Operation, OperationShowType, SourceFileFilterModStatus, TimeFilterType} from './constants';
import {Modal, toast} from 'acud';
import * as http from '@api/integration';
import urls from '@utils/urls';
import {onStart} from './FileColletRunModal';
import OnOpenModal, {ModalTypeEnum} from '../components/OnOpenModal';
import {JobType} from '@api/integration/type';
import {Privilege} from '@api/permission/type';
import {isArray} from 'lodash';
import WorkflowTable from '../components/WorkflowTable';

// callback 参数有差异，暂时写 any
export type OperationConfig = Record<
  OperationShowType,
  {label: string; callback: any; key: Operation; disabled?: boolean; isAuth?: boolean}[] | null
>;

export type JobItem = Job & {
  operations: OperationConfig;
};

// 发布
export const onPublish = (jobs: IJobItem[], workspaceId: string, callback) => {
  if (!jobs?.length) {
    return;
  }
  Modal.confirm({
    title: '发布任务',
    content: <WorkflowTable jobs={jobs.map((item) => item.jobId)} />,
    width: 600,
    onOk: async () => {
      try {
        const res = await http.publishIntegrationJob(workspaceId, jobs[0].jobId);
        if (res.success) {
          toast.success({message: '发布成功', duration: 5});
          callback();
        }
      } catch {
        console.error('发布任务失败');
      }
    }
  });
};

// 停止
export const onStop = (jobId: string, workspaceId: string, runId: string, callback) => {
  Modal.confirm({
    title: '终止',
    content: `确定要终止吗？`,
    onOk: async () => {
      try {
        const res = await http.stopIntegrationJob(workspaceId, jobId, runId);
        if (res.success) {
          toast.success({message: `终止成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('终止任务失败');
      }
    }
  });
};

// 删除
export const onDelete = (jobs: JobItem[], workspaceId: string, callback) => {
  const jobNames = jobs.map((job) => `"${job.name}"`).join('、');
  const isBatch = jobs.length > 1;
  const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(jobs[0].status);
  Modal.confirm({
    title: `${isBatch ? '批量删除' : '删除任务'}`,
    content: `${jobNames}删除后，任务运行数据将被清空，无法恢复，请确认是否删除。`,
    onOk: async () => {
      try {
        const res = isBatch
          ? await http.batchOperationIntegrationJob(workspaceId, BatchOperateType.Delete, {
              jobIds: jobs.map((item) => item.jobId)
            })
          : await http.deleteIntegrationJob(workspaceId, jobs[0].jobId, {isPublished});
        if (res.success) {
          toast.success({message: `${jobNames}删除成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('删除任务失败');
      }
    }
  });
};

// 创建/编辑
export const onEdit = (
  mode: EditMode,
  id: string,
  type: FileSourceType,
  isPublished: boolean,
  navigate,
  backTo?: string
) => {
  if (mode === EditMode.Create) {
    navigate(`${urls.fileCollectCreate}?mode=${mode}&sourceType=${type}`);
  } else {
    navigate(
      `${urls.fileCollectCreate}?mode=${mode}&jobId=${id}&isPublished=${isPublished}&backTo=${backTo}`
    );
  }
};

// 复制
export const onCopy = async (jobs: JobItem[], workspaceId, callback) => {
  const {name, jobId} = jobs[0];
  try {
    const res = await http.copyIntegrationJob(workspaceId, jobId);
    if (res.success) {
      toast.success({message: `${name}复制成功`, duration: 5});
      callback();
    }
  } catch {
    console.error('复制任务失败');
  }
};

// 前置检查
export const onPreCheck = (jobs: JobItem[], workspaceId: string, callback, navigate) => {
  OnOpenModal(ModalTypeEnum.FilePreCheck, {jobs, workspaceId, callback, navigate});
};

// 根据操作配置列表聚合展示操作项
export const getOperationList = (
  navigate,
  obj?: {privileges: string[]; status: JobConfigStatus}
): OperationConfig => {
  const {status, privileges} = obj || {};
  const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(status);
  const PreCheck = {
    label: '前置检查',
    key: Operation.PreCheck,
    callback: (jobs: IJobItem[], workspaceId: string, callback) =>
      OnOpenModal(ModalTypeEnum.FilePreCheck, {jobs, workspaceId, callback, navigate}),
    isAuth: authCheck(privileges, Privilege.Execute),
    disabled: [JobConfigStatus.Updating, JobConfigStatus.PreCheck, JobConfigStatus.Published].includes(status)
  };
  const Start = {
    label: '运行',
    key: Operation.Start,
    disabled: ![JobConfigStatus.CheckPass, JobConfigStatus.Updating, JobConfigStatus.Published].includes(
      status
    ),
    callback: (jobs: IJobItem[], workspaceId: string, callback) =>
      OnOpenModal(ModalTypeEnum.StartJob, {jobs, workspaceId, callback, navigate, jobType: JobType.File}),
    isAuth: authCheck(privileges, Privilege.Execute)
  };
  const Publish = {
    label: '发布',
    key: Operation.Publish,
    callback: onPublish,
    isAuth: authCheck(privileges, Privilege.Manage),
    disabled: ![JobConfigStatus.CheckPass].includes(status)
  };
  const Edit = {
    label: '编辑',
    key: Operation.Edit,
    isAuth: authCheck(privileges, Privilege.Manage),
    disabled: [JobConfigStatus.Updating, JobConfigStatus.PreCheck].includes(status),
    callback: (jobs: IJobItem[], workspaceId: string, callback, backTo?: string) =>
      onEdit(EditMode.Edit, jobs?.[0]?.jobId, null, isPublished, navigate, backTo)
  };
  const Copy = {
    label: '复制',
    key: Operation.Copy,
    callback: onCopy,
    disabled: false,
    isAuth: authCheck(privileges, Privilege.Manage)
  };
  const Delete = {
    label: '删除',
    key: Operation.Delete,
    callback: onDelete,
    isAuth: authCheck(privileges, Privilege.Manage)
  };
  const initialList = {
    [OperationShowType.List]: [PreCheck, Start, Publish],
    [OperationShowType.Dropdown]: [Edit, Copy, Delete],
    [OperationShowType.DetailDropdown]: [PreCheck, Edit, Copy, Delete],
    [OperationShowType.DetailList]: [Start],
    [OperationShowType.ResultDropdown]: [PreCheck, Edit, Copy, Delete]
  };

  return initialList;
};

export const getMetaUrl = (volumePath, workspaceId) => {
  const pathArr = volumePath?.replace('/Volumes/', '')?.split('/');
  const catalog = pathArr[0];
  const schema = pathArr[1];
  const node = pathArr[2];
  const path = pathArr?.length > 3 ? pathArr.slice(3).join('/') : '';
  return `${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}&schema=${schema}&node=${node}&path=${path}&type=volume`;
};

// 权限检查
export const authCheck = (privileges: string[], auth: string) => {
  if (!privileges || !isArray(privileges)) {
    return false;
  }

  // 管理权限
  if (privileges.includes(Privilege.Manage)) {
    return true;
  }

  // 没有管理权限 但是需要管理权限
  if (auth === Privilege.Manage) {
    return false;
  }

  return privileges.includes(auth);
};

/**
 * 获取时间过滤字段的值
 * @param timeFilterValue 时间过滤值（可能是对象或字符串）
 * @param enableStatus 是否启用时间过滤
 * @returns 处理后的字符串值
 */
export const getTimeFilterValue = (timeFilterValue: any, enableStatus: string): string => {
  // 如果未启用时间过滤，返回空字符串
  if (enableStatus !== SourceFileFilterModStatus.Enabled) {
    return '';
  }

  // 如果值不存在，返回空字符串
  if (!timeFilterValue) {
    return '';
  }

  // 如果是对象类型（新的数据结构）
  if (typeof timeFilterValue === 'object') {
    // 如果类型是无限制，返回空字符串
    if (timeFilterValue.type === TimeFilterType.Unlimited) {
      return '';
    }
    // 否则返回值，如果值不存在则返回空字符串
    return timeFilterValue.value || '';
  }

  // 如果是字符串类型，直接返回
  return timeFilterValue || '';
};

/**
 * 创建时间过滤字段的验证器
 * @param fieldLabel 字段标签，用于错误信息显示
 * @returns 表单验证函数
 */
export const createTimeFilterValidator = (fieldLabel: string) => {
  return (_, value) => {
    // 如果没有值，直接返回错误
    if (!value) {
      return Promise.reject(new Error(`请设置${fieldLabel}`));
    }

    // 如果类型是不限，不需要验证值
    if (value.type === TimeFilterType.Unlimited) {
      return Promise.resolve();
    }

    // 如果类型是固定时间或动态时间，值不能为空
    if (value.type === TimeFilterType.Fixed || value.type === TimeFilterType.Dynamic) {
      if (!value.value || value.value.trim() === '') {
        const errorMsg =
          value.type === TimeFilterType.Fixed ? `请选择${fieldLabel}` : `请输入${fieldLabel}过滤表达式`;
        return Promise.reject(new Error(errorMsg));
      }

      // 动态时间格式校验
      if (value.type === TimeFilterType.Dynamic) {
        const dynamicTimeRegex = /^\$\{logicTime\((yyyy-MM-dd\s+HH:mm:ss)(,[+-]?[0-9]+[yMdhms])?\)\}$/;
        if (!dynamicTimeRegex.test(value.value.trim())) {
          return Promise.reject(new Error(`格式必须为 \${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}`));
        }
      }
    }

    return Promise.resolve();
  };
};
