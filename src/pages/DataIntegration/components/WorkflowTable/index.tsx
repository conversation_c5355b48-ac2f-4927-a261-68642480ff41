import React, {useEffect, useMemo, useState} from 'react';
import {Table, toast} from 'acud';
import moment from 'moment';
import {queryJobsByDiJobIds} from '@api/integration';
import {WorkflowRelation} from '@api/integration/type';

export interface WorkflowTableProps {
  /** 集成作业ID，可以是单个或多个 */
  jobs: string[];
}

/**
 * 工作流表格组件
 *
 * 该组件用于展示影响工作流的范围
 * - 当传入单个作业ID时，展示4列：影响工作流作业名称、描述、创建用户、创建时间
 * - 当传入多个作业ID时，展示5列：任务名称、影响工作流作业名称、描述、创建用户、创建时间（同一任务名称的单元格合并）
 */
export const WorkflowTable: React.FC<WorkflowTableProps> = ({jobs}) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<WorkflowRelation[]>([]);

  // 是否为多个作业
  const isMultipleJobs = useMemo(() => jobs.length > 1, [jobs]);

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      if (jobs.length === 0) {
        setDataSource([]);
        return;
      }

      setLoading(true);
      try {
        const res = await queryJobsByDiJobIds({
          integrationJobIds: jobs
        });

        if (res?.result?.relations) {
          setDataSource(res.result.relations);
        } else {
          setDataSource([]);
        }
      } catch (error) {
        toast.error('查询工作流映射关系失败');
        setDataSource([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [jobs]);

  // 处理单元格合并
  const getMergedDataSource = useMemo(() => {
    if (!isMultipleJobs) {
      return dataSource;
    }

    // 按 integrationJobId 分组统计
    const groupMap = new Map<string, number>();
    dataSource.forEach((item) => {
      const count = groupMap.get(item.integrationJobId) || 0;
      groupMap.set(item.integrationJobId, count + 1);
    });

    // 为每个数据项添加 rowSpan 信息
    const mergedData = dataSource.map((item, index) => {
      const isFirstInGroup = index === 0 || dataSource[index - 1].integrationJobId !== item.integrationJobId;

      return {
        ...item,
        _rowSpan: isFirstInGroup ? groupMap.get(item.integrationJobId) || 1 : 0
      };
    });

    return mergedData;
  }, [dataSource, isMultipleJobs]);

  // 多个作业的列配置
  const columns = useMemo(
    () => [
      ...(jobs.length > 1
        ? [
            {
              title: '任务名称',
              dataIndex: 'integrationJobId',
              width: 150,
              ellipsis: true,
              render: (text: string, record: WorkflowRelation & {_rowSpan?: number}) => {
                // 根据 _rowSpan 返回单元格配置
                const obj = {
                  children: text,
                  props: {
                    rowSpan: record._rowSpan || 0
                  }
                };
                return obj;
              }
            }
          ]
        : []),
      {
        title: '影响工作流作业名称',
        dataIndex: 'jobName',
        width: 150,
        ellipsis: true
      },
      // 暂时隐藏描述字段
      {
        title: '描述',
        dataIndex: 'jobDesc',
        width: 120,
        ellipsis: true,
        render: (text: string) => text || '-'
      },
      {
        title: '创建用户',
        dataIndex: 'createUser',
        width: 120,
        ellipsis: true
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
        render: (text: string) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
      }
    ],
    [jobs.length]
  );

  return (
    <div>
      {dataSource.length ? (
        <div>
          <div className="mb-[16px]">
            发布操作会用该草稿覆盖当前正在被工作流调度使用中的任务，并将任务状态从“更新中”改为“已发布”。此操作将影响以下工作流，并且这些变更将在下一个调度周期生效。
          </div>
          <Table columns={columns} dataSource={getMergedDataSource} loading={loading} pagination={false} />
        </div>
      ) : (
        <div>发布任务之后，可以在工作流中被调度执行。</div>
      )}
    </div>
  );
};

export default WorkflowTable;
