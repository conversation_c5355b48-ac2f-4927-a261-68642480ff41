import {getDatasourceTables} from '@api/integration/batch';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {useRequest} from 'ahooks';
import React, {Key, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import styles from './index.module.less';
import {Loading, Search, Tree} from 'acud';
import {getTreeKey} from '@pages/DataIntegration/utils';
import {Compute} from '@api/integration/type';

interface IntegrationTableTreeProps {
  compute: Compute;
  disabled?: boolean;
  onCheck: (checked: Key[], info: any) => void;
  checkedKeys: Key[];
  sourceConfig: {
    sourceConnectionId?: string;
    sourceDatabase: string;
    sourceType: SourceTypeEnum;
    sourceSchema?: string;
  };
}

// 由于实时/离线的数据库类型不完全一致，故在组件内重写一份集合
export enum SourceTypeEnum {
  MySQL = 'MySQL',
  Oracle = 'Oracle',
  SQLServer = 'SQLServer',
  PostgreSQL = 'PostgreSQL',
  Hana = 'HANA'
}

// 使用sourceSchema作为数据库名称的类型
export const SchemaConnectionType = [
  SourceTypeEnum.PostgreSQL,
  SourceTypeEnum.SQLServer,
  SourceTypeEnum.Hana
];

const IntegrationTableTree: React.FC<IntegrationTableTreeProps> = ({
  checkedKeys,
  disabled,
  sourceConfig,
  compute,
  onCheck
}) => {
  const {workspaceId} = useContext(WorkspaceContext);

  const sourceConnectionId = useMemo(() => sourceConfig?.sourceConnectionId || '', [sourceConfig]);
  const sourceDatabase = useMemo(() => sourceConfig?.sourceDatabase || '', [sourceConfig]);
  const sourceSchema = useMemo(() => sourceConfig?.sourceSchema || '', [sourceConfig]);

  const [searchValue, setSearchValue] = useState<string>('');

  useEffect(() => {
    console.log(compute);
  }, [compute]);

  // 获取表列表
  const {
    run: tablesRun,
    loading: tablesLoading,
    data: tablesList
  } = useRequest(
    () =>
      getDatasourceTables({
        environment: {workspaceId, computeId: compute?.computeId},
        datasourceInfo: {
          connectionId: sourceConnectionId,
          database: sourceDatabase,
          ...(SchemaConnectionType.includes(sourceConfig.sourceType)
            ? {schema: sourceConfig.sourceSchema}
            : {})
        }
      }),
    {
      manual: true
    }
  );

  useEffect(() => {
    if (sourceConnectionId && sourceDatabase && compute?.computeId) {
      tablesRun();
    }
  }, [compute?.computeId, sourceConnectionId, sourceDatabase, tablesRun]);

  // tree 搜索
  const onSearch = useCallback((value) => {
    setSearchValue(value);
  }, []);

  /**
   * 目前仅支持单库下多个表
   */
  const treeData = useMemo(() => {
    return [
      {
        title: sourceConnectionId,
        key: sourceConnectionId,
        children: [
          {
            title: SchemaConnectionType.includes(sourceConfig?.sourceType) ? sourceSchema : sourceDatabase,
            key: getTreeKey([sourceConnectionId, sourceDatabase]),
            children: tablesList?.result?.tables
              .map((item) => ({
                title: item,
                key: getTreeKey([sourceConnectionId, sourceDatabase, item]),
                isLeaf: true
              }))
              .filter((child) => child.title.toLowerCase().includes(searchValue.toLowerCase()))
          }
        ]
      }
    ];
  }, [
    searchValue,
    sourceConfig?.sourceType,
    sourceConnectionId,
    sourceDatabase,
    sourceSchema,
    tablesList?.result?.tables
  ]);

  // 默认展开的tree节点
  const defaultExpandedKeys = useMemo(() => {
    return [sourceConnectionId, getTreeKey([sourceConnectionId, sourceDatabase])];
  }, [sourceConnectionId, sourceDatabase]);

  return (
    <div className={styles['container']}>
      <div className={styles['title']}>可选项</div>
      <Search placeholder="请输入名称搜索" allowClear onSearch={onSearch} className={styles['search']} />
      <Loading loading={tablesLoading}>
        <Tree
          disabled={disabled}
          treeData={treeData}
          checkable
          expandedKeys={defaultExpandedKeys}
          virtual
          height={300}
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          className={styles['tree']}
        />
      </Loading>
    </div>
  );
};

export default IntegrationTableTree;
