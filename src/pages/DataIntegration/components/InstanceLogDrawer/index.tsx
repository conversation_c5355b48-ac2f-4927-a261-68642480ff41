import React, {useCallback, useContext, useEffect, useState} from 'react';
import {useMemoizedFn, useRequest} from 'ahooks';
import {Drawer, Empty, Pagination} from 'acud';
import {WorkspaceContext} from '@pages/index';
import styles from './index.module.less';
import MonacoLog from '@components/MonacoLog';
import {getIntegrationExecutionLog, downloadIntegrationExecutionLog} from '@api/integration';
import useUrlState from '@ahooksjs/use-url-state';
import IconSvg from '@components/IconSvg';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

// 任务参数组件
const InstanceLogDrawer: React.FC<{
  runId: string;
  visible: boolean;
  onClose: () => void;
}> = ({runId, visible, onClose}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: ''});
  // 自动刷新
  const [taskFinished, setTaskFinished] = useState(false);
  // 当前页码
  const [pageNo, setPageNo] = useState<number>(1);

  const {
    data: logData,
    run: runQueryLog,
    loading
  } = useRequest(getIntegrationExecutionLog, {
    manual: true,
    pollingInterval: visible && !taskFinished ? 5000 : undefined, // drawer 打开时每 5s 轮询，taskFinished 为 true 时停止
    pollingWhenHidden: false, // 页面不可见时暂停轮询，但因为 Drawer 组件特性，该配置在 Drawer 组件关闭时并不生效
    onSuccess: (res) => {
      // 如果请求失败，取消刷新
      if (!res.success) {
        setTaskFinished(true);
        return;
      }
      // 如果当前页码为 0，则设置为总页码 最新的数据
      if (pageNo === 0) {
        setPageNo(res.result.totalLogPageCount);
      }
      const result = res.result;
      setTaskFinished(!!result.taskFinished);
    }
  });
  // 查询日志
  const queryLog = useCallback(() => {
    if (!runId) {
      return;
    }
    if (!pageNo) {
      setPageNo(0);
    }
    runQueryLog(workspaceId, urlState.jobId, runId, {
      pageSize: 100,
      pageNo
    });
  }, [runId, runQueryLog, urlState.jobId, workspaceId, pageNo]);

  const changePageNo = useMemoizedFn((pageNo: number) => {
    setPageNo(pageNo);
  });

  const handleDownload = useMemoizedFn(() => {
    if (!runId) {
      console.warn('runId is required for downloading log');
      return;
    }
    // 如果日志为空，不触发下载
    if (!logData?.result?.logContent) {
      return;
    }
    downloadIntegrationExecutionLog(workspaceId, runId);
  });

  useEffect(() => {
    if (visible) {
      queryLog();
    }
  }, [visible, queryLog]);

  const drawerTitle = (
    <div className={styles['log-title']}>
      <div>任务日志</div>
      <div>
        <IconSvg
          type="refresh"
          size={16}
          onClick={queryLog}
          className={cx('mr-[12px]', 'cursor-pointer')}
          color="#151B26"
        />
        <IconSvg
          type="download"
          size={16}
          color="#151B26"
          onClick={handleDownload}
          className={cx('cursor-pointer')}
        />
      </div>
    </div>
  );

  return (
    <Drawer
      onClose={onClose}
      title={drawerTitle}
      placement="right"
      visible={visible}
      className={styles['drawer']}
      width={800}
    >
      <div className={styles['task-log-container']}>
        {logData?.result?.logContent || loading ? (
          <>
            <div className={styles['content']}>
              <MonacoLog value={logData?.result?.logContent || ''} loading={loading} />
            </div>
            <div className={styles['pagination']}>
              <Pagination
                className="flex justify-end"
                size="small"
                pageSize={1}
                total={logData?.result?.totalLogPageCount || 0}
                current={pageNo}
                onChange={changePageNo}
              />
            </div>
          </>
        ) : (
          <Empty description="暂无日志" />
        )}
      </div>
    </Drawer>
  );
};

export default InstanceLogDrawer;
