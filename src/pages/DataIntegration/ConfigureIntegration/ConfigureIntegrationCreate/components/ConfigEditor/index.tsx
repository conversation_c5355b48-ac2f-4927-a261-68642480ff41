/**
 * 配置文件模式-创建-数据流配置
 */

import useUrlState from '@ahooksjs/use-url-state';
import SqlEdit from '@pages/DataIntegration/components/SqlEdit';
import {ConfigTypeOptions} from '@pages/DataIntegration/ConfigureIntegration/constants';
import {Alert, Form, Modal, Radio, Tooltip} from 'acud';
import React, {useCallback, useContext, useMemo, useState} from 'react';
import {CheckStatus, CheckStatusConfig} from '../../constants';
import IconSvg from '@components/IconSvg';

import styles from './index.module.less';
import {WorkspaceContext} from '@pages/index';
import {checkConfigureJobContent, ConfigType} from '@api/integration/configure';
import {formatHocon} from '../../utils';
import {FormInstance} from 'acud/lib/form';
import {format} from 'sql-formatter';

interface ConfigEditorProps {
  checkStatus: CheckStatus;
  form: FormInstance<any>;
  onCheckStatusChange: (status: CheckStatus) => void;
}

const ConfigEditor: React.FC<ConfigEditorProps> = ({checkStatus, form, onCheckStatusChange}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: ''});
  const configContent = Form.useWatch('configContent', form);
  const configFormat = Form.useWatch('configFormat', form);
  const configCategory = Form.useWatch('configCategory', form);

  const [checkMsg, setCheckMsg] = useState<string>('');

  const onCheckContent = useCallback(async () => {
    onCheckStatusChange(CheckStatus.Pending);
    try {
      // 校验接口
      const {result} = await checkConfigureJobContent(workspaceId, {
        configContent,
        configFormat,
        configCategory,
        type: 'config'
      });
      setCheckMsg(result.checkPass ? '' : result.errorInfo);
      onCheckStatusChange(result.checkPass ? CheckStatus.Success : CheckStatus.Failed);
    } catch {
      onCheckStatusChange(CheckStatus.Failed);
    }
  }, [configContent, configFormat, onCheckStatusChange, workspaceId, configCategory]);

  // 格式化
  const onFormat = useCallback(() => {
    const formatMap = {
      [ConfigType.HOCON]: formatHocon,
      [ConfigType.JSON]: (configContent) => {
        try {
          return JSON.stringify(JSON.parse(configContent), null, 2);
        } catch {
          return configContent;
        }
      },
      [ConfigType.SQL]: (configContent) => format(configContent)
    };
    const newContent = formatMap[configFormat](configContent);
    form.setFieldValue('configContent', newContent);
  }, [configContent, configFormat, form]);

  const renderCheckStatus = useMemo(() => {
    const status = (
      <div className={styles[CheckStatusConfig[checkStatus].className]}>
        {CheckStatusConfig[checkStatus].label}
      </div>
    );

    return checkMsg ? <Tooltip title={checkMsg}>{status}</Tooltip> : status;
  }, [checkMsg, checkStatus]);

  return (
    <div>
      <Form.Item name="configFormat" noStyle />
      <Form.Item
        label="格式"
        rules={[{required: true, message: '请选择格式'}]}
        extra="注意：切换格式会清除下方已输入的内容"
        shouldUpdate={(prev, curr) => prev.configFormat !== curr.configFormat}
      >
        {({getFieldValue, setFieldValue}) => (
          <Radio.Group
            optionType="button"
            options={ConfigTypeOptions}
            disabled={urlState?.jobId}
            value={getFieldValue('configFormat')}
            onChange={(e) => {
              const value = e.target.value;
              Modal.confirm({
                title: '切换格式',
                content: `是否切换为 ${value} 格式？切换后，下方已输入内容将被清空，请先保存重要内容。`,

                onOk() {
                  form.setFieldValue('configFormat', value);
                  setFieldValue('configContent', '');
                  onCheckStatusChange(CheckStatus.Draft);
                }
              });
            }}
          />
        )}
      </Form.Item>
      <Form.Item label="配置文件" name="configContent" rules={[{required: true, message: '请填写配置文件'}]}>
        <Alert
          message="任务创建完成后，job.mode (支持batch和streaming) 不可修改"
          type="info"
          showIcon
          className="mb-[16px]"
        />
        <div className={styles['editor']}>
          <div className={styles['editor-header']}>
            {renderCheckStatus}
            <div className={styles['check-button']}>
              <div className={styles['check-button-item']} onClick={onCheckContent}>
                <IconSvg type="check" size={16} className="mr-[4px]" fill="transparent" />
                校验
              </div>
              <div className={styles['check-button-item']} onClick={onFormat}>
                <IconSvg type="format" size={16} className="mr-[4px]" fill="transparent" />
                格式化
              </div>
            </div>
          </div>

          <SqlEdit
            height={406}
            value={configContent}
            onChange={(value) => form.setFieldValue('configContent', value)}
          ></SqlEdit>
        </div>
      </Form.Item>
    </div>
  );
};

export default ConfigEditor;
