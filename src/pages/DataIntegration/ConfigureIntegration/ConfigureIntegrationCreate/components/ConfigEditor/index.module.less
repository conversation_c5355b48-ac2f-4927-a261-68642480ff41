.editor {
  border-radius: 6px;
  border: 1px solid #eaeef2;

  &-header {
    border-bottom: 0.5px solid #eaeef2;
    background: #f7f9fc;
    display: flex;
    height: 40px;
    padding: 8px 16px;
    align-items: center;
  }
}

.check-icon {
  height: 20px;
  padding: 0 8px;
  justify-content: center;
  align-items: center;
  display: flex;
  gap: 10px;
  border-radius: 20px;
}

.check {
  &-success {
    .check-icon();

    border: 0.5px solid #b6f0da;
    background: #e6faf1;
    color: #13B982;
  }

  &-pending {
    .check-icon();

    border: 0.5px solid #c7c9ff;
    background: #f6f6ff;
    color: #352EFF;
  }

  &-failed {
    .check-icon();

    border: 0.5px solid #FFC3BD;
    background: #ffeceb;
    color: #FA423C;
  }

  &-button {
    padding-left: 12px;
    border-left: 1px solid #e4e7eb;
    margin-left: 12px;
    display: flex;
    align-items: center;

    &-item {
      margin-left: 8px;
      height: 20px;
      padding: 0 8px 0 6px;
      display: flex;
      align-items: center;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background-color: #e4e8f0;
      }
    }
  }
}
