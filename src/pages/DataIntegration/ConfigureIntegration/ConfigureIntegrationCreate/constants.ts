import {ConfigCategory} from '@api/integration/configure';

export enum CreateMode {
  Create = 'create',
  Edit = 'edit'
}

export enum CheckStatus {
  Success = 'success',
  Failed = 'failed',
  Pending = 'pending',
  Draft = 'draft'
}

export const ConfigCategoryOptions = [
  {label: '库表采集', value: ConfigCategory.TABLE},
  {label: '文件采集', value: ConfigCategory.FILE}
];

export const CheckStatusConfig = {
  [CheckStatus.Success]: {
    label: '校验通过',
    className: 'check-success'
  },
  [CheckStatus.Failed]: {
    label: '校验不通过',
    className: 'check-failed'
  },
  [CheckStatus.Pending]: {
    label: '校验中',
    className: 'check-pending'
  },
  [CheckStatus.Draft]: {
    label: '待校验',
    className: 'check-pending'
  }
};
