/**
 * 配置文件模式-创建
 */

import IconSvg from '@components/IconSvg';
import {Space, Form, Input, Button, Tooltip, Radio} from 'acud';
import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import {ConfigureJobDetail, JobType} from '@api/integration/type';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import {PageMode} from '@pages/DataIntegration/constants';
import {CreateGroupTitle} from '@pages/DataIntegration/components/CreateGroupTitle';
import ComputeSelect from '@pages/DataIntegration/components/ComputeSelect';
import {RULE} from '@utils/regs';
import ConfigEditor from './components/ConfigEditor';

import styles from './index.module.less';
import {CheckStatus, ConfigCategoryOptions} from './constants';
import {ConfigCategory, ConfigType} from '@api/integration/configure';
import {createIntegrationJob, getJobDetails, updateIntegrationJob} from '@api/integration';
import {WorkspaceContext} from '@pages/index';

const ConfigureIntegrationCreate: React.FC = () => {
  const [form] = Form.useForm();
  const [urlState] = useUrlState({jobId: ''});
  const [detail, setDetail] = useState<ConfigureJobDetail>();
  const [checkStatus, setCheckStatus] = useState<CheckStatus>(CheckStatus.Draft);
  const {workspaceId} = useContext(WorkspaceContext);
  const navigate = useNavigate();

  const getDetail = useCallback(async () => {
    //获取详情
    try {
      const res = await getJobDetails<JobType.Configure>(workspaceId, urlState.jobId);
      if (res.success) {
        setDetail(res.result);
      }
    } catch {
      console.error('获取详情失败');
    }
  }, [urlState.jobId, workspaceId]);

  useEffect(() => {
    if (urlState.jobId) {
      getDetail();
    }
  }, [getDetail, urlState.jobId]);

  useEffect(() => {
    form.setFieldsValue(
      detail || {
        configFormat: ConfigType.HOCON
      }
    );
  }, [detail, form]);

  const backToList = useCallback(() => {
    navigate(`${urls.integration}?mode=${PageMode.Configure}`);
  }, [navigate]);

  const submit = useCallback(async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }
    // 保存
    const params = {...form.getFieldsValue(), type: JobType.Configure};
    try {
      if (urlState.jobId) {
        await updateIntegrationJob<JobType.Configure>(workspaceId, urlState.jobId, params);
      } else {
        await createIntegrationJob<JobType.Configure>(workspaceId, params);
      }
      backToList();
    } catch (err) {
      console.error('更新失败', err);
    }
  }, [backToList, form, urlState.jobId, workspaceId]);

  const saveButton = useMemo(() => {
    const disabled = checkStatus !== CheckStatus.Success;
    const button = (
      <Button onClick={submit} size="large" className={styles['button']} disabled={disabled}>
        保存
      </Button>
    );
    return disabled ? <Tooltip title="校验通过后方可创建完成">{button}</Tooltip> : button;
  }, [checkStatus, submit]);

  return (
    <div className={styles['container']}>
      <Space className={styles['title']} onClick={backToList}>
        <IconSvg type="left" size={16} color="#151B26" />
        {urlState.jobId ? `编辑 ${detail?.name}` : '创建任务'}
      </Space>
      <div className={styles['content']}>
        <Form
          form={form}
          labelWidth="80px"
          labelAlign="left"
          inputMaxWidth={900}
          colon={false}
          layout="vertical"
          initialValues={{configFormat: ConfigType.HOCON, configCategory: ConfigCategory.TABLE}}
        >
          <CreateGroupTitle title="基本配置" />
          <Form.Item
            label="任务名称"
            name="name"
            rules={[
              {required: true, message: '请输入任务名称'},
              {
                pattern: RULE.workflowName,
                message: RULE.workflowNameText
              }
            ]}
            extra={RULE.workflowNameText}
          >
            <Input placeholder="请输入任务名称" limitLength={256} forbidIfLimit style={{width: 500}} />
          </Form.Item>
          <Form.Item
            label="任务类型"
            name="configCategory"
            rules={[{required: true, message: '请选择任务类型'}]}
          >
            <Radio.Group options={ConfigCategoryOptions} />
          </Form.Item>
          <Form.Item label="任务描述" name="description" rules={[{max: 500, message: '不超过500字符'}]}>
            <Input.TextArea
              placeholder="请输入任务描述"
              limitLength={500}
              autoSize={{minRows: 3, maxRows: 6}}
              width={500}
            />
          </Form.Item>
          <CreateGroupTitle title="计算实例配置" />
          <Form.Item label="计算实例" name="compute" rules={[{required: true, message: '请选择计算实例'}]}>
            <ComputeSelect />
          </Form.Item>
          <CreateGroupTitle title="数据流配置" />
          <ConfigEditor
            checkStatus={checkStatus}
            onCheckStatusChange={(value) => setCheckStatus(value)}
            form={form}
          />
        </Form>
      </div>
      <div className={styles['footer']}>
        {saveButton}
        <Button onClick={backToList} size="large" className={styles['button']}>
          取消
        </Button>
      </div>
    </div>
  );
};

export default ConfigureIntegrationCreate;
