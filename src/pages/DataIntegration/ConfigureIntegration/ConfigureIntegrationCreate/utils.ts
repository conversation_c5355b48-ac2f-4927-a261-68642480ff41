// HOCON 自动换行 + 缩进格式化
export function formatHocon(hoconStr) {
  if (!hoconStr) return '';

  // 预处理：去掉多余空格，让花括号、逗号周围有空格方便 split
  hoconStr = hoconStr
    .replace(/[{]/g, ' {\n')
    .replace(/[}]/g, '\n}\n')
    .replace(/,/g, ',\n')
    .replace(/\n\s*\n/g, '\n') // 去除多余空行
    .trim();

  const lines = hoconStr.split('\n');
  let indent = 0;
  const result = [];

  for (const rawLine of lines) {
    const line = rawLine.trim();
    if (!line) continue;

    // 如果行是闭括号，先减缩进
    if (line.startsWith('}')) {
      indent--;
    }

    const space = '  '.repeat(indent);
    result.push(space + line);

    // 如果行以 { 结尾，则下一级缩进
    if (line.endsWith('{')) {
      indent++;
    }
  }

  return result.join('\n');
}
