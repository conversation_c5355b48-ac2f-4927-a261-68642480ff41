.detail-log {
  margin-top: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;

  &-title {
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0;
  }

  &-right {
    display: flex;
    align-items: center;
  }

  &-button {
    border-left: 1px solid #E4E7EB;
    padding-left: 16px;
    margin-left: 16px;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    background-color: #f7f9fc;
    height: 40px;

    button {
      width: 20px;
      height: 20px;
    }
  }

  &_container {
    display: flex;
    flex-direction: column;
    flex: 1;

    .content {
      flex: 1;
      overflow: hidden;
    }
  }

  .log-title {
    display: flex;
    justify-content: space-between;
  }

  &_hasPage-patination {
    border-top: 1px solid rgb(232 233 235 / 100%);
    padding: 8px 0;
  }
}
