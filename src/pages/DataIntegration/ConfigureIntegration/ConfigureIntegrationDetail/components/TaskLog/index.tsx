/**
 * 配置文件模式-详情-任务列表
 */
import React, {useCallback, useContext, useEffect, useState} from 'react';
import {useMemoizedFn, useRequest} from 'ahooks';
import {Empty, Pagination, Space, Button, Divider, Select} from 'acud';
import {WorkspaceContext} from '@pages/index';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MonacoLog from '@components/MonacoLog';
import {getIntegrationExecutionLog, downloadIntegrationExecutionLog} from '@api/integration';
import useUrlState from '@ahooksjs/use-url-state';
import {OutlinedBceDownload, OutlinedRefresh} from 'acud-icon';

const cx = classNames.bind(styles);
const kclass = 'detail-log';

const IntervalOptions = [
  {value: 2000, label: '2s'},
  {value: 5000, label: '5s'},
  {value: 10000, label: '10s'},
  {value: 20000, label: '20s'},
  {value: 30000, label: '30s'}
];

const TaskLog: React.FC = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: '', runId: ''});
  // 自动刷新
  const [taskFinished, setTaskFinished] = useState(false);
  // 当前页码
  const [pageNo, setPageNo] = useState<number>(1);
  // 是否已触发兜底 Flag
  const [isEnsureTriggered, setIsEnsureTriggered] = useState(false);
  const [intervalTime, setIntervalTime] = useState<number>(2000); // 默认 5s 刷新一次

  const {
    data: logData,
    run: runQueryLog,
    loading: logLoading
  } = useRequest(getIntegrationExecutionLog, {
    manual: true,
    pollingInterval: !taskFinished ? intervalTime : undefined, // drawer 打开时每 5s 轮询，taskFinished 为 true 时停止
    pollingWhenHidden: false, // 页面不可见时暂停轮询，但因为 Drawer 组件特性，该配置
    onSuccess: (res) => {
      // 如果请求失败，取消刷新
      if (!res.success) {
        setTaskFinished(true);
        return;
      }
      // 如果当前页码为 0，则设置为总页码 最新的数据
      if (pageNo === 0) {
        setPageNo(res.result.totalLogPageCount);
      }
      const result = res.result;
      // 如果任务已完成，前端停止刷新
      // 但由于任务执行过快的时候，概率出现 taskFinished 为 true 但日志为空，所以前端 10s 后做一次兜底刷新
      if (result.taskFinished && !result.logContent && !isEnsureTriggered) {
        setIsEnsureTriggered(true);
        setTimeout(() => {
          queryLog();
        }, 10000);
      }
      setTaskFinished(!!result.taskFinished);
    }
  });

  // 查询日志
  const queryLog = useCallback(() => {
    if (!pageNo) {
      setPageNo(0);
    }
    runQueryLog(workspaceId, urlState.jobId, urlState.runId, {
      pageSize: 100,
      pageNo
    });
  }, [pageNo, runQueryLog, workspaceId, urlState.jobId, urlState.runId]);

  const changePageNo = useMemoizedFn((pageNo: number) => {
    setPageNo(pageNo);
  });

  // 综合 Loading 状态：runId 获取中 或 日志查询中
  const isLoading = logLoading;

  // 日志可下载的条件：非加载中 && 日志内容不为空 && 有 runId
  const canDownloadLog = !isLoading && logData?.result?.logContent?.length > 0;

  /**
   * 下载日志
   */
  const handleDownload = useMemoizedFn(() => {
    // 如果日志为空，不触发下载
    if (!canDownloadLog) {
      return;
    }
    downloadIntegrationExecutionLog(workspaceId, urlState.runId);
  });

  // 当 runId 获取成功后，自动查询日志
  useEffect(() => {
    queryLog();
  }, [queryLog]);

  const changeFreshTime = useCallback((value) => {
    setIntervalTime(value);
  }, []);

  return (
    <div className={styles[kclass]}>
      <div className={styles[`${kclass}-header`]}>
        <div className={styles[`${kclass}-title`]}>任务日志</div>
        <div className={styles[`${kclass}-right`]}>
          <div className={styles[`${kclass}-refresh`]}>
            <span>自动刷新间隔：</span>
            <Select
              style={{width: 80}}
              showTitle
              onChange={changeFreshTime}
              value={intervalTime}
              options={IntervalOptions}
              size="small"
            />
          </div>
          <Space className={styles[`${kclass}-button`]}>
            <Button type="text" onClick={handleDownload} disabled={!canDownloadLog}>
              <OutlinedBceDownload />
            </Button>
            <Button type="text" onClick={queryLog}>
              <OutlinedRefresh />
            </Button>
          </Space>
        </div>
      </div>
      <Divider style={{padding: 0, margin: 0}}></Divider>
      <div className={cx('flex', 'h-[600px]')}>
        <div className={styles[`${kclass}_container`]}>
          {logData?.result?.logContent || isLoading ? (
            <>
              <div className={styles['content']}>
                <MonacoLog value={logData?.result?.logContent || ''} loading={isLoading} />
              </div>
              <div className={styles[`${kclass}_hasPage-patination`]}>
                <Pagination
                  className="flex justify-end"
                  size="small"
                  pageSize={1}
                  total={logData?.result?.totalLogPageCount || 0}
                  current={pageNo}
                  onChange={changePageNo}
                />
              </div>
            </>
          ) : (
            <Empty description="暂无日志" />
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskLog;
