/**
 * 配置文件模式-详情-任务列表
 */

import IconSvg from '@components/IconSvg';
import {Button, Modal, Space, Table, toast} from 'acud';
import React, {useCallback, useContext, useEffect, useState} from 'react';
import classNames from 'classnames';
import moment from 'moment';
import {ExecutionInfo, ExecutionOrderBy, InstanceStatus, JobDetailRes, JobType} from '@api/integration/type';
import {ConfigMode, restoreConfigureExecution, suspendConfigureExecution} from '@api/integration/configure';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import {Order} from '@api/common';
import {orderMap} from '@pages/DataIntegration/constants';
import {listExecutions, stopIntegrationJob} from '@api/integration';
import {InstanceStatusConfig} from '../../constants';
import styles from './index.module.less';
import {formatSeconds} from '@utils/utils';
import _ from 'lodash';

interface TaskListProps {
  detail: JobDetailRes<JobType.Configure>;
  onDetail: (record: ExecutionInfo) => void;
}

const TaskList: React.FC<TaskListProps> = ({detail, onDetail}) => {
  const [urlState] = useUrlState({jobId: '', runId: ''});
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [total, setTotal] = useState<number>(0);
  const [status, setStatus] = useState<InstanceStatus>();
  const [executionList, setExecutionList] = useState<ExecutionInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: ExecutionOrderBy; order: Order}>();

  // 获取任务列表
  const getJobList = useCallback(async () => {
    const params = {
      status,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      orderBy: sorter?.order ? sorter?.field : undefined,
      order: orderMap[sorter?.order]
    };
    setLoading(true);
    try {
      const res = await listExecutions(workspaceId, urlState.jobId, params);
      const {totalCount, executions} = res.result;
      setTotal(totalCount);
      setExecutionList(
        executions.map((item) => {
          const duration =
            item.duration === -1 && item.executedTime
              ? Math.floor((Date.now() - new Date(item.executedTime).getTime()) / 1000)
              : item.duration;
          return {...item, duration};
        })
      );
    } catch {
      console.error('获取文件采集任务列表失败');
    } finally {
      setLoading(false);
    }
  }, [
    pagination.pageNo,
    pagination.pageSize,
    sorter?.field,
    sorter?.order,
    status,
    urlState.jobId,
    workspaceId
  ]);

  useEffect(() => {
    getJobList();
  }, [getJobList]);

  const onSuspend = useCallback(
    (runId) => () => {
      //暂停
      Modal.confirm({
        title: '暂停',
        content: '请确认是否暂停',
        onOk: async () => {
          const res = await suspendConfigureExecution(workspaceId, runId);
          if (res.success) {
            toast.success({
              message: '暂停成功',
              duration: 5
            });
            getJobList();
          }
        }
      });
    },
    [getJobList, workspaceId]
  );

  const onStop = useCallback(
    (runId) => () => {
      //终止
      Modal.confirm({
        title: '终止',
        content: '请确认是否终止',
        onOk: async () => {
          const res = await stopIntegrationJob(workspaceId, urlState.jobId, runId);
          if (res.success) {
            toast.success({
              message: '终止成功',
              duration: 5
            });
            getJobList();
          }
        }
      });
    },
    [getJobList, urlState.jobId, workspaceId]
  );

  const onResume = useCallback(
    (runId) => () => {
      //恢复
      Modal.confirm({
        title: '恢复',
        content: '请确认是否恢复',
        onOk: async () => {
          const res = await restoreConfigureExecution(workspaceId, runId);
          if (res.success) {
            toast.success({
              message: '恢复成功',
              duration: 5
            });
            getJobList();
          }
        }
      });
    },
    [getJobList, workspaceId]
  );

  const statusConfig = useCallback((configMode) => {
    return configMode === ConfigMode.Batch
      ? _.omit(InstanceStatusConfig, [InstanceStatus.Suspend, InstanceStatus.Suspending])
      : InstanceStatusConfig;
  }, []);

  // 渲染操作项
  const renderOperation = (record) => {
    const isStream = detail?.configMode === ConfigMode.Stream;
    const streamOperation = (
      <div>
        {record.status === InstanceStatus.Running ? (
          <Button type="actiontext" onClick={onSuspend(record.runId)}>
            暂停
          </Button>
        ) : null}
        {record.status === InstanceStatus.Suspend ? (
          <Button type="actiontext" onClick={onResume(record.runId)}>
            恢复
          </Button>
        ) : null}
      </div>
    );
    return (
      <Space className={styles['operation']}>
        <Button type="actiontext" onClick={() => onDetail(record)}>
          详情
        </Button>
        {isStream ? streamOperation : null}
        {record.status === InstanceStatus.Running ? (
          <Button type="actiontext" onClick={onStop(record.runId)}>
            终止
          </Button>
        ) : null}
      </Space>
    );
  };

  const columns = [
    {
      title: '运行记录 ID',
      dataIndex: 'runId'
    },
    {
      title: '业务时间',
      dataIndex: 'scheduleTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '状态',
      dataIndex: 'status',
      filters: Object.keys(statusConfig(detail?.configMode)).map((key) => ({
        text: InstanceStatusConfig[key].label,
        value: key
      })),
      filterMultiple: false,
      render: (key) => {
        const info = InstanceStatusConfig[key];
        return (
          <div className={styles['status-tag']} style={{color: info?.textColor, background: info?.color}}>
            {info?.label}
          </div>
        );
      }
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '运行时长',
      dataIndex: 'duration',
      render: (text) => {
        if (text === -1) {
          return '-';
        }
        return formatSeconds(text);
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      render: (_, record) => renderOperation(record)
    }
  ];

  const onPageChange = (page, pageSize) => {
    setPagination({
      pageNo: page,
      pageSize: pageSize
    });
  };

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSorter({field: sorter?.field, order: sorter?.order});
    setStatus(filters.status?.[0]);
  }, []);

  return (
    <div>
      <div className={classNames('mr-[10px]', 'mb-[12px]', styles['refresh-button'])}>
        <Button onClick={getJobList}>
          <IconSvg type="refresh" size={16} color="#6c6d70" />
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={executionList}
        loading={loading}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`,
          onChange: onPageChange
        }}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default TaskList;
