.info-container {
  background-color: #fff;
  overflow-y: auto;
  margin: 8px;
  border-radius: 6px;
  border: 1px solid #d4d6d9;
  padding: 16px;
  width: 100%;
}

.info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #818999;
  margin-top: 12px;

  &-item {
    &:last-child {
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    &:not(:last-child) {
      padding-right: 12px;
      margin-right: 12px;
      border-right: 1px solid #e4e7eb;
      flex-shrink: 0;
    }
  }
}

.title {
  font-weight: 500;
  height: 30px;
  line-height: 30px;
  font-size: 20px;
  overflow: hidden;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
