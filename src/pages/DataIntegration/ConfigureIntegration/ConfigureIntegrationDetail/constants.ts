import {InstanceStatus} from '@api/integration/type';

export const InstanceStatusConfig = {
  [InstanceStatus.Ready]: {
    label: '待触发',
    color: '#E6F0FF',
    textColor: '#2468F2'
  },
  [InstanceStatus.Running]: {
    label: '运行中',
    color: '#E6F0FF',
    textColor: '#2468F2'
  },
  [InstanceStatus.Failed]: {
    label: '失败',
    color: '#FFE8E6',
    textColor: '#F33E3E'
  },
  [InstanceStatus.Success]: {
    label: '成功',
    color: '#ECFFE6',
    textColor: '#30BF13'
  },
  [InstanceStatus.Stopping]: {
    label: '终止中',
    color: '#FFF4E6',
    textColor: '#FF9326'
  },
  [InstanceStatus.Stop]: {
    label: '已终止',
    color: '#F0F0F1',
    textColor: '#5C5F66'
  },
  [InstanceStatus.Suspend]: {
    label: '暂停',
    color: '#fff4e6',
    textColor: '#FF9326'
  },
  [InstanceStatus.Suspending]: {
    label: '暂停中',
    color: '#fff4e6',
    textColor: '#FF9326'
  }
};
