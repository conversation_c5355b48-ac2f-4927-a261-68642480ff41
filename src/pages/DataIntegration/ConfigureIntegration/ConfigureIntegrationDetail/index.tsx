/**
 * 配置文件模式-详情
 */

import useUrlState from '@ahooksjs/use-url-state';
import {PageMode} from '@pages/DataIntegration/constants';
import urls from '@utils/urls';
import {<PERSON><PERSON>crumb, Button, Dropdown, Link, Loading, Menu, Modal, toast} from 'acud';
import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import TaskList from './components/TaskList';
import TaskLog from './components/TaskLog';

import flags from '@/flags';
import {checkConfigModeDiJob} from '@api/auth';
import {deleteIntegrationJob, getJobDetails} from '@api/integration';
import {ExecutionInfo, JobDetailRes, JobType} from '@api/integration/type';
import IconSvg from '@components/IconSvg';
import {PageNoPermission} from '@components/WithoutPermissionPage/PageNoPermission';
import {WorkspaceContext} from '@pages/index';
import {formatSeconds} from '@utils/utils';
import moment from 'moment';
import {useNavigate} from 'react-router-dom';
import {onStart} from '../components/RunModal';
import {CreateMode} from '../ConfigureIntegrationCreate/constants';
import {InstanceStatusConfig} from './constants';
import styles from './index.module.less';

const ConfigureIntegrationDetail: React.FC = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const navigate = useNavigate();
  const [urlState, setUrlState] = useUrlState({jobId: '', runId: ''});
  const [detail, setDetail] = useState<JobDetailRes<JobType.Configure>>();
  const [executionDetail, setExecutionDetail] = useState<ExecutionInfo>();
  const [isInWhiteList, setIsInWhiteList] = useState<boolean>(false);
  const [whiteListLoading, setWhiteListLoading] = useState<boolean>(true);

  const getDetail = useCallback(async () => {
    //获取详情
    try {
      const res = await getJobDetails<JobType.Configure>(workspaceId, urlState.jobId);
      if (res.success) {
        setDetail(res.result);
      }
    } catch {
      console.error('获取详情失败');
    }
  }, [urlState.jobId, workspaceId]);

  useEffect(() => {
    getDetail();
  }, [getDetail]);

  const renderInfo = useMemo(() => {
    const jobConfig = [
      {
        label: '模式',
        value: detail?.configMode,
        key: 'configMode'
      },
      {
        label: '创建人',
        value: detail?.creatorName,
        key: 'creatorName'
      },
      {
        label: '创建时间',
        value: detail?.createTime,
        key: 'createTime'
      },
      {
        label: '任务描述',
        value: detail?.description || '-',
        key: 'description'
      }
    ];
    const executionConfig = [
      {
        label: '状态',
        value: executionDetail?.status ? InstanceStatusConfig[executionDetail?.status].label : '-',
        key: 'status'
      },
      {
        label: '开始时间',
        value: executionDetail?.scheduleTime
          ? moment(executionDetail?.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
        key: 'scheduleTime'
      },
      {
        label: '结束时间',
        value: executionDetail?.endTime
          ? moment(executionDetail?.endTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
        key: 'endTime'
      },
      {
        label: '运行时长',
        value: executionDetail?.duration === -1 ? '-' : `${formatSeconds(executionDetail?.duration)}s`,
        key: 'duration'
      }
    ];
    const config = urlState.runId ? executionConfig : jobConfig;
    return (
      <div className={styles['info']}>
        {config.map((item) => (
          <div key={item.key} className={styles['info-item']}>
            {item.label}: {item.value}
          </div>
        ))}
      </div>
    );
  }, [
    detail?.configMode,
    detail?.createTime,
    detail?.creatorName,
    detail?.description,
    executionDetail,
    urlState.runId
  ]);

  const runJob = useCallback(() => {
    //运行
    onStart({...detail, jobId: urlState.jobId}, workspaceId, getDetail);
  }, [detail, getDetail, urlState.jobId, workspaceId]);

  const onDelete = useCallback(async () => {
    //删除
    Modal.confirm({
      title: '确认删除任务',
      content: `${detail?.name}删除后，任务运行数据将被清空，无法恢复，请确认是否删除。`,
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        await deleteIntegrationJob(workspaceId, urlState?.jobId);
        toast.success('删除成功');
        navigate(`${urls.integration}?mode=${PageMode.Configure}`);
      }
    });
  }, [urlState?.jobId, detail?.name, navigate, workspaceId]);

  const onEdit = useCallback(() => {
    //编辑
    navigate(
      `${urls.configureIntegrationCreate}?workspaceId=${workspaceId}&mode=${CreateMode.Edit}&jobId=${urlState?.jobId}`
    );
  }, [urlState?.jobId, navigate, workspaceId]);

  // 跳转到详情页
  const onDetail = useCallback(
    (record: ExecutionInfo) => {
      setExecutionDetail(record);
      setUrlState({runId: record.runId});
    },
    [setUrlState]
  );

  const getConfigModeWhiteList = useCallback(async () => {
    // 获取可配置化模式白名单
    setWhiteListLoading(true);
    try {
      const res = await checkConfigModeDiJob();
      if (res.success) {
        const inWhitelist = res.result?.inWhitelist;
        setIsInWhiteList(inWhitelist);
      }
    } catch (err) {
      console.error('获取可配置化模式白名单失败', err);
    } finally {
      setWhiteListLoading(false);
    }
  }, []);

  useEffect(() => {
    const isPrivate = flags?.DatabuilderPrivateSwitch;
    if (isPrivate) {
      const isShowIntegrationConfigure = window.PRIVATE_STATIC?.customOtherConfig?.isShowIntegrationConfigure;
      setIsInWhiteList(isShowIntegrationConfigure);
      setWhiteListLoading(false);
    } else {
      getConfigModeWhiteList();
    }
  }, [getConfigModeWhiteList]);

  return (
    <div className={styles['info-container']}>
      <Loading loading={whiteListLoading} />
      {isInWhiteList ? (
        <div>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link href={`#${urls.integration}?mode=${PageMode.Configure}`}>集成任务-配置文件模式</Link>
            </Breadcrumb.Item>
            {urlState.runId ? (
              <Breadcrumb.Item>
                <Link onClick={() => setUrlState({runId: ''})}>{detail?.name}</Link>
              </Breadcrumb.Item>
            ) : null}
          </Breadcrumb>

          <div className={styles['header']}>
            <div className={styles['title']}>{urlState.runId ? urlState?.runId : detail?.name}</div>
            {urlState.runId ? null : (
              <div className="flex">
                <Button type="primary" onClick={runJob} className="mr-[12px]" disabled={!detail?.canExecute}>
                  运行
                </Button>
                <Dropdown
                  overlay={
                    <Menu>
                      <Menu.Item key="run" onClick={onEdit}>
                        编辑
                      </Menu.Item>
                      <Menu.Item key="delete" onClick={onDelete}>
                        删除
                      </Menu.Item>
                    </Menu>
                  }
                >
                  <Button>
                    <IconSvg type="more" />
                  </Button>
                </Dropdown>
              </div>
            )}
          </div>

          {renderInfo}

          {urlState.runId ? <TaskLog /> : <TaskList detail={detail} onDetail={onDetail} />}
        </div>
      ) : (
        <PageNoPermission content="您的账号未被授权访问配置文件模式" />
      )}
    </div>
  );
};

export default ConfigureIntegrationDetail;
