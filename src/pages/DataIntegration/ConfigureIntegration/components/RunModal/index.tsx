/**
 * 集成可配置作业 - 运行弹窗
 */
import React, {useState, useEffect} from 'react';
import {FileJobRunType, JobDetailRes, JobType} from '@api/integration/type';
import {useMemoizedFn} from 'ahooks';
import {startIntegrationJob} from '@api/integration';
import {Modal, Form, DatePicker, Space, Button, toast} from 'acud';
import ReactDOM from 'react-dom/client';
import locale from 'acud/lib/date-picker/locale/zh_CN';
import moment from 'moment';

const StartModal: React.FC<{
  detail: JobDetailRes<JobType.Configure>;
  workspaceId: string;
  callback: () => void;
  afterClose: () => void;
}> = ({detail, workspaceId, callback, afterClose}) => {
  const defaultDate = moment(new Date());
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();

  // 运行任务 是否查看详情
  const runJob = useMemoizedFn(async () => {
    let scheduleTime = form.getFieldValue('scheduleTime');
    if (!moment.isMoment(scheduleTime)) {
      scheduleTime = moment(scheduleTime || new Date());
    }

    try {
      const result = await startIntegrationJob(workspaceId, detail?.jobId, {
        runtimeArgs: {
          scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss'),
          triggerType: FileJobRunType.Once
        }
      });

      if (result?.success) {
        toast.success({
          message: `${detail.name}运行成功！`,
          duration: 5
        });
        callback();
      }
    } catch {
      console.error('运行任务失败');
    }

    setVisible(false);
  });

  // 底部按钮配置
  const footer = (
    <Space>
      <Button type="default" onClick={() => setVisible(false)}>
        取消
      </Button>
      <Button type="enhance" onClick={runJob}>
        运行
      </Button>
    </Space>
  );
  return (
    <Modal
      visible={visible}
      title="运行"
      width={632}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      <Form className="mt-4">
        <Form.Item
          label="业务时间"
          name="scheduleTime"
          extra="设置的业务时间仅在任务配置中明确包含变量${logicTime}时才生效"
        >
          <DatePicker showTime showNow={true} locale={locale} defaultValue={defaultDate} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default StartModal;

// 运行和批量运行弹窗 - 目前仅支持单个作业
export const onStart = (detail, workspaceId: string, callback) => {
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };
  root.render(
    <StartModal detail={detail} workspaceId={workspaceId} callback={callback} afterClose={afterClose} />
  );
};
