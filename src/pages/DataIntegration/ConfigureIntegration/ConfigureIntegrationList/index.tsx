/**
 * 配置文件模式-列表
 */

import {Order} from '@api/common';
import {ConfigJobItem, ConfigMode, getConfigJobList, JobOrderBy} from '@api/integration/configure';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {orderMap} from '@pages/DataIntegration/constants';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {Button, Search, Space, Table, Tooltip} from 'acud';
import {useRequest} from 'ahooks';
import moment from 'moment';
import React, {useCallback, useContext, useState} from 'react';
import {Link, useNavigate} from 'react-router-dom';
import {CreateMode} from '../ConfigureIntegrationCreate/constants';
import {Modal, toast} from 'acud';

import styles from './index.module.less';
import {ColumnType} from 'acud/lib/table';
import {ConfigModeOptions} from '../constants';
import {onStart} from '../components/RunModal';
import {deleteIntegrationJob} from '@api/integration';

const ConfigureIntegrationList: React.FC = () => {
  const [dataSource, setDataSource] = useState<ConfigJobItem[]>([]);
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // 搜索条件
  const [searchValue, setSearchValue] = useState<string>('');
  // 任务模式筛选
  const [mode, setMode] = useState<ConfigMode[]>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: JobOrderBy | null; order: Order | null}>({
    field: null,
    order: null
  });

  // 拉取实时任务列表
  const {loading, run: getList} = useRequest(
    () =>
      getConfigJobList(workspaceId, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        orderBy: sorter.field,
        order: orderMap[sorter.order],
        mode: mode[0],
        namePattern: searchValue
      }),
    {
      onSuccess: (res) => {
        if (res.success) {
          const {jobs, totalCount} = res.result;
          setDataSource(jobs);
          setTotal(totalCount);
        }
      },
      refreshDeps: [pagination, sorter, searchValue, mode]
    }
  );

  const onDelete = useCallback(
    (job: ConfigJobItem) => {
      // TODO: 删除任务
      Modal.confirm({
        title: '确认删除任务',
        content: `${job.name}删除后，任务运行数据将被清空，无法恢复，请确认是否删除。`,
        okText: '删除',
        cancelText: '取消',
        onOk: async () => {
          await deleteIntegrationJob(workspaceId, job.jobId);
          toast.success('删除成功');
          getList();
        }
      });
    },
    [getList, workspaceId]
  );

  const handleCreate = useCallback(() => {
    navigate(`${urls.configureIntegrationCreate}?workspaceId=${workspaceId}&mode=${CreateMode.Create}`);
  }, [navigate, workspaceId]);

  /**
   * 获取操作
   */
  const getOperations = useCallback(
    (job: ConfigJobItem) => {
      return [
        {
          key: 'run',
          label: '运行',
          callback: () => onStart(job, workspaceId, getList),
          disabled: !job.canExecute,
          disabledTooltip: '仅最新运行实例为已终止或失败时可点击运行'
        },
        {
          key: 'taskList',
          label: '运行记录',
          callback: () =>
            navigate(`${urls.configureIntegrationDetail}?workspaceId=${workspaceId}&jobId=${job.jobId}`)
        },
        {
          key: 'edit',
          label: '编辑',
          callback: () =>
            navigate(
              `${urls.configureIntegrationCreate}?workspaceId=${workspaceId}&mode=${CreateMode.Edit}&jobId=${job.jobId}`
            )
        },
        {
          key: 'delete',
          label: '删除',
          callback: () => onDelete(job)
        }
      ];
    },
    [getList, navigate, onDelete, workspaceId]
  );

  // 渲染操作列
  const renderOperation = useCallback(
    (record: ConfigJobItem) => {
      return (
        <Space className={styles['operation']}>
          {getOperations(record).map((item, index) => {
            const button = (
              <Button type="actiontext" key={index} onClick={item.callback} disabled={item.disabled}>
                {item.label}
              </Button>
            );
            return item.disabled && item.disabledTooltip ? (
              <Tooltip title={item.disabledTooltip} key={index}>
                {button}
              </Tooltip>
            ) : (
              button
            );
          })}
        </Space>
      );
    },
    [getOperations]
  );

  // 表格列配置
  const columns: ColumnType<ConfigJobItem>[] = [
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 220,
      ellipsis: true,
      // 任务名称点击跳转详情页面
      render: (text: string, record: ConfigJobItem) => (
        <Link to={`${urls.configureIntegrationDetail}?workspaceId=${workspaceId}&jobId=${record.jobId}`}>
          <Ellipsis tooltip={text}>{text}</Ellipsis>
        </Link>
      )
    },
    {
      title: '模式',
      dataIndex: 'configMode',
      width: 140,
      filterMultiple: false,
      filters: ConfigModeOptions.map((item) => ({value: item.value, text: item.label})),
      filteredValue: mode
    },
    {
      title: '计算实例',
      dataIndex: 'computeName',
      width: 120,
      render: (text: string) => <>{text ? <Ellipsis tooltip={text}>{text}</Ellipsis> : '-'}</>
    },
    {
      title: '任务描述',
      dataIndex: 'description',
      width: 120,
      ellipsis: true,
      render: (text: string) => <>{text ? <Ellipsis tooltip={text}>{text}</Ellipsis> : '-'}</>
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
      ellipsis: true,
      // 跳转到数据库详情页面
      render: (text: string) => <>{text ? <Ellipsis tooltip={text}>{text}</Ellipsis> : '-'}</>
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
      sorter: true,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 220,
      fixed: 'right',
      render: (_, record: ConfigJobItem) => renderOperation(record)
    }
  ];

  const handleSearch = (value) => {
    setSearchValue(value);
    setPagination((prev) => ({...prev, pageNo: 1}));
  };
  const handleRefresh = () => {
    getList();
  };

  const handleTableChange = (pagination, filters, sorter) => {
    setPagination({...pagination, pageNo: pagination.current, pageSize: pagination.pageSize});
    setSorter({field: sorter.field, order: sorter.order});
    setMode(filters.configMode || []);
  };
  return (
    <div className={styles['container']}>
      <div className={styles['header']}>
        <Search allowClear onSearch={handleSearch} className={styles['search']} />
        <div className={styles['header-right']}>
          <Button onClick={handleRefresh} className="mr-[10px]">
            <IconSvg type="refresh" size={16} color="#6c6d70" />
          </Button>
          <Button
            type="primary"
            onClick={handleCreate}
            className="mr-[10px]"
            icon={<IconSvg type="add" size={16} />}
          >
            创建
          </Button>
        </div>
      </div>
      <Table
        className={styles['cdc-table']}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        scroll={{x: 1200}}
        onChange={handleTableChange}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total: total,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true
        }}
        rowKey="jobId"
      />
    </div>
  );
};

export default ConfigureIntegrationList;
