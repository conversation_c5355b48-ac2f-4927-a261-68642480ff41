/**
 * 数据血缘图谱组件
 * 包括内容：
 *
 * @author: <EMAIL>
 */
import _ from 'lodash';
import React, {useEffect, useState, useCallback, useMemo, useContext, createContext} from 'react';

import {Drawer, Loading, toast} from 'acud';
import IconSvg from '@components/IconSvg';
import {
  ReactFlow,
  Background,
  useNodesState,
  useEdgesState,
  BackgroundVariant,
  useReactFlow,
  MarkerType,
  useStoreApi
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import {LineageDirection} from '@api/lineage/type';
import {WorkspaceContext} from '@pages/index';
import {NodeType, LineageGroupType, lineageGroupTypeMap} from '@pages/Lineage/constants';
import {
  initGraphNodeData,
  collectLineages,
  fetchLineage,
  deDuplicateData,
  getUpperTimestamp,
  fetchColumnLineage,
  collectColumnUpLineage,
  collectColumnDownLineage,
  collectColumnLineageIds,
  getLayoutedElements,
  updateLayoutElements
} from '@pages/Lineage/utils';
import CustomLineageNode from '../components/customNode';
import CustomDashEdge from '../components/customEdge';
import {DetailCacheContext} from '@pages/Lineage/LineageList';
import CustomLineageOperation from '../components/customOperation';
import EdgeDetail from '../components/edgeDetail';
import NodeDetail from '../components/nodeDetail';
import {renderNodeName} from '@pages/Lineage/LineageList/renderNameColumn';
import styles from './index.module.less';
import classNames from 'classnames/bind';
export const GraphContext = createContext(null);
const cx = classNames.bind(styles);

interface Props {
  initialNodes: any;
  initialEdges: any;
}
const expandValueMap = {
  [LineageDirection.Upstream]: 'isExpandedUp',
  [LineageDirection.Downstream]: 'isExpandedDown'
};

const LineageGraph: React.FC<Props> = ({initialNodes, initialEdges}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const store = useStoreApi();
  // 最终展示的节点和边
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  // 所有的节点和边
  const [allNodes, setAllNodes] = useState(initialNodes);
  const [allEdges, setAllEdges] = useState(initialEdges);
  const {fitView} = useReactFlow();
  const [activeEdge, setActiveEdge] = useState(null);
  const [activeNode, setActiveNode] = useState(null);
  const [edgeVisible, setEdgeVisible] = useState(false);
  const [nodeVisible, setNodeVisible] = useState(false);
  const {detailCache, updateDetailCache, timestamp} = useContext(DetailCacheContext);
  useEffect(() => {
    setAllNodes(initialNodes);
    setAllEdges(initialEdges);
  }, [initialNodes, initialEdges]);

  // 处理edges -> 添加选中样式
  const filteredEdges = useMemo(() => {
    return _.map(allEdges, (edge) => ({
      ...edge,
      className: cx('custom-edge', {
        'selected-edge': activeEdge && edge.id === activeEdge?.id
      })
    }));
  }, [allEdges, activeEdge]);
  const handleLayout = useCallback(
    (newNodes, newEdges) => {
      if (newNodes?.length) {
        const layouted = getLayoutedElements(
          initGraphNodeData(newNodes),
          newEdges,
          'LR',
          store.getState()?.height
        );
        // const layouted = updateLayoutElements(nodes, edges, newNodes, newEdges, 'LR');

        setNodes(layouted);
      }
      if (newEdges?.length) {
        setEdges(newEdges);
      }
    },
    [setNodes, setEdges]
  );

  // 始终根据过滤后的节点布局
  const initLayout = () => {
    const nodes = initGraphNodeData(allNodes);
    handleLayout(nodes, filteredEdges);
    handleCenter();
  };

  useEffect(() => {
    initLayout();
  }, []);
  useEffect(() => {
    handleLayout(allNodes, filteredEdges);
  }, [allNodes, handleLayout]);
  const onReLayout = useCallback(() => {
    setAllNodes(initialNodes);
    setAllEdges(initialEdges);
    initLayout();
  }, [initialNodes, initialEdges]);

  const closeEdgeDrawer = () => {
    setEdgeVisible(false);
    setActiveEdge(null);
  };
  const closeNodeDrawer = () => {
    setNodeVisible(false);
    setActiveNode(null);
  };
  // 添加节点及其对应边 - 重新布局
  const onAddNodes = useCallback(
    (newNodes, newEdges) => {
      const updateEdges = [...allEdges, ...newEdges];
      setAllNodes((prevNodes) => {
        const updateNodes = deDuplicateData(prevNodes, newNodes);
        const layouted = getLayoutedElements(updateNodes, updateEdges, 'LR', store.getState()?.height);
        return layouted;
      });
      setAllEdges((preEdges) => deDuplicateData(preEdges, updateEdges));
    },
    [allEdges, setAllEdges, setAllNodes]
  );
  const onAddEdges = useCallback(
    (newEdges) => {
      setAllEdges((preEdges) => deDuplicateData(preEdges, newEdges));
    },
    [setAllEdges]
  );
  const updateGraph = () => {
    updateNodeData([], (node) => node);
  };

  // 边点击事件定义
  const onEdgeClick = (e, edge) => {
    // 字段连线无点击事件 / volume相邻连线禁用点击事件
    if (edge?.edgeType === 'column' || edge.disabled) return;
    closeNodeDrawer();
    if (edge?.id === activeEdge?.id) {
      closeEdgeDrawer();
    } else {
      setActiveEdge(edge);
      setEdgeVisible(true);
    }
  };
  const renderNodeDrawerName = () => {
    const node = activeNode?.data;
    return renderNodeName(node, node?.info, workspaceId, true);
  };
  const [nodeDetail, setDetail] = useState(null);
  const getDetail = async (activeNode) => {
    if (!activeNode) return;
    const node = activeNode?.data;
    const id = activeNode?.id;
    // 1. 查询缓存是否有对应详情
    if (detailCache?.[node?.nodeType]?.has(id)) {
      return detailCache[node.nodeType].get(id);
    }
    // 2. 没有详情数据，调用详情接口查询详情，并存储到map中
    else {
      try {
        const result = await updateDetailCache(node.nodeType, node?.info);
        if (!result) {
          return null;
        }
        return result?.value;
      } catch (e) {
        console.error('获取详情失败', e);
        return null;
      }
    }
  };
  const onNodeClick = async (e, node) => {
    const nodeType = node?.data?.nodeType;
    // 虚拟节点禁用点击事件
    if (nodeType === LineageGroupType.virtualVolume) return;
    closeEdgeDrawer();
    if (node?.id === activeNode?.id) {
      closeNodeDrawer();
    } else {
      const detail = await getDetail(node);
      if (!detail) return;
      setNodeVisible(true);
      setActiveNode(node);
      setDetail(detail);
    }
  };
  useEffect(() => {}, [allNodes, filteredEdges]);
  const updateNodeData = useCallback(
    (ids, updater) => {
      setAllNodes((ns) =>
        ns.map((n) => (_.includes(ids, n.id) ? {...n, data: {...n.data, ...updater(n.data)}} : n))
      );
    },
    [setAllNodes]
  );
  const handleCenter = useCallback(() => {
    // TODO: 待定位为什么直接fitview不能触发重新渲染
    updateGraph();
    fitView({padding: 0.2, duration: 500});
  }, [fitView, updateNodeData]);

  const [selectedColumn, setSelectColumn] = useState<string | null>(null);
  const [columnLoading, setColumnLoading] = useState<boolean>(false);
  // 字段点击事件
  const handleColumnClick = useCallback(
    async (e, column, node, expandCurrent = false) => {
      // id 获取逻辑
      const id = node?.id;
      const config = lineageGroupTypeMap[node?.nodeType as NodeType];
      const upIds = new Set();
      const downIds = new Set();
      const columnKey = `${id}-${column.name}`;
      // 获取上游下游节点id集合
      _.forEach(edges, (edge) => {
        if (id && edge.source === id) {
          downIds.add(edge.target);
        }
        if (id && edge.target === id) {
          upIds.add(edge.source);
        }
      });
      const downstreamIds = Array.from(downIds);
      const upstreamIds = Array.from(upIds);
      e.stopPropagation();

      // 0. 移除之前的字段血缘
      const handle = selectedColumn;
      const newEdges = _.filter(
        edges,
        (edge) => edge.sourceHandle !== handle && edge.targetHandle !== handle
      );

      setAllEdges(newEdges);
      // 取消当前字段选中效果
      if (selectedColumn === columnKey) {
        updateGraph();
        setSelectColumn(null);
        return;
      }

      setColumnLoading(true);

      // 1. 接口获取字段血缘
      const tableFullName = config?.getKey(node?.info);
      // 收集字段血缘的关联节点ids
      const expandedIds = [];
      try {
        const params = {
          workspaceId: workspaceId,
          tableFullName: tableFullName,
          columnName: column.name,
          startTimestamp: getUpperTimestamp(timestamp)
        };
        const edges = [];

        if (upstreamIds.length) {
          const upRes = await fetchColumnLineage(LineageDirection.Upstream, {
            ...params,
            acceptTables: upstreamIds.join(',')
          });
          if (upRes) {
            const {columnLineageInfos, externalColumnLineageInfos} = upRes?.result;
            const targetColumn = {
              targetId: id,
              targetHandle: columnKey
            };
            edges.push(
              ...collectColumnUpLineage(targetColumn, columnLineageInfos, LineageGroupType.tables),
              ...collectColumnUpLineage(
                targetColumn,
                externalColumnLineageInfos,
                LineageGroupType.externalTables
              )
            );
            expandedIds.push(...collectColumnLineageIds(columnLineageInfos, LineageGroupType.tables));
            expandedIds.push(
              ...collectColumnLineageIds(externalColumnLineageInfos, LineageGroupType.externalTables)
            );
          }
        }
        if (downstreamIds.length) {
          const downRes = await fetchColumnLineage(LineageDirection.Downstream, {
            ...params,
            acceptTables: downstreamIds.join(',')
          });

          if (downRes) {
            const {columnLineageInfos, externalColumnLineageInfos} = downRes?.result;
            const sourceColumn = {
              sourceId: id,
              sourceHandle: columnKey
            };
            edges.push(
              ...collectColumnDownLineage(sourceColumn, columnLineageInfos, LineageGroupType.tables),
              ...collectColumnDownLineage(
                sourceColumn,
                externalColumnLineageInfos,
                LineageGroupType.externalTables
              )
            );
            expandedIds.push(...collectColumnLineageIds(columnLineageInfos, LineageGroupType.tables));
            expandedIds.push(
              ...collectColumnLineageIds(externalColumnLineageInfos, LineageGroupType.externalTables)
            );
          }
        }
        if (!edges.length) {
          toast.info({message: '该字段暂无上下游血缘', duration: 2});
        }
        // 2. addEdge，未展开节点对应的连接桩不存在，不会进行连接
        else onAddEdges(edges);
      } catch (e) {
        console.error(`获取${columnKey}字段血缘失败`, e);
      }
      // 3. 给当前字段添加/取消选中效果
      setSelectColumn(columnKey);

      // 4. 展开节点 - TODO:目前每次点击字段会重新布局，之前拖放节点的效果会丢失，后续优化
      // updateNodeData([id], (data) => ({...data, showOtherColums: true}));
      updateNodeData([...expandedIds, ...(expandCurrent ? [id] : [])], (data) => ({
        ...data,
        showOtherColums: true
      }));

      setColumnLoading(false);
    },
    [workspaceId, edges, onAddEdges, timestamp, selectedColumn, setAllEdges, updateNodeData]
  );

  const handleExpand = useCallback(
    async (e, node, direction: LineageDirection) => {
      e.stopPropagation();
      const expandValue = expandValueMap[direction];
      const config = lineageGroupTypeMap[node?.nodeType];
      const currentValue = node?.[expandValue];

      const path = node?.info?.path || '';
      const resourceType = config?.resourceType;
      if (currentValue) return;
      const params = {
        workspaceId,
        resourceId: node?.id,
        resourceType: resourceType,
        path: path,
        startTimestamp: getUpperTimestamp(timestamp),
        node
      };
      try {
        // 接口血缘数据
        const data = await fetchLineage(direction, params);
        updateNodeData([node?.id], (data) => ({...data, [expandValue]: true}));

        const {nodes, edges} = collectLineages(data?.result?.lineages, {}, direction, node?.id);

        onAddNodes(nodes, edges);
      } catch (e) {
        console.error('展开血缘失败', e);
      }
    },
    [workspaceId, updateNodeData, onAddNodes, timestamp]
  );

  // 节点展开上游
  const handleExpandUpstream = useCallback(
    async (e, node) => {
      handleExpand(e, node, LineageDirection.Upstream);
    },
    [handleExpand]
  );
  // 节点展开下游
  const handleExpandDownstream = useCallback(
    async (e, node) => {
      handleExpand(e, node, LineageDirection.Downstream);
    },
    [handleExpand]
  );
  const nodeTypes = {
    custom: CustomLineageNode
  };
  const edgeTypes = {
    dash: CustomDashEdge
  };

  return (
    <GraphContext.Provider
      value={{
        handleExpandUpstream,
        handleExpandDownstream,
        handleColumnClick,
        updateNodeData,
        selectedColumn,
        activeNode
      }}
    >
      <div style={{width: '100%', height: '100%', position: 'relative'}}>
        <Loading loading={columnLoading} />
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onEdgeClick={onEdgeClick}
          onNodeClick={onNodeClick}
          minZoom={0.1} // 最小缩放
          maxZoom={2} // 最大缩放
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          proOptions={{hideAttribution: true}} // 隐藏右下角水印
          defaultEdgeOptions={{
            markerEnd: {
              type: MarkerType.Arrow,
              width: 10,
              height: 10
            }
          }}
          fitView
        >
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          <CustomLineageOperation initLayout={onReLayout} handleCenter={handleCenter} />
        </ReactFlow>
        <Drawer
          className={styles['node-detail-drawer']}
          title={activeNode ? renderNodeDrawerName() : '血缘链路'}
          placement="right"
          width={500}
          visible={edgeVisible || nodeVisible}
          mask={false}
          maskClosable={false}
          closeIcon={<IconSvg type="lineage-back" />}
          onClose={() => {
            closeEdgeDrawer();
            closeNodeDrawer();
            setActiveEdge(null);
            setActiveNode(null);
          }}
        >
          {edgeVisible && <EdgeDetail edge={activeEdge} nodes={nodes} />}
          {nodeVisible && (
            <NodeDetail
              node={{...activeNode?.data, id: activeNode?.id}}
              nodeDetail={nodeDetail}
              handleColumnClick={handleColumnClick}
              onClose={closeNodeDrawer}
            />
          )}
        </Drawer>
      </div>
    </GraphContext.Provider>
  );
};
export default LineageGraph;
