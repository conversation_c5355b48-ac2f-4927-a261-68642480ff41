.opr-container {
  position: absolute;
  left: calc(~'50% - 40px');
  bottom: 24px;
  background-color: #fff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  z-index: 9999;
  gap: 8px;
  .item-split {
    width: 1px;
    height: 16px;
    background: #edeef5;
  }
  .btn-icon {
    cursor: pointer;
    svg {
      fill: none;
    }
  }
}
.custom-edge {
  &:hover {
    :global(.react-flow__edge-path) {
      stroke: #5c61ff;
    }
  }
}
:global {
  .react-flow__edges .selected .react-flow__edge-path {
    stroke: #2127d9 !important;
    stroke-width: 2;
  }
}
.node-detail-drawer {
  :global {
    .acudicon-outlined-left {
      display: none;
    }

    .acud-drawer-header {
      display: flex;
      align-items: center;
      .acudicon svg {
        fill: none;
      }
    }
    .acud-drawer-title {
      margin-right: 4px;
    }
    .acud-drawer-body {
      padding: 16px;
    }
  }
}
