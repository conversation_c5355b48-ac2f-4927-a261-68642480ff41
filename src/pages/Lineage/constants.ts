import {
  LineageDirection,
  TableInfo,
  VolumeInfo,
  ExternalTableInfo,
  ExternalFileInfo,
  WorkflowInfo,
  IntegrationJobInfo,
  NotebookInfo,
  AssetType,
  ExternalAssetType
} from '@api/lineage/type';
import {getTableDetail, getVolumeDetail} from '@api/metaRequest';
import {queryConnectionDetail} from '@api/connection';
import {detailJob} from '@api/job';
import {queryIntegrationJobExecutionDetail} from '@api/integration';
import {
  renderTableName,
  renderExternalName,
  renderTaskName,
  renderIntegrationName
} from './LineageList/renderNameColumn';
import {fetchExternalDetail} from '@pages/Lineage/utils';
import {getNotebookDetail} from '@api/WorkArea';
// 列表页 - 血缘方向映射
export const directionMap = {
  [LineageDirection.Upstream]: {text: '上游', icon: 'lineage-upstream'},
  [LineageDirection.Downstream]: {text: '下游', icon: 'lineage-downstream'}
};
// 列表页 - 数据血缘节点类型
export enum LineageGroupType {
  tables = 'tableInfo',
  volumes = 'volumeInfo',
  externalTables = 'externalTableInfo',
  externalFiles = 'externalFileInfo',
  workflows = 'workflowInfos',
  jobs = 'integrationJobInfos',
  notebooks = 'notebookInfos',
  virtualVolume = 'virtualVolume'
}
// 列表页 - 数据血缘节点类型映射
export const lineageGroupTypeMap = {
  [LineageGroupType.tables]: {
    text: '数据表',
    typeText: '数据表',
    icon: 'lineage-table',
    resourceType: AssetType.TABLE,
    getKey: (node) => `${node.catalogName}.${node.schemaName}.${node.tableName}`,
    getName: (node) => `${node.catalogName}.${node.schemaName}.${node.tableName}`,
    renderNameColumn: (name, node, workspaceId, detail) =>
      renderTableName(name, node, 'table', workspaceId, detail),
    getDetail: async (node, workspaceId) =>
      await getTableDetail(workspaceId, `${node.catalogName}.${node.schemaName}.${node.tableName}`, true)
  },
  [LineageGroupType.virtualVolume]: {
    text: '数据卷',
    typeText: '数据卷',
    icon: 'lineage-volume',
    resourceType: AssetType.VOLOME,
    getKey: (node) => node.path,
    getName: (node) => `${node.catalogName}.${node.schemaName}.${node.volumeName}`,
    renderNameColumn: (name, node, workspaceId, detail) =>
      renderTableName(name, node, 'volume', workspaceId, detail),
    getDetail: async (node, workspaceId) =>
      await getVolumeDetail(workspaceId, `${node.catalogName}.${node.schemaName}.${node.volumeName}`, true)
  },
  [LineageGroupType.volumes]: {
    text: '数据路径',
    typeText: '数据路径',
    icon: 'lineage-volume',
    resourceType: AssetType.VOLOME,
    getKey: (node) => node.path,
    getName: (node) => `${node.catalogName}.${node.schemaName}.${node.volumeName}`,
    renderNameColumn: (name, node, workspaceId, detail) =>
      renderTableName(name, node, 'volume', workspaceId, detail),
    getDetail: async (node, workspaceId) =>
      await getVolumeDetail(workspaceId, `${node.catalogName}.${node.schemaName}.${node.volumeName}`, true)
  },
  [LineageGroupType.externalTables]: {
    text: '数据表',
    typeText: '数据表',
    icon: 'lineage-path',
    resourceType: ExternalAssetType.EXTERNAL_TABLE,
    getKey: (node) => `${node.datasourceName}@${node.schemaName}.${node.tableName}`,
    getName: (node) => `${node.schemaName}.${node.tableName}`,
    renderNameColumn: (name, node, workspaceId, detail) =>
      renderExternalName(name, node, workspaceId, detail),
    getDetail: async (node, workspaceId) => {
      const detail = await fetchExternalDetail(node, workspaceId);
      const connectionDetail = await queryConnectionDetail(workspaceId, node?.datasourceName, true);
      return detail
        ? {
            success: detail.success,
            result: {
              ...detail.result,
              connectionType: connectionDetail?.result?.type,
              createdBy: connectionDetail?.result?.createdBy
            }
          }
        : null;
    }
  },
  [LineageGroupType.externalFiles]: {
    text: '数据路径',
    typeText: '数据路径',
    icon: 'lineage-path',
    resourceType: ExternalAssetType.EXTERNAL_PATH,
    getKey: (node) => `${node.path}/${node.datasourceName}`,
    getName: (node) => node.path,
    renderNameColumn: (name, node, workspaceId, detail) => renderExternalName(name, node, workspaceId),
    getDetail: async (node, workspaceId) => {
      const connectionDetail = await queryConnectionDetail(workspaceId, node?.datasourceName, true);
      return {
        success: connectionDetail?.success,
        result: {
          ...node,
          connectionType: connectionDetail?.result?.type,
          createdBy: connectionDetail?.result?.createdBy
        }
      };
    }
  },
  [LineageGroupType.workflows]: {
    text: '工作流',
    typeText: '工作流',
    icon: 'lineage-workflow',
    getKey: (node) => `${node.workspaceId}.${node.workflowJobId}`,
    // 需要通过详情获取名称，getName返回null便于判断，下面同理
    getName: (node) => null,
    renderNameColumn: (name, node, detail) => renderTaskName(name, node, 'workflow'),
    getDetail: async (node, _) => await detailJob(node.workspaceId, node.workflowJobId, null, true)
  },
  [LineageGroupType.jobs]: {
    text: '数据集成',
    typeText: '数据集成',
    icon: 'lineage-jobs',
    getKey: (node) => `${node.workspaceId}.${node.integrationJobId}`,
    getName: (node) => null,
    renderNameColumn: (name, node, detail) => renderIntegrationName(name, node),
    getDetail: async (node, _) =>
      await queryIntegrationJobExecutionDetail(node.workspaceId, node?.integrationRunId, true)
  },
  [LineageGroupType.notebooks]: {
    text: 'Notebook',
    typeText: 'Notebook',
    icon: 'lineage-notebook',
    getKey: (node) => `${node.workspaceId}.${node.notebookId}`,
    getName: (node) => null,
    renderNameColumn: (name, node) => renderTaskName(name, node, 'notebook'),
    getDetail: async (node, _) => await getNotebookDetail(node?.workspaceId, node?.notebookId, true)
  }
};
// 血缘列表页 - 类型选择器选项
export const typeOptions = [
  {
    text: 'Notebook',
    value: LineageGroupType.notebooks,
    label: 'Notebook'
  },
  {
    text: '工作流',
    value: LineageGroupType.workflows,
    label: '工作流'
  },
  {
    text: '数据集成',
    value: LineageGroupType.jobs,
    label: '数据集成'
  },
  {
    text: '数据表',
    value: LineageGroupType.tables,
    label: '数据表'
  },
  {
    text: '数据路径',
    value: LineageGroupType.volumes,
    label: '数据路径'
  }
];
interface WithDirection<T> extends T {
  direction: LineageDirection;
  type: LineageGroupType;
}
// 血缘列表接口返回数据存储格式
export interface LineageGroup {
  [LineageGroupType.tables]: WithDirection<TableInfo>[];
  [LineageGroupType.volumes]: WithDirection<VolumeInfo>[];
  [LineageGroupType.externalTables]: WithDirection<ExternalTableInfo>[];
  [LineageGroupType.externalFiles]: WithDirection<ExternalFileInfo>[];
  [LineageGroupType.workflows]: WithDirection<WorkflowInfo>[];
  [LineageGroupType.jobs]: WithDirection<IntegrationJobInfo>[];
  [LineageGroupType.notebooks]: WithDirection<NotebookInfo>[];
}
// 缓存详情的 Map（按类型分类）
export interface DetailCache {
  [LineageGroupType.tables]: Map<string, any>; // key = catalog.schema.table
  [LineageGroupType.volumes]: Map<string, any>; // key = catalog.schema.volume
  [LineageGroupType.workflows]: Map<string, any>; // key = workspaceId.workflowJobId
  [LineageGroupType.jobs]: Map<string, any>; // key =  workspaceId.integrationJobId
  [LineageGroupType.notebooks]: Map<string, any>; // key =  workspaceId.notebookId
  [LineageGroupType.externalTables]: Map<string, any>;
  [LineageGroupType.externalFiles]: Map<string, any>;
}

export type NodeType =
  | LineageGroupType.tables
  | LineageGroupType.volumes
  | LineageGroupType.externalTables
  | LineageGroupType.externalFiles
  | LineageGroupType.virtualVolume;

type InfoType = TableInfo | VolumeInfo | ExternalTableInfo | ExternalFileInfo;
// 图谱页 - node 节点数据类型 - 详情由节点组件处理查询map和请求详情接口等逻辑，不存储在节点node内
export interface NodeDataType {
  isRoot?: boolean;
  expanded: boolean;
  nodeType: NodeType;
  info: InfoType;
}
// 图谱页 - edge 边数据类型
export interface EdgeDataType {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  workflowInfos?: WorkflowInfo[];
  integrationJobInfos?: IntegrationJobInfo[];
  notebookInfos?: NotebookInfo[];
}

export enum timestampValues {
  oneWeek = '1week',
  oneMonth = '1month',
  threeMonths = '3months',
  sixMonths = '6months',
  oneYear = '1year'
}
export const timestampOptions = [
  {label: '最近1周', value: timestampValues.oneWeek},
  {label: '最近1个月', value: timestampValues.oneMonth},
  {label: '最近3个月', value: timestampValues.threeMonths},
  {label: '最近6个月', value: timestampValues.sixMonths},
  {label: '最近1年', value: timestampValues.oneYear}
];
export const nameFieldMap = {
  [LineageGroupType.notebooks]: 'name',
  [LineageGroupType.workflows]: 'name',
  [LineageGroupType.jobs]: 'jobName'
};
export const creatorFieldMap = {
  // 元数据接口返回为userId，需要使用userList进行映射
  [LineageGroupType.tables]: 'createdBy',
  [LineageGroupType.volumes]: 'createdBy',
  [LineageGroupType.externalTables]: 'createdBy',
  [LineageGroupType.externalFiles]: 'createdBy',
  [LineageGroupType.workflows]: 'createUser',
  [LineageGroupType.jobs]: 'creatorName',
  [LineageGroupType.notebooks]: 'creator'
};
