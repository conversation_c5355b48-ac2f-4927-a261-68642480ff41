import _ from 'lodash';
import dagre from 'dagre';
import {
  LineageGroupType,
  LineageGroup,
  lineageGroupTypeMap,
  timestampValues,
  creatorFieldMap,
  nameFieldMap
} from './constants';
import {searchTableSchema} from '@api/metaRequest';
import {queryIntegrationJobExecutionDetail} from '@api/integration';
import {
  LineageDirection,
  LineageInfo,
  ColumnLineageInfo,
  ExternalColumnLineageInfo,
  AssetType,
  ExternalAssetType
} from '@api/lineage/type';
import {JobType} from '@api/integration/type';
import {queryLineage, queryColumnLineage, queryExternalLineage} from '@api/lineage';
import urls from '@utils/urls';
import {renderNoDetail} from '@pages/Lineage/LineageList/renderNameColumn';

// 从血缘信息中提取节点和边数据 -> push到传入的nodes和edges
export const collectLineages = (
  lineages: LineageInfo[],
  group: LineageGroup,
  direction: LineageDirection,
  targetNodeId: string
) => {
  const nodes = [];
  const edges = [];
  const nodeKeys = [
    LineageGroupType.externalFiles,
    LineageGroupType.externalTables,
    LineageGroupType.tables,
    LineageGroupType.volumes
  ];
  const edgeKeys = [LineageGroupType.workflows, LineageGroupType.jobs, LineageGroupType.notebooks];
  _.forEach(lineages, (item) => {
    let nodeId = null;
    const isUpstream = direction === LineageDirection.Upstream;
    let node = {};
    let edge = {};
    let groupKey = null;
    let groupItem = {};
    _.forEach(nodeKeys, (key) => {
      const config = lineageGroupTypeMap[key];
      const value = item?.[key];
      if (value) {
        nodeId = config?.getKey(value);
        groupKey = key;
        groupItem = {...value, direction, type: key};
        node = {
          id: nodeId,
          type: 'custom',
          data: {nodeType: key, info: {...value, type: key}}
        };
      }
    });
    _.forEach(edgeKeys, (key) => {
      const value = item?.[key];
      if (key === LineageGroupType.jobs && value?.length) {
        node.data = {
          ...node.data,
          integrationRunId: value[0]?.integrationRunId
        };
        groupItem = {
          ...groupItem,
          integrationRunId: value[0]?.integrationRunId
        };
      }
      if (value?.length) {
        group?.[key]?.push(...value.map((n) => ({...n, direction, type: key})));
        if (!edge[key]) {
          edge[key] = [];
        }
        edge[key].push(...value);
        edge = {
          ...edge,
          id: isUpstream ? `${nodeId}-${targetNodeId}` : `${targetNodeId}-${nodeId}`,
          source: isUpstream ? nodeId : targetNodeId,
          target: isUpstream ? targetNodeId : nodeId,
          sourceHandle: isUpstream ? nodeId : targetNodeId,
          targetHandle: isUpstream ? targetNodeId : nodeId
        };
      }
    });
    group?.[groupKey]?.push(groupItem);
    nodes.push(node);
    edges.push(edge);
  });
  return {nodes, edges, group};
};
// 根据字段上游血缘信息提取边数据
export const collectColumnUpLineage = (
  targetColumn,
  lineages: ExternalColumnLineageInfo[] | ColumnLineageInfo[],
  type: LineageGroupType.externalTables | LineageGroupType.tables
) => {
  const getKey = lineageGroupTypeMap[type].getKey;
  return _.map(lineages, (lineage) => {
    return {
      edgeType: 'column',
      source: getKey(lineage),
      sourceHandle: `${getKey(lineage)}-${lineage?.columnName}`,
      target: targetColumn.targetId,
      targetHandle: targetColumn.targetHandle,
      id: `${getKey(lineage)}-${lineage?.columnName}-${targetColumn.targetHandle}`
    };
  });
};
// 根据字段下游血缘信息提取边数据
export const collectColumnDownLineage = (
  sourceColumn,
  lineages: ExternalColumnLineageInfo[] | ColumnLineageInfo[],
  type: LineageGroupType.externalTables | LineageGroupType.tables
) => {
  const getKey = lineageGroupTypeMap[type].getKey;
  return _.map(lineages, (lineage) => {
    return {
      edgeType: 'column',
      source: sourceColumn.sourceId,
      sourceHandle: sourceColumn.sourceHandle,
      target: getKey(lineage),
      targetHandle: `${getKey(lineage)}-${lineage?.columnName}`,
      id: `${sourceColumn.sourceHandle}-${getKey(lineage)}-${lineage?.columnName}`
    };
  });
};
// 收集具有血缘的节点id
export const collectColumnLineageIds = (
  lineages: ExternalColumnLineageInfo[] | ColumnLineageInfo[],
  type: LineageGroupType.externalTables | LineageGroupType.tables
) => {
  const getKey = lineageGroupTypeMap[type].getKey;
  return _.map(lineages, (lineage) => {
    return getKey(lineage);
  });
};
// 处理Volume相邻边的禁用状态
export const handleDidabledEdges = (edges, nodeId) => {
  return _.map(edges, (edge) => ({
    ...edge,
    type: 'dash',
    disabled: edge?.source === nodeId || edge?.target === nodeId
  }));
};

// 初始化图谱需要的节点相关数据
export const initGraphNodeData = (nodes) => {
  return _.map(nodes, (node) => {
    const isRoot = node?.data?.info?.isRoot;
    return {
      ...node,
      data: {
        ...node.data,
        // 上游是否展开
        isExpandedUp: node?.data?.isExpandedUp || isRoot ? true : false,
        // 下游是否展开
        isExpandedDown: node?.data?.isExpandedDown || isRoot ? true : false,
        // 是否展示其他字段
        showOtherColums: node?.data?.showOtherColums ?? false
      }
    };
  });
};

export const getUpperTimestamp = (timestamp: timestampValues) => {
  const upperTimeMap = {
    [timestampValues.oneWeek]: Date.now() - 7 * 24 * 60 * 60 * 1000,
    [timestampValues.oneMonth]: Date.now() - 30 * 24 * 60 * 60 * 1000,
    [timestampValues.threeMonths]: Date.now() - 90 * 24 * 60 * 60 * 1000,
    [timestampValues.sixMonths]: Date.now() - 180 * 24 * 60 * 60 * 1000,
    [timestampValues.oneYear]: Date.now() - 365 * 24 * 60 * 60 * 1000
  };
  return upperTimeMap?.[timestamp] || null;
};
export const getUserNameByValue = (type, detail, userList) => {
  const creatorField = creatorFieldMap[type];
  const creator = detail?.[creatorField];
  // 需要使用userList进行用户名映射的类型
  const metaDataType = [
    LineageGroupType.tables,
    LineageGroupType.volumes,
    LineageGroupType.externalTables,
    LineageGroupType.externalFiles
  ];
  if (!_.includes(metaDataType, type)) return creator;
  if (_.isEmpty(userList)) return creator;
  const user = _.find(userList, {id: creator});
  return user?.name || creator;
};
// 批量获得节点详情
export const getDetailByGroup = async (lineageGroup, detailCache, updateDetailCache, userList = []) => {
  const detailKey = Object.values(LineageGroupType);
  const entries = await Promise.all(
    _.map(detailKey, async (groupKey) => {
      const groupConfig = lineageGroupTypeMap[groupKey];
      const groupItems = deDuplicateLieageData(lineageGroup?.[groupKey]);
      const processedItems = await Promise.all(
        _.map(groupItems, async (item) => {
          try {
            const keyValue = groupConfig?.getKey(item);
            const fullname = groupConfig?.getName(item);
            const nameField = nameFieldMap?.[groupKey];
            const cachedDetail = detailCache?.[groupKey]?.get(keyValue);

            if (cachedDetail) {
              return {
                ...item,
                detail: cachedDetail,
                fullname: fullname || cachedDetail?.[nameField],
                creator: getUserNameByValue(groupKey, cachedDetail, userList)
              };
            }
            const result = await updateDetailCache(groupKey, item);

            if (result && result?.value) {
              const detail = result?.value;
              return {
                ...item,
                detail,
                fullname: fullname || result?.value?.[nameField],
                creator: getUserNameByValue(groupKey, result?.value, userList),
                // Notebook详情接口会返回status字段，此处用于标记是否为删除状态
                isDeleted: detail?.status === 'DELETED' || detail?.isDeleted || !detail
              };
            }
            // 兜底处理，如果没有查到详情，则直接返回空字符串 -> fullname为空会渲染为删除状态
            return {
              ...item,
              detail: null,
              fullname: fullname || '',
              isDeleted: true,
              creator: ''
            };
          } catch (error) {
            console.error(`处理节点失败:`, error);
          }
        })
      );
      return [groupKey, processedItems];
    })
  );
  return Object.fromEntries(entries);
};
// 初始化血缘节点数据缓存
export const initDetailCache = () => {
  return {
    [LineageGroupType.tables]: new Map(),
    [LineageGroupType.volumes]: new Map(),
    [LineageGroupType.externalTables]: new Map(),
    [LineageGroupType.externalFiles]: new Map(),
    [LineageGroupType.workflows]: new Map(),
    [LineageGroupType.jobs]: new Map(),
    [LineageGroupType.notebooks]: new Map()
  };
};
// 初始化血缘分组数据结构
export const initLineageGroup = () => {
  return {
    [LineageGroupType.tables]: [],
    [LineageGroupType.volumes]: [],
    [LineageGroupType.externalTables]: [],
    [LineageGroupType.externalFiles]: [],
    [LineageGroupType.workflows]: [],
    [LineageGroupType.jobs]: [],
    [LineageGroupType.notebooks]: []
  };
};

// 获取上游/下游血缘数据请求
export const fetchLineage = async (
  direction: LineageDirection,
  params: {
    workspaceId: string;
    resourceId: string;
    resourceType: AssetType | ExternalAssetType;
    path?: string;
    startTimestamp?: number;
    node?: any;
  }
) => {
  const {workspaceId, resourceId, resourceType, path, startTimestamp, node} = params;
  const fullName = lineageGroupTypeMap[node?.nodeType]?.getName(node?.info) || resourceId;
  if (Object.values(AssetType).includes(resourceType)) {
    return await queryLineage(workspaceId, {
      fullName: fullName,
      type: resourceType,
      path: path || '',
      lineageDirection: direction,
      startTimestamp,
      pageNo: 1,
      pageSize: 1000
    });
  }
  if (Object.values(ExternalAssetType).includes(resourceType)) {
    const schemaName = node?.info?.schemaName;
    const tableName = node?.info?.tableName;
    return await queryExternalLineage(workspaceId, {
      dataSourceName: node?.info?.datasourceName,
      type: resourceType,
      path: path || '',
      tableName: schemaName ? `${schemaName}.${tableName}` : '',
      lineageDirection: direction,
      startTimestamp,
      pageNo: 1,
      pageSize: 1000
    });
  }
};
// 获取字段血缘数据请求
export const fetchColumnLineage = async (
  direction: LineageDirection,
  params: {
    workspaceId: string;
    tableFullName: string;
    columnName: string;
    acceptTables: string;
    startTimestamp: number;
  }
) => {
  const {workspaceId, tableFullName, columnName, acceptTables, startTimestamp} = params;
  return await queryColumnLineage(workspaceId, {
    tableFullName,
    columnName,
    acceptTables,
    lineageDirection: direction,
    startTimestamp
  });
};

// 渲染血缘列表的名称列
export const renderTableName = (fullname, record, workspaceId) => {
  const nodeType = record?.type;
  const renderNameColumn = lineageGroupTypeMap[nodeType]?.renderNameColumn;
  const detail = record?.detail;
  if (!renderNameColumn) return fullname;
  if (!detail || detail?.isDeleted || detail?.status === 'DELETED') return renderNoDetail(record, fullname);
  return renderNameColumn(fullname, record, workspaceId, detail);
};

// 外部表获取详情逻辑
export const fetchExternalDetail = async (node, workspaceId) => {
  // 1. 获取集成任务计算实例
  const runId = node?.integrationRunId;
  if (!runId) return null;
  try {
    const jobDetail = await queryIntegrationJobExecutionDetail(workspaceId, runId, true);
    if (!jobDetail.success) return null;
    // 2、携带计算实例请求外部表详情接口
    const result = await searchTableSchema({
      environment: {
        workspaceId: workspaceId,
        computeId: jobDetail.result?.computeId
      },
      datasourceInfo: {
        connectionId: node?.datasourceName,
        database: node?.schemaName,
        table: node?.tableName
      },
      silent: true
    });
    // 3. 如果计算实例无权限/无数据，则兜底处理 -> 处理为空状态 detail: null
    if (!result.success) {
      return null;
    }
    return result;
  } catch (e) {
    console.error('获取外部表详情失败: ', e);
    return null;
  }
};

const urlsMap = {
  [JobType.File]: urls.fileCollectDetail,
  [JobType.Batch]: urls.offlineCollectDetail,
  [JobType.CDC]: urls.cdcCollectDetail
};
// 获得任务型节点链接
export const getTaskLink = (type, node) => {
  switch (type) {
    case LineageGroupType.workflows:
      return `${urls.jobDetail}?workspaceId=${node?.workspaceId}&jobId=${node?.workflowJobId}`;
    case LineageGroupType.notebooks:
      return `${urls.notebook}?workspaceId=${node?.workspaceId}&notebookId=${node?.notebookId}`;
    case LineageGroupType.jobs:
      return `${urlsMap[node?.detail?.jobType]}?workspaceId=${node?.workspaceId}&jobId=${node?.integrationJobId}&isPublished=${node?.detail?.isPublished}`;
    default:
      return '';
  }
};
// 获得节点跳转链接
export const getNodeLink = (type, node, workspaceId) => {
  const isVolume = type === LineageGroupType.volumes;
  const baseUrl = `${urls.metaData}?workspaceId=${workspaceId}&catalog=${node?.catalogName}&schema=${node?.schemaName}&type=${isVolume ? 'volume' : 'table'}`;
  switch (type) {
    case LineageGroupType.externalTables:
    case LineageGroupType.externalFiles:
      return `${urls.connectionDetail}?workspaceId=${workspaceId}&name=${node?.datasourceName}`;
    case LineageGroupType.tables:
      return `${baseUrl}&node=${node?.tableName}`;
    case LineageGroupType.volumes:
      return `${baseUrl}&node=${node?.volumeName}`;
    default:
      return '';
  }
};
// 处理添加节点或边时的重复问题，新增节点会替换原节点，新增边会替换原边
export const deDuplicateData = (oldList, newList) => {
  const listMap = new Map(oldList.map((item) => [item.id, item]));
  _.forEach(newList, (item) => {
    if (!listMap.has(item.id)) {
      listMap.set(item.id, item);
    }
  });
  return Array.from(listMap.values());
};
// 对血缘列表数据进行过滤，相同节点保留时间戳大的那一个
export const deDuplicateLieageData = (list) => {
  const listMap = new Map();
  const taskKeys = [LineageGroupType.workflows, LineageGroupType.jobs, LineageGroupType.notebooks];
  _.forEach(list, (item) => {
    const id = lineageGroupTypeMap[item?.type]?.getKey(item);
    const key = _.includes(taskKeys, item?.type) ? `${id}-${item.direction}` : id;
    const oldItem = listMap.get(key);
    if (!oldItem || (oldItem && item?.lineageTimestamp > oldItem?.lineageTimestamp)) {
      listMap.set(key, item);
    }
  });
  return Array.from(listMap.values());
};
export const getVolumePath = (catalog: string, schema: string, volume: string) => {
  return `/Volumes/${catalog}/${schema}/${volume}/`;
};

export const getDetailFromCache = (info, cache) => {
  const key = lineageGroupTypeMap[info?.type]?.getKey(info);
  const detail = cache?.[info?.type]?.get(key);
  return detail;
};
const nodeWidth = 320;
const nodeHeight = 300;
// 使用dagre进行布局
export function getLayoutedElements(nodes, edges, direction: 'TB' | 'LR' = 'LR', graphHeight): Node[] {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph?.setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({
    rankdir: direction,
    nodesep: 50,
    ranksep: 50 // 不同层之间的间距
  });

  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, {width: nodeWidth, height: node?.data?.height ?? nodeHeight});
  });

  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  dagre.layout(dagreGraph);
  // const regularLayout = nodes.map((node) => {
  //   const {x, y} = dagreGraph.node(node.id);
  //   return {
  //     ...node,
  //     position: {x, y},
  //     targetPosition: 'left',
  //     sourcePosition: 'right'
  //   };
  // });
  const layoutedNodes = nodes.map((node) => {
    const dagreNode = dagreGraph.node(node.id);

    const newX = dagreNode?.x - dagreNode?.width / 2;
    const newY = dagreNode?.y - dagreNode?.height / 2;

    return {
      ...node,
      position: {x: newX, y: newY},
      targetPosition: 'left',
      sourcePosition: 'right'
    };
  });

  return layoutedNodes;
}
// 增量更新节点和边的位置
export function updateLayoutElements(
  oldNodes = [],
  oldEdges = [],
  newNodes,
  newEdges,
  direction: 'TB' | 'LR' = 'LR'
) {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({rankdir: direction});
  // 1. 旧节点固定位置不更新
  oldNodes.forEach((node) => {
    dagreGraph.setNode(node.id, {
      width: nodeWidth,
      height: node?.data?.height || nodeHeight,
      x: node.position.x,
      y: node.position.y
    });
  });
  // 2. 加入旧边
  oldEdges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });
  // 3. 新增节点处理
  newNodes.forEach((node) => {
    dagreGraph.setNode(node.id, {
      width: nodeWidth,
      height: node?.data?.height || nodeHeight
    });
  });

  // 4. 新增边
  newEdges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });
  // 5. 布局计算
  dagre.layout(dagreGraph);

  // 6. 只更新新增节点的 position
  return newNodes.map((node) => {
    const {x, y} = dagreGraph.node(node.id);
    return {
      ...node,
      position: {x, y},
      targetPosition: 'left',
      sourcePosition: 'right'
    };
  });
}
