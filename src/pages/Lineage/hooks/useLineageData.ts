/**
 * 封装数据血缘接口的处理逻辑
 * 以供数据血缘列表页和节点详情的血缘列表页使用
 * @author: <EMAIL>
 */
import {useContext, useState, useEffect} from 'react';

import {WorkspaceContext} from '@pages/index';
import {LineageDirection, AssetType} from '@api/lineage/type';
import {LineageGroup, LineageGroupType, timestampValues} from '@pages/Lineage/constants';

import {ResourceType} from '@api/permission/type';
import {
  initLineageGroup,
  collectLineages,
  fetchLineage,
  getUpperTimestamp,
  handleDidabledEdges
} from '@pages/Lineage/utils';
interface LineageDataProps {
  resourceId: string;
  resourceType: AssetType;
  assetType: LineageGroupType;
  // 当前节点Id
  nodeId: string;
  direction?: LineageDirection | 'both'; // 请求方向
  // 路径筛选
  pathValue?: string;
  // 时间戳
  timestamp?: timestampValues;
}
interface UseLineageDataReturn {
  loading: boolean;
  nodes: any[];
  edges: any[];
  lineageGroup: LineageGroup;
  // 路径筛选选项
  pathLoading?: boolean;
  getLineageData: () => Promise<any>;
}

export default function useLineageData({
  resourceId,
  resourceType,
  assetType,
  direction = 'both',
  pathValue = '',
  timestamp,
  nodeId
}: LineageDataProps): UseLineageDataReturn {
  const {workspaceId} = useContext(WorkspaceContext);
  const [lineageGroup, setLineageGroup] = useState(initLineageGroup());
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [loading, setLoading] = useState(false);

  const getLineageData = async () => {
    setLoading(true);
    const isVolume = resourceType === AssetType.VOLOME;
    const params = {
      workspaceId,
      resourceId,

      resourceType,
      path: isVolume ? pathValue : '',
      startTimestamp: getUpperTimestamp(timestamp)
    };
    try {
      const [upStream, downStream] =
        direction === 'both'
          ? await Promise.all([
              fetchLineage(LineageDirection.Upstream, params),
              fetchLineage(LineageDirection.Downstream, params)
            ])
          : await Promise.all([fetchLineage(direction, params)]);
      let group = initLineageGroup();
      const nodes = [];
      const edges = [];
      if (upStream) {
        const {
          nodes: updatedNodes,
          edges: updatedEdges,
          group: updatedGroup
        } = collectLineages(upStream.result.lineages, group, LineageDirection.Upstream, nodeId);
        nodes.push(...updatedNodes);
        edges.push(...updatedEdges);
        group = updatedGroup;
      }
      if (downStream) {
        const {
          nodes: updatedNodes,
          edges: updatedEdges,
          group: updatedGroup
        } = collectLineages(downStream.result.lineages, group, LineageDirection.Downstream, nodeId);
        nodes.push(...updatedNodes);
        edges.push(...updatedEdges);
        group = updatedGroup;
      }
      setLineageGroup(group);
      setNodes(nodes);
      // 虚拟节点禁用邻边
      if (assetType === LineageGroupType.virtualVolume) {
        setEdges(handleDidabledEdges(edges, nodeId));
      } else {
        setEdges(edges);
      }
    } catch (e) {
      console.error(`拉取${resourceId}血缘详情数据错误`, e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLineageData();
  }, [resourceId, resourceType, pathValue, timestamp]);

  return {
    loading,
    nodes,
    edges,
    lineageGroup,
    getLineageData
  };
}
