/**
 * 数据血缘列表页面 - 关联对象名称渲染
 * 主要包括内容：
 * 1. 7类节点的名称部分渲染 （数据表、数据卷、外部表、外部文件、工作流、集成作业、Notebook）
 * 2. 已删除节点 - Tooltip提示 （详情拉不到会渲染为已删除）
 * 3. 无权限节点 - Tooltip提示 （本期不做，跳转到对应页面无权限会报错）
 * @param name 节点展示名称
 * @param node 血缘节点
 * @param type 节点类型(可选， volumn / table)
 * <AUTHOR>
 */
import React, {ReactNode} from 'react';
import {Link} from 'react-router-dom';
import urls from '@utils/urls';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import IconSvg from '@components/IconSvg';
import {LineageGroupType, lineageGroupTypeMap} from '@pages/Lineage/constants';
import RenderDetasourceLink from '@pages/Lineage/components/customNode/renderDatasourceLink';
import CopyToClipboard from '@components/CopyToClipboard';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {getTaskLink, getNodeLink} from '@pages/Lineage/utils';
import {Tooltip} from 'acud';
const cx = classNames.bind(styles);

/**
 * 渲染内部数据表/数据卷的名称
 */
export function renderTableName(name: string, node: any, type: string, workspaceId: string, detail) {
  const isVolume = type === 'volume';
  const baseUrl = `${urls.metaData}?workspaceId=${workspaceId}&catalog=${node?.catalogName}&schema=${node?.schemaName}&type=${type}`;
  const aimUrl = `${baseUrl}&node=${isVolume ? node?.volumeName : node?.tableName}`;
  const name_content = (
    <>
      <TextEllipsis tooltip={name}>
        <Link to={aimUrl} target="_blank">
          {name}
        </Link>
      </TextEllipsis>

      {detail?.dataSourceFormat || ''}
      {isVolume && node?.path && (
        <TextEllipsis tooltip={node?.path}>
          <CopyToClipboard text={node?.path} iconClassName={styles['lineage-copy']}></CopyToClipboard>
        </TextEllipsis>
      )}
    </>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
}

/**
 * 渲染外部数据源路径/表的名称
 */
export function renderExternalName(name: string, node: any, workspaceId: string, detail) {
  const name_content = (
    <>
      <TextEllipsis tooltip={name}>{name}</TextEllipsis>
      {RenderDetasourceLink({info: node, workspaceId, detail})}
    </>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
}

/**
 * 任务类型节点名称渲染（工作流、Notebook）
 */
export function renderTaskName(name: string, node: any, type: string) {
  const urlsMap = {
    workflow: `${urls.jobDetail}?jobId=${node?.workflowJobId}`,
    notebook: `${urls.notebook}?notebookId=${node?.notebookId}`
  };
  const name_content = (
    <>
      <TextEllipsis tooltip={name}>
        <Link to={`${urlsMap[type]}&workspaceId=${node.workspaceId}`} target="_blank">
          {name}
        </Link>
      </TextEllipsis>
    </>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
}

/**
 * 集成作业名称渲染
 */
export function renderIntegrationName(name: string, node: any) {
  const name_content = (
    <>
      <TextEllipsis tooltip={name}>
        <Link to={getTaskLink(node?.type, node)} target="_blank">
          {name}
        </Link>
      </TextEllipsis>
    </>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
}
// 已被删除对象
export const renderNoDetail = (node, name) => {
  const config = lineageGroupTypeMap?.[node?.type];
  const fullname = config?.getName(node) || name;
  const name_content = (
    <Tooltip title="该对象已被删除">
      <div className={cx(styles['description-text'], styles['disabled-text'])}>
        {fullname || '被删除对象'}
      </div>
    </Tooltip>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
};
// 图谱节点名称渲染
export const renderNodeName = (node, detail, workspaceId: string, isDrawer = false) => {
  const name = lineageGroupTypeMap?.[node?.nodeType]?.getName(node?.info) || '';
  const link = getNodeLink(node?.nodeType, node?.info, workspaceId);
  const linkKeys = [LineageGroupType.tables, LineageGroupType.volumes];
  const nameLink = linkKeys.includes(node?.nodeType) ? (
    <Link to={link} target="_blank">
      {name}
    </Link>
  ) : (
    name
  );
  const name_content = (
    <>
      <TextEllipsis width={380} tooltip={name}>
        {nameLink}
      </TextEllipsis>
      {isDrawer || <div className={cx(styles['description-text'])}>{detail?.creator || ''}</div>}
    </>
  );
  return <RenderNameWithIcon node={node}>{name_content}</RenderNameWithIcon>;
};
interface RenderNameWithIconProps {
  node: any;
  children?: ReactNode;
}
export const RenderNameWithIcon: React.FC<RenderNameWithIconProps> = ({node, children}) => {
  const type = node?.type || node?.nodeType;
  return (
    <div className={cx(styles['table-name-container'])}>
      <div className={styles['icon']}>
        <IconSvg type={lineageGroupTypeMap?.[type]?.icon} size={20} />
      </div>
      <div className={cx(styles['name-container'])}>{children}</div>
    </div>
  );
};
