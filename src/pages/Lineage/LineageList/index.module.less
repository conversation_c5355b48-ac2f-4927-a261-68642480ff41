// 关联对象列样式
.table-name-container {
  width: 100%;
  display: flex;
  align-items: center;
  .icon {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    background-color: #f2f5fa;
    border: 0.5px solid #e4e7eb;
    border-radius: 6px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    svg {
      fill: none;
    }
  }
  .name-container {
    display: flex;
    flex-direction: column;
    width: calc(~'100% - 50px');
  }
  .disabled-text {
    cursor: not-allowed;
    color: #a2abbd;
    width: calc(~'100% - 50px');
  }
  .description-text {
    font-size: 12px;
  }
}

.lineage-list {
  .filter-container {
    display: flex;
    justify-content: space-between;
    .left-container,
    .right-container {
      display: flex;
      gap: 8px;
    }
  }
  .lineage-table {
    .name-container {
      max-width: 300px;
    }
  }
}
.full-btn {
  position: absolute;
  right: 35px;
}
.lineage-copy {
  margin-left: 8px;
  cursor: pointer;
  &:hover {
    :global(svg) {
      color: #2468f2;
    }
  }
}

.lineage-direction {
  width: 55px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #495366;
  background-color: #f7f9fc;
  border-radius: 20px;
  gap: 4px;
}
.drawer-container {
  :global {
    .acud-drawer-body {
      padding: 0;
    }

    .acud-drawer-header {
      display: flex;
    }
    .acud-drawer-title {
      display: flex;
      align-items: center;
    }
  }
}
.graph-filter-container {
  position: absolute;
  top: 70px;
  left: 16px;
  z-index: 9999;
}
