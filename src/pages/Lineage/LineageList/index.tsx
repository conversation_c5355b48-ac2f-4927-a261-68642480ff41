/**
 * 数据血缘列表页 （数据卷/数据表）
 * 主体逻辑：
 * 1. 请求血缘数据时调用两次接口（上游和下游），接口返回全量数据，前端进行分类处理展示列表
 * 2. 前端实现筛选、过滤和分页
 * 3. 对于任务型血缘（集成/工作流/notebook），
 *    由前端请求详情页获取展示名（设置缓存，避免反复请求）
 * <AUTHOR>
 */
import _ from 'lodash';
import React, {useCallback, useState, useMemo, createContext, useEffect, useContext} from 'react';
import {WorkspaceContext} from '@pages/index';
import {Table, Search, Button, Select, Drawer} from 'acud';
import {enableMapSet, produce} from 'immer';
enableMapSet();
import RefreshButton from '@components/RefreshButton';
import {formatTime} from '@utils/moment';
import {queryVolumeLineagePath} from '@api/lineage';
import {AssetType, LineageDirection, LineageInfo} from '@api/lineage/type';
import LineageGraph from '@pages/Lineage/LineageGraph';
import IconSvg from '@components/IconSvg';
import {ReactFlowProvider} from '@xyflow/react';
import {
  directionMap,
  LineageGroupType,
  lineageGroupTypeMap,
  DetailCache,
  typeOptions,
  timestampOptions,
  timestampValues
} from '../constants';
import useLineageData from '../hooks/useLineageData';
import EmptyList from '../components/emptyList';
import {
  getUpperTimestamp,
  getDetailByGroup,
  initDetailCache,
  deDuplicateLieageData,
  getVolumePath,
  renderTableName
} from '../utils';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);

interface LineageListProps {
  // 数据表/卷
  resourceType: AssetType;
  // 数据表/卷的完整名称 形如catalog.schema.table
  resourceId: string;
  // 数据表/卷的名称
  name: string;
  // 用户列表
  userList: any;
}
export const DetailCacheContext = createContext(null);

const LineageList: React.FC<LineageListProps> = (props: LineageListProps) => {
  const {resourceType, resourceId, name, userList} = props;
  const [catalog, schema, table] = resourceId.split('.');
  const {workspaceId} = useContext(WorkspaceContext);
  // 是否为数据卷
  const isVolume = useMemo(() => {
    return resourceType === AssetType.VOLOME;
  }, [resourceType]);
  const [showDrawer, setShowDrawer] = useState(false);
  // 筛选条件 - 搜索关键字（name）
  const [searchValue, setSearchValue] = useState('');
  // 筛选条件 - 数据卷路径
  const [pathValue, setPathValue] = useState('');
  // 筛选条件 - 血缘方向
  const [direction, setDirection] = useState<LineageDirection | ''>('');
  // 筛选条件 - 对象类型
  const [type, setType] = useState<LineageGroupType | ''>('');
  // 筛选条件 - 最近活跃时间
  const [timestamp, setTimestamp] = useState<timestampValues>(timestampValues.threeMonths);
  // 筛选条件 - 排序
  const [orderObj, setOrderObj] = useState({
    field: 'lineageTimestamp',
    order: 'descend'
  });
  // 筛选条件 - 分页
  const [pager, setPager] = useState({
    pageNo: 1,
    pageSize: 10
  });
  // 总数
  const [total, setTotal] = useState(0);
  // 血缘节点数据详情缓存 - id:detail
  const [detailCache, setDetailCache] = useState<DetailCache>(initDetailCache());
  /**
   * nodeType 图谱节点类型
   * 1. 当前为卷时，生成模拟节点作为中心节点
   * 2. 当前为卷但选中了筛选路径时，生成volume类型中心节点，即内部路径
   * 3. 当前为表时，生成table类型中心节点
   */
  const nodeType = useMemo(() => {
    if (!isVolume) return LineageGroupType.tables;
    else return pathValue ? LineageGroupType.volumes : LineageGroupType.virtualVolume;
  }, [isVolume, pathValue]);
  const nodeId = useMemo(() => {
    return isVolume ? pathValue || `${getVolumePath(catalog, schema, table)}-virtual` : resourceId;
  }, [resourceId, isVolume, pathValue, catalog, schema, table]);
  // 当前节点信息（图谱中心节点）
  const currentNode = useMemo(() => {
    return {
      id: nodeId,
      type: 'custom',
      data: {
        nodeType: nodeType,
        info: {
          isRoot: true,
          catalogName: catalog,
          schemaName: schema,
          tableName: table,
          volumeName: table,
          type: nodeType,
          path: pathValue || getVolumePath(catalog, schema, table)
        }
      }
    };
  }, [nodeType, nodeId, catalog, schema, table, pathValue]);
  const [nodes, setNodes] = useState([currentNode]);
  const [loading, setLoading] = useState(false);
  // 请求血缘图谱数据
  const {
    loading: lineageLoading,
    nodes: lineageNodes,
    edges,
    lineageGroup,
    getLineageData
  } = useLineageData({
    resourceId,
    resourceType,
    assetType: nodeType,
    pathValue,
    timestamp,
    nodeId
  });
  useEffect(() => {
    setNodes([currentNode, ...lineageNodes]);
  }, [lineageNodes, currentNode]);

  // 接口 获取节点详情
  const getNodeDetail = useCallback(
    async (record) => {
      const getDetail = lineageGroupTypeMap[record?.type]?.getDetail;
      const key = lineageGroupTypeMap[record?.type]?.getKey(record);
      if (!getDetail) return;
      try {
        const res = await getDetail(record, workspaceId);
        return res?.success && res?.result ? {key, value: res?.result || res} : null;
      } catch (e) {
        console.error(`获取${key}详情失败`, e);
        return null;
      }
    },
    [workspaceId]
  );
  // 更新缓存方法
  const updateDetailCache = useCallback(
    async (type: LineageGroupType, record: LineageInfo) => {
      try {
        const result = await getNodeDetail({...record, type});
        if (!result) return null;
        const {key, value} = result;
        setDetailCache((currentCache) =>
          produce(currentCache, (draft) => {
            draft[type]?.set(key, value);
          })
        );

        return {key, value};
      } catch (e) {
        console.error(`更新详情缓存失败`, e);
        return null;
      }
    },
    [getNodeDetail, setDetailCache]
  );
  // 具备全名的血缘列表数据
  const [fullnameGroup, setFullGroup] = useState([]);

  const handleGetDetailByGroup = useCallback(async () => {
    (async () => {
      setLoading(true);
      const groupWithFullname = await getDetailByGroup(
        lineageGroup,
        detailCache,
        updateDetailCache,
        userList
      );
      setFullGroup(groupWithFullname);
      setLoading(false);
    })();
  }, [lineageGroup, userList]);
  useEffect(() => {
    handleGetDetailByGroup();
  }, [handleGetDetailByGroup]);

  // 路径筛选选项 - TOOD: 方法待提取
  const [pathOptions, setPathOptions] = useState([]);
  const getPathList = useCallback(async () => {
    const upperTimestamp = getUpperTimestamp(timestamp);
    const options = [
      {
        label: '全部路径',
        value: ''
      }
    ];
    try {
      const upPath = await queryVolumeLineagePath(workspaceId, {
        fullName: resourceId,
        lineageDirection: LineageDirection.Upstream,
        startTimestamp: upperTimestamp
      });
      const downPath = await queryVolumeLineagePath(workspaceId, {
        fullName: resourceId,
        lineageDirection: LineageDirection.Downstream,
        startTimestamp: upperTimestamp
      });
      if (upPath.success) {
        options.push(
          ..._.map(upPath.result, (path) => ({
            label: path,
            value: path
          }))
        );
      }
      if (downPath.success) {
        options.push(
          ..._.map(downPath.result, (path) => ({
            label: path,
            value: path
          }))
        );
      }
      setPathOptions(options);
    } catch (e) {
      console.error('拉取路径列表失败', e);
      setPathOptions([]);
    }
  }, [resourceId, workspaceId, timestamp]);
  useEffect(() => {
    isVolume && getPathList();
  }, [isVolume, getPathList]);

  const columns = [
    {
      title: '关联对象',
      dataIndex: 'fullname',
      width: 300,
      ellipse: true,
      render: (value: string, record) => renderTableName(value, record, workspaceId)
    },
    {
      title: '上/下游',
      dataIndex: 'direction',
      width: 120,
      filterMultiple: false,
      filters: Object.entries(directionMap).map(([key, value]) => ({
        text: value.text,
        value: key as LineageDirection
      })),
      render: (value: LineageDirection) => (
        <div className={styles['lineage-direction']}>
          {directionMap[value]?.text} <IconSvg type={directionMap[value]?.icon} fill="none" />
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      filterMultiple: false,
      filters: typeOptions,
      width: 100,
      render: (value: LineageGroupType) => lineageGroupTypeMap[value]?.text || '未知类型'
    },
    {
      title: '最近活跃时间',
      dataIndex: 'lineageTimestamp',
      width: 180,
      sorter: true,
      render: (value) => formatTime(value)
    }
  ];
  // 前端实现过滤和筛选
  const filteredLineageList = useMemo(() => {
    let list = [];
    // 类型筛选
    if (type) {
      switch (type) {
        case LineageGroupType.tables:
          list = [
            ...fullnameGroup[LineageGroupType.tables],
            ...fullnameGroup[LineageGroupType.externalTables]
          ];
          break;
        case LineageGroupType.volumes:
          list = [
            ...fullnameGroup[LineageGroupType.volumes],
            ...fullnameGroup[LineageGroupType.externalFiles]
          ];
          break;
        default:
          list = fullnameGroup[type];
      }
    } else {
      list = Object.values(fullnameGroup).flat();
    }
    list = deDuplicateLieageData(list);
    // 搜索关联对象名称
    if (searchValue) {
      const lowerSearchValue = searchValue.toLowerCase();
      list = _.filter(list, (item) => _.includes(item.fullname.toLowerCase(), lowerSearchValue));
    }
    // 上下游筛选
    if (direction) {
      list = _.filter(list, (item) => item.direction === direction);
    }
    //  最近活跃时间排序
    if (orderObj.field) {
      list = _.orderBy(list, [orderObj.field], orderObj.order === 'ascend' ? 'asc' : 'desc');
    }
    setTotal(list.length);
    // 分页
    const start = (pager.pageNo - 1) * pager.pageSize;
    const end = start + pager.pageSize;
    return _.slice(list, start, end);
  }, [
    fullnameGroup,
    direction,
    type,
    orderObj.field,
    orderObj.order,
    pager.pageNo,
    pager.pageSize,
    searchValue
  ]);

  // 全屏控制
  const [isFullScreen, setIsFullScreen] = useState(false);
  const handleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleRefresh = () => {
    getLineageData();
    setDetailCache(initDetailCache());
  };

  // 抽屉标题渲染
  const renderDrawerTitle = () => {
    return (
      <>
        <span>{name} 的数据血缘</span>
        <Button className={cx(styles['full-btn'], 'mr-[8px]')} onClick={handleFullScreen}>
          {isFullScreen ? '收起全屏' : '全屏查看'}
        </Button>
      </>
    );
  };

  const onTableChange = (pagination, filters, sorter) => {
    setPager({
      pageNo: pagination.current,
      pageSize: pagination.pageSize
    });
    setOrderObj({
      field: sorter.field,
      order: sorter.order
    });
    setType(filters?.type?.[0]);
    setDirection(filters?.direction?.[0]);
  };
  const emptyText = useMemo(() => {
    return _.find(timestampOptions, (item) => item.value === timestamp)?.label || '-';
  }, [timestamp]);
  return (
    <div className={cx(styles['lineage-list'])}>
      <div className={cx(styles['filter-container'], 'mb-[16px]')}>
        <div className={cx(styles['left-container'])}>
          <Search
            width="240"
            placeholder="请输入关联对象搜索"
            onSearch={(value) => {
              setSearchValue(value);
              setPager({
                ...pager,
                pageNo: 1
              });
            }}
          />
          {isVolume && (
            <Select
              options={pathOptions}
              value={pathValue}
              onChange={(value) => setPathValue(value)}
              style={{width: 240}}
            />
          )}
          <Select options={timestampOptions} value={timestamp} onChange={(value) => setTimestamp(value)} />
        </div>
        <div className={cx(styles['right-container'])}>
          <RefreshButton onClick={handleRefresh} />
          <Button onClick={() => setShowDrawer(true)}>查看血缘图谱</Button>
        </div>
      </div>
      <Table
        className={styles['lineage-table']}
        columns={columns}
        onChange={onTableChange}
        dataSource={filteredLineageList}
        loading={loading || lineageLoading}
        scroll={{x: 620}}
        locale={{emptyText: <EmptyList text={`${emptyText}，暂无任何数据`} />}}
        pagination={{
          total,
          current: pager.pageNo,
          pageSize: pager.pageSize,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`
        }}
      />
      <Drawer
        className={styles['drawer-container']}
        title={renderDrawerTitle()}
        visible={showDrawer}
        maskClosable={false}
        onClose={() => setShowDrawer(false)}
        width={isFullScreen ? '100%' : '85%'}
      >
        <DetailCacheContext.Provider value={{detailCache, updateDetailCache, userList, timestamp}}>
          <ReactFlowProvider>
            <div className={styles['graph-filter-container']}>
              {isVolume && (
                <Select
                  options={pathOptions}
                  value={pathValue}
                  onChange={(value) => setPathValue(value)}
                  style={{width: 240}}
                />
              )}
              <Select
                className="ml-[8px]"
                options={timestampOptions}
                value={timestamp}
                onChange={(value) => setTimestamp(value)}
              />
            </div>
            <LineageGraph initialNodes={nodes} initialEdges={edges} />
          </ReactFlowProvider>
        </DetailCacheContext.Provider>
      </Drawer>
    </div>
  );
};
export default LineageList;
