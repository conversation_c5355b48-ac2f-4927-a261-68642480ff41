import React from 'react';
import {getNodeLink} from '@pages/Lineage/utils';
import {Link} from 'react-router-dom';
import IconSvg from '@components/IconSvg';
import {LineageGroupType} from '@pages/Lineage/constants';
import {ConnectionTypeFlatMap} from '@pages/MetaData/Connection/constants';
import styles from './index.module.less';
interface Props {
  info: any;
  detail?: any;
  workspaceId?: string;
}
const RenderDetasourceLink: React.FC<Props> = ({info, detail, workspaceId}) => {
  return (
    <Link
      to={getNodeLink(LineageGroupType.externalTables, info, workspaceId)}
      target="_blank"
      className={styles['datasource-link']}
    >
      <IconSvg className="mr-[4px]" type={ConnectionTypeFlatMap[detail?.connectionType]?.icon} />
      {info?.datasourceName}
    </Link>
  );
};

export default RenderDetasourceLink;
