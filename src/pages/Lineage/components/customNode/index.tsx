/**
 * 血缘图谱自定义节点
 * 节点类型包括： 1. 内部表  2. 外部表 3. 内部路径 4. 外部路径
 * 交互：
 * 1, 点击节点展开右侧抽屉显示详情
 * 2. 点击内容为+的连接桩可以展开上下游
 * 3. 点击table字段可以展示字段血缘，并进行连线
 * @author: <EMAIL>
 */

import React, {useState, useMemo, useEffect, useContext, useCallback, useRef, useLayoutEffect} from 'react';
import {Handle, Position} from '@xyflow/react';
import {Tooltip} from 'acud';
import {NodeType, LineageGroupType, lineageGroupTypeMap} from '@pages/Lineage/constants';
import {getUserNameByValue} from '@pages/Lineage/utils';
import {renderNoDetail, renderNodeName} from '@pages/Lineage/LineageList/renderNameColumn';
import {WorkspaceContext} from '@pages/index';
import {DetailCacheContext} from '@pages/Lineage/LineageList';
import {GraphContext} from '@pages/Lineage/LineageGraph';
import CopyToClipboard from '@components/CopyToClipboard';
import TextEllipsis from '@components/TextEllipsisTooltip';
import RenderDetasourceLink from './renderDatasourceLink';
import styles from './index.module.less';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);
interface CustomNodeProps {
  id: string;
  data: {
    nodeType: NodeType;
    // info: LineageInfo;
    info: any; // 待扩展，+ isRoot
    isExpandedUp: boolean;
    isExpandedDown: boolean;
    integrationRunId?: string;
    showOtherColums: boolean;
  };
}
export const lineageGroupConfigMap = {
  [LineageGroupType.tables]: (_node, detail) => ({
    label: '数据源格式',
    value: detail?.dataSourceFormat || '-'
  }),

  [LineageGroupType.virtualVolume]: (node) => ({
    label: '文件路径',
    value: node?.info?.path || '-',
    showCopy: true
  }),

  [LineageGroupType.volumes]: (node) => ({
    label: '文件路径',
    value: node?.info?.path || '-',
    showCopy: true
  }),

  [LineageGroupType.externalTables]: (node, detail, workspaceId) => ({
    label: '数据源',
    value: RenderDetasourceLink({info: node?.info, detail, workspaceId})
  }),

  [LineageGroupType.externalFiles]: (node, detail, workspaceId) => ({
    label: '数据源',
    value: RenderDetasourceLink({info: node?.info, detail, workspaceId})
  })
};

const CustomLineageNode = ({id, data}: CustomNodeProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInit = useRef(false);
  const node = useMemo(() => {
    const {nodeType, info, isExpandedUp, isExpandedDown} = data;
    return {
      id: id,
      nodeType,
      type: nodeType,
      info: {
        ...info,
        integrationRunId: data?.integrationRunId,
        type: nodeType,
        ...info?.info
      },
      isExpandedUp,
      isExpandedDown,
      isRoot: info?.isRoot || false
    };
  }, [id, data]);
  const config = lineageGroupTypeMap[node.nodeType as NodeType];
  const [detail, setDetail] = useState(null);
  const {workspaceId} = useContext(WorkspaceContext);
  const {detailCache, updateDetailCache, userList} = useContext(DetailCacheContext);
  const {
    handleExpandUpstream,
    handleExpandDownstream,
    handleColumnClick,
    updateNodeData,
    selectedColumn,
    activeNode
  } = useContext(GraphContext);

  const getDetail = useCallback(async () => {
    // 1. 查询缓存是否有对应详情
    if (detailCache?.[node.nodeType]?.has(id)) {
      const detail = detailCache[node.nodeType].get(id);
      setDetail({
        ...detail,
        creator: getUserNameByValue(node?.nodeType, detail, userList)
      });
    }
    // 2. 没有详情数据，调用详情接口查询详情，并存储到map中
    else {
      try {
        const result = await updateDetailCache(node.nodeType, node?.info);
        if (!result) {
          setDetail(null);
          return;
        }
        setDetail({
          ...result?.value,
          creator: getUserNameByValue(node?.nodeType, result?.value, userList)
        });
      } catch (e) {
        console.error('获取详情失败', e);
        setDetail(null);
      }
    }
  }, [id, setDetail, updateDetailCache, detailCache, node?.info, node?.nodeType, userList]);
  useEffect(() => {
    getDetail();
  }, [id]);

  // 根据节点类型返回info内容
  const baseInfo = useMemo(() => {
    const type = node?.nodeType;
    return lineageGroupConfigMap[type]
      ? lineageGroupConfigMap[type](node, detail, workspaceId)
      : {label: '暂无详情', value: ''};
  }, [node, detail, workspaceId]);

  /*
    内部数据表or外部数据表，展示表字段，
    1. 默认最多展示四个，超过四个时只展示两个，展示收缩按钮
    2. 点击字段链接/取消链接字段血缘(下游未展开时不连接)
  */
  const isTable = useMemo(() => {
    return [LineageGroupType.tables, LineageGroupType.externalTables].includes(node?.nodeType);
  }, [node?.nodeType]);
  const isVolume = useMemo(() => {
    return node?.nodeType === LineageGroupType.virtualVolume;
  }, [node?.nodeType]);
  // 控制是否展示收缩按钮
  const showMoreBtn = useMemo(() => {
    return isTable && detail?.columns?.length > 4;
  }, [isTable, detail]);
  // 收缩状态
  const showMore = useMemo(() => {
    return data?.showOtherColums || false;
  }, [data?.showOtherColums]);
  // 展示的字段列表
  const showableColumns = useMemo(() => {
    const columns = detail?.columns || [];
    return showMore ? columns : columns.slice(0, 4);
  }, [detail, showMore]);
  const handleClickMore = (e) => {
    e.stopPropagation();
    updateNodeData([id], (data) => ({...data, showOtherColums: !showMore}));
  };
  // 更新节点最新高度
  useLayoutEffect(() => {
    if (!isInit.current) {
      isInit.current = true;
      return;
    }
    if (ref.current) {
      updateNodeData([id], (data) => ({
        ...data,
        height: ref.current.offsetHeight
      }));
    }
  }, [data?.showOtherColums]);

  // table字段渲染
  const renderTableColumns = () => {
    return (
      <div className={styles['node-table-columns']}>
        {showableColumns.map((column, index) => {
          const columnKey = `${id}-${column.name}`;
          const columnHeight = 24;
          const offsetTop = index * columnHeight + columnHeight / 2 + 125;
          return (
            <div
              className={cx(styles['table-column'], {'selected-column': selectedColumn === columnKey})}
              key={index}
              onClick={(e) => handleColumnClick(e, column, node)}
            >
              <span className={styles['name']}>
                <TextEllipsis tooltip={column.name}>{column.name}</TextEllipsis>
              </span>
              <span className={styles['type']}>{column?.typeName || column?.dbType}</span>
              <Handle
                type="source"
                position={Position.Right}
                id={columnKey}
                style={{
                  top: offsetTop,
                  opacity: 0
                }}
              />
              <Handle
                type="target"
                position={Position.Left}
                id={columnKey}
                style={{
                  top: offsetTop,
                  opacity: 0
                }}
              />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <div className={cx(styles['node-type-text'])}>{config?.typeText}</div>
      <div className={cx({[styles['selected-outer-border']]: activeNode?.id === id})}>
        <div
          className={cx(styles['node-card-container'], {
            [styles['selected-table-node']]: activeNode?.id === id && !isVolume,
            [styles['volume-node']]: isVolume
          })}
          ref={ref}
        >
          {detail ? (
            <>
              <div className={cx(styles['node-title'], 'mb-[12px]')}>
                {renderNodeName(node, detail, workspaceId)}
              </div>
              <div className={cx(styles['node-content'])}>
                <div className={cx(styles['info-container'], 'mb-[8px]')}>
                  <span className={cx(styles['label'], 'mr-[16px]')}>{baseInfo?.label}</span>

                  <TextEllipsis tooltip={baseInfo?.value} width={200}>
                    <span className={styles['value']}>{baseInfo?.value}</span>
                  </TextEllipsis>
                  {baseInfo?.showCopy && (
                    <CopyToClipboard className="ml-[8px]" text={baseInfo?.value} showText={false} />
                  )}
                </div>
                {isTable && renderTableColumns()}
              </div>
              {isTable && showMoreBtn && (
                <div className={styles['node-more']} onClick={handleClickMore}>
                  {showMore ? '收起' : `展开剩余${detail?.columns.length - 4}个字段`}
                </div>
              )}
            </>
          ) : (
            // detail为null时，展示已删除节点样式
            <div className={cx(styles['node-card-container'], styles['deleted-node'])}>
              <div className={cx(styles['node-title'], 'mb-[12px]')}>{renderNoDetail(node?.info, '')}</div>
            </div>
          )}

          {/* 连接桩 */}
          <Tooltip title="点击展开节点血缘">
            <Handle
              type="target"
              className={cx(styles['base-handle'], {
                'handle-plus': !node?.isExpandedUp && detail
              })}
              position={Position.Left}
              id={id}
              style={{top: 50}}
              onClick={(e) => {
                if (detail) handleExpandUpstream(e, node);
              }}
            />
          </Tooltip>
          <Tooltip title="点击展开节点血缘">
            <Handle
              type="source"
              className={cx(styles['base-handle'], {
                'handle-plus': !node?.isExpandedDown && detail
              })}
              position={Position.Right}
              id={id}
              style={{top: 50}}
              onClick={(e) => {
                if (detail) handleExpandDownstream(e, node);
              }}
            />
          </Tooltip>
        </div>
      </div>
    </>
  );
};

export default CustomLineageNode;
