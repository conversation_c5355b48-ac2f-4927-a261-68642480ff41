.node-type-text {
  font-family: <PERSON>Fang SC;
  font-weight: 400;
  font-style: Regular;
  font-size: 12px;
  color: #818999;
}

.deleted-node {
  cursor: not-allowed !important;
  color: #84868c;
}
.node-card-container {
  box-sizing: border-box;
  cursor: pointer;
  width: 320px;
  background: #fff;
  background-clip: border-box;
  border: 0.5px solid #eaeef2;
  border-radius: 8px;
  transition: all 0.1s;
  &:hover {
    border: 1px solid #5c61ff;
  }

  .node-title {
    padding: 16px;
    height: 50px;
    max-width: 100%;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(209, 217, 255, 0.4) 0%, rgba(255, 255, 255, 0) 100%);
    font-weight: 500;
  }
  .node-more {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    color: #495366;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    background-color: #f2f5fa;
  }
}
.volume-node {
  border: 1.5px dashed #c7c9ff;
  cursor: not-allowed;
  &:hover {
    border: 1.5px dashed #c7c9ff;
  }
}
.selected-table-node {
  border: 1px solid #5c61ff;
}
.node-content {
  width: 100%;
  padding: 12px 16px 14px;
  font-size: 12px;
  .node-table-columns {
    .table-column {
      height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      transition: all 0.2s;
      .name {
        max-width: 70%;
      }
      .type {
        max-width: 30%;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        border-radius: 4px;
        font-size: 10px;
        color: #495366;
        background-color: #f7f9fc;
        border: 0.5px solid #e4e8f0;
      }

      &:hover {
        background-color: #f7f9fc;
      }
    }
    .selected-column {
      background-color: #f2f5fa;
      .type {
        background-color: #ffffff;
      }
    }
  }
  .info-container {
    display: flex;
    align-items: center;
  }
}
.datasource-link {
  svg {
    border-radius: 50%;
  }
}

// 连接桩样式
.base-handle {
  cursor: pointer !important;
  width: 12px;
  height: 12px;
  border-radius: 12px;
  color: #fff;
  background-color: #818999;
  font-size: 12px;
  &:hover {
    background-color: #333aff;
  }
}
.handle-plus {
  &::before {
    position: absolute;
    top: -6px;
    right: 1px;
    content: '+';
  }
}
