.node-detail {
  .info-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .info-item {
      display: flex;
      .info-label {
        display: inline-block;
        width: 80px;
        margin-right: 32px;
        color: #818999;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
      }
      .info-value {
        display: flex;
        font-size: 13px;
        color: #000;
      }
    }
  }
  :global {
    .acud-table .acud-btn {
      padding: 0;
    }
  }
  .detail-lineage-container {
    .lineage-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
    }
  }
  .lineage-direction {
    width: 55px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #495366;
    background-color: #f7f9fc;
    border-radius: 20px;
    gap: 4px;
  }
}
