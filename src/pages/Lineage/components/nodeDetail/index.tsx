import _ from 'lodash';
import React, {useMemo, useState, useContext, useEffect} from 'react';
import {LineageGroupType} from '@pages/Lineage/constants';
import {WorkspaceContext} from '@pages/index';
import {DetailCacheContext} from '@pages/Lineage/LineageList';
import {GraphContext} from '@pages/Lineage/LineageGraph';
import {getUserNameByValue} from '@pages/Lineage/utils';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {Tabs, Table, Button} from 'acud';
import DetailLineage from './detailLineage';
import CopyToClipboard from '@components/CopyToClipboard';
import RenderDetasourceLink from '@pages/Lineage/components/customNode/renderDatasourceLink';
import styles from './index.module.less';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);
const {TabPane} = Tabs;
interface NodeDetailProps {
  node: any;
  nodeDetail: any;
  handleColumnClick: (e, column, node) => void;
  onClose: () => void;
}
export type DetailItem = {
  label: string;
  value: React.ReactNode;
  showCopy?: boolean;
};

export const lineageNodeDetailMap = {
  [LineageGroupType.tables]: ({creatorName, nodeDetail}) => [
    {label: '创建人', value: creatorName || '-'},
    {label: '数据源格式', value: nodeDetail?.dataSourceFormat || '-'}
  ],

  [LineageGroupType.externalTables]: ({info, creatorName, nodeDetail, workspaceId}) => [
    {label: '数据源', value: RenderDetasourceLink({info, detail: nodeDetail, workspaceId})},
    {label: '数据源创建人', value: creatorName || '-'}
  ],

  [LineageGroupType.externalFiles]: ({info, creatorName, nodeDetail, workspaceId}) => [
    {label: '数据源', value: RenderDetasourceLink({info, detail: nodeDetail, workspaceId})},
    {label: '数据源创建人', value: creatorName || '-'}
  ],

  [LineageGroupType.volumes]: ({info, creatorName}) => [
    {label: '创建人', value: creatorName || '-'},
    {label: '文件路径', value: info?.path || '-', showCopy: true}
  ]
};

const NodeDetail: React.FC<NodeDetailProps> = ({node, nodeDetail}) => {
  const {nodeType, info} = node;
  const {workspaceId} = useContext(WorkspaceContext);
  // table节点展示字段信息
  const showcolumns = useMemo(() => {
    return [LineageGroupType.tables, LineageGroupType.externalTables].includes(nodeType);
  }, [nodeType]);
  const [activeKey, setActiveKey] = useState(showcolumns ? 'column' : 'lineage');
  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };
  const {userList} = useContext(DetailCacheContext);
  const {handleColumnClick, selectedColumn} = useContext(GraphContext);

  // 基本信息展示内容
  const baseInfo = useMemo(() => {
    const creatorName = getUserNameByValue(nodeType, nodeDetail, userList);
    return lineageNodeDetailMap[nodeType]
      ? lineageNodeDetailMap[nodeType]({info, creatorName, nodeDetail, workspaceId})
      : [];
  }, [nodeType, info, nodeDetail, userList, workspaceId]);
  /**
   * 字段tab展示内容相关
   * 前端实现分页
   */
  // 字段信息数据
  const columnsData = useMemo(() => {
    return nodeDetail?.columns || [];
  }, [nodeDetail]);
  const [pager, setPager] = useState({
    pageNo: 1,
    pageSize: 10,
    total: columnsData.length
  });
  useEffect(() => {
    setPager({
      ...pager,
      total: columnsData.length
    });
  }, [columnsData]);
  // 过滤字段信息数据，实现分页效果
  const filteredColumns = useMemo(() => {
    if (!columnsData) return [];
    return _.slice(columnsData, (pager.pageNo - 1) * pager.pageSize, pager.pageNo * pager.pageSize);
  }, [columnsData, pager.pageNo, pager.pageSize]);
  const onTableChange = (pagination) => {
    setPager({
      total: columnsData.length,
      pageNo: pagination.current,
      pageSize: pagination.pageSize
    });
  };
  // 详情页点击字段血缘 再次点击时不进行取消
  const handleDetailColumnClick = async (e, column) => {
    const selectedKey = `${node?.id}-${column?.name}`;
    const index = _.findIndex(columnsData, (item) => item?.name === column?.name);
    if (selectedColumn !== selectedKey) {
      // 如果点击第5个节点及以下，展开当前节点
      await handleColumnClick(e, column, node, index > 3);
    }
  };
  // 字段信息展示内容
  const renderColumnTable = () => {
    const isExternalTable = nodeType === LineageGroupType.externalTables;
    const columns = [
      {title: '名称', dataIndex: 'name', ellipsis: true, width: 100},
      {title: '类型', dataIndex: isExternalTable ? 'dbType' : 'typeName', width: 80},
      {
        title: '描述',
        dataIndex: isExternalTable ? 'description' : 'comment',
        ellipsis: true,
        render: (text) => text || '-',
        width: 100
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: 120,
        render: (_, record) => (
          <Button
            type="actiontext"
            onClick={(e) => {
              handleDetailColumnClick(e, record);
            }}
          >
            查看字段血缘
          </Button>
        )
      }
    ];
    return (
      <Table
        columns={columns}
        scroll={{x: 400}}
        onChange={onTableChange}
        dataSource={filteredColumns}
        pagination={{
          total: pager.total,
          current: pager.pageNo,
          pageSize: pager.pageSize,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`
        }}
      />
    );
  };
  return (
    <div className={styles['node-detail']}>
      <div className={styles['info-container']}>
        {baseInfo.map((item, index) => (
          <div className={styles['info-item']} key={index}>
            <span className={styles['info-label']}>{item.label}</span>

            <span className={styles['info-value']}>
              <TextEllipsis width={300} tooltip={item.value}>
                {item.value}
              </TextEllipsis>
              {item.showCopy && <CopyToClipboard text={item.value} className="ml-[10px]" showText={false} />}
            </span>
          </div>
        ))}
      </div>
      {nodeType !== LineageGroupType.externalFiles && (
        <Tabs
          className={cx(styles['tab-container'], 'mt-[20px]')}
          activeKey={activeKey}
          onChange={handleTabChange}
        >
          {showcolumns && (
            <TabPane tab="字段" key="column">
              {renderColumnTable()}
            </TabPane>
          )}
          {nodeType !== LineageGroupType.externalTables && (
            <TabPane tab="血缘" key="lineage">
              <DetailLineage detail={nodeDetail} node={node} />
            </TabPane>
          )}
        </Tabs>
      )}
    </div>
  );
};
export default NodeDetail;
