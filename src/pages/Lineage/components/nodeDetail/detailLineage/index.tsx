import _ from 'lodash';
import React, {useCallback, useEffect, useState, useMemo, useContext} from 'react';
import {WorkspaceContext} from '@pages/index';
import {DetailCacheContext} from '@pages/Lineage/LineageList';
import styles from '../index.module.less';
import {LineageDirection, AssetType} from '@api/lineage/type';
import {Select, Table, Button} from 'acud';
import {renderTableName, getNodeLink, getDetailByGroup, deDuplicateLieageData} from '@pages/Lineage/utils';
import {directionMap, typeOptions, lineageGroupTypeMap, LineageGroupType} from '@pages/Lineage/constants';
import useLineageData from '@pages/Lineage/hooks/useLineageData';
import IconSvg from '@components/IconSvg';
import EmptyList from '@pages/Lineage/components/emptyList';
interface DetailLineageProps {
  detail: any;
  node: any;
}
const DetailLineage: React.FC<DetailLineageProps> = ({detail, node}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const {detailCache, updateDetailCache, userList, timestamp} = useContext(DetailCacheContext);
  const filteredOptions = [{label: '全部类型', value: ''}, ...typeOptions];
  // 具备全名的血缘列表数据
  const [fullnameGroup, setFullGroup] = useState({});
  const [type, setType] = useState<LineageGroupType | ''>('');
  // 筛选条件 - 血缘方向
  const [direction, setDirection] = useState<LineageDirection | ''>('');
  // 筛选条件 - 分页
  const [pager, setPager] = useState({
    pageNo: 1,
    pageSize: 10
  });
  // 总数
  const [total, setTotal] = useState(0);
  const info = useMemo(() => node?.info, [node]);
  const resourceId = useMemo(() => {
    return lineageGroupTypeMap?.[info?.type]?.getName(info);
  }, [info]);
  const resourceType = useMemo(() => {
    return info?.type === LineageGroupType.tables ? AssetType.TABLE : AssetType.VOLOME;
  }, [info?.type]);

  const {loading, lineageGroup} = useLineageData({
    resourceId,
    resourceType,
    assetType: node?.nodeType,
    pathValue: info?.path,
    timestamp,
    nodeId: node?.id
  });

  const handleGetDetailByGroup = async () => {
    const groupWithFullname = await getDetailByGroup(lineageGroup, detailCache, updateDetailCache, userList);
    setFullGroup(groupWithFullname);
  };
  useEffect(() => {
    handleGetDetailByGroup();
  }, [lineageGroup]);
  const filteredLineageList = useMemo(() => {
    let list = [];
    if (type) {
      switch (type) {
        case LineageGroupType.tables:
          list = [
            ...fullnameGroup[LineageGroupType.tables],
            ...fullnameGroup[LineageGroupType.externalTables]
          ];
          break;
        case LineageGroupType.volumes:
          list = [
            ...fullnameGroup[LineageGroupType.volumes],
            ...fullnameGroup[LineageGroupType.externalFiles]
          ];
          break;
        default:
          list = fullnameGroup[type];
      }
    } else {
      list = Object.values(fullnameGroup).flat();
    }
    list = deDuplicateLieageData(list);
    // 上下游筛选
    if (direction) {
      list = _.filter(list, (item) => item.direction === direction);
    }
    setTotal(list.length);
    // 分页
    const start = (pager.pageNo - 1) * pager.pageSize;
    const end = start + pager.pageSize;
    return _.slice(list, start, end);
  }, [fullnameGroup, type, direction, pager]);
  // 更多详情
  const goDetail = () => {
    const aimUrl = getNodeLink(info?.type, info, workspaceId);
    window.open(`#${aimUrl}&tab=4`, '_blank');
  };
  const columns = [
    {
      title: '关联对象',
      dataIndex: 'fullname',
      ellipse: true,
      width: 200,
      render: (value: string, record) => renderTableName(value, record, workspaceId)
    },
    {
      title: '上/下游',
      dataIndex: 'direction',
      width: 92,
      filterMultiple: false,
      filters: Object.entries(directionMap).map(([key, value]) => ({
        text: value.text,
        value: key as LineageDirection
      })),
      render: (value: LineageDirection) => (
        <div className={styles['lineage-direction']}>
          {directionMap[value]?.text} <IconSvg type={directionMap[value]?.icon} fill="none" />
        </div>
      )
    }
  ];
  const onTableChange = (pagination, filters) => {
    setPager({
      pageNo: pagination.current,
      pageSize: pagination.pageSize
    });
    setType(filters?.type?.[0]);
    setDirection(filters?.direction?.[0]);
  };
  return (
    <div className={styles['detail-lineage-container']}>
      <div className={styles['lineage-header']}>
        <Select options={filteredOptions} value={type} onChange={(value) => setType(value)} />
        <Button onClick={goDetail}>更多详情</Button>
      </div>
      <Table
        columns={columns}
        onChange={onTableChange}
        dataSource={filteredLineageList}
        loading={loading}
        locale={{emptyText: <EmptyList text="暂无任何数据" />}}
        pagination={{
          total,
          current: pager.pageNo,
          pageSize: pager.pageSize,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`
        }}
      />
    </div>
  );
};
export default DetailLineage;
