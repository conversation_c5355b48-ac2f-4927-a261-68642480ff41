/**
 * 数据血缘图谱 - 自定义操作栏
 * @author: <EMAIL>
 */
import React from 'react';
import styles from '../../LineageGraph/index.module.less';
import {Tooltip} from 'acud';
import IconSvg from '@components/IconSvg';
import {useReactFlow} from '@xyflow/react';
interface OprProps {
  initLayout: () => void;
  handleCenter: () => void;
}
const CustomLineageOperation: React.FC<OprProps> = ({initLayout, handleCenter}) => {
  const {zoomIn, zoomOut} = useReactFlow();
  // 恢复默认布局
  const handleRelayout = () => {
    // 恢复初始布局
    initLayout();
    requestAnimationFrame(() => {
      handleCenter();
    });
  };

  return (
    <div className={styles['opr-container']}>
      <Tooltip title="恢复默认图谱">
        <IconSvg
          type="lineage-relayout"
          className={styles['btn-icon']}
          size={16}
          onClick={() => handleRelayout()}
        />
      </Tooltip>
      <Tooltip title="居中视图">
        <IconSvg
          type="lineage-center"
          className={styles['btn-icon']}
          size={16}
          onClick={() => handleCenter()}
        />
      </Tooltip>

      <span className={styles['item-split']}></span>
      <Tooltip title="缩小10%">
        <IconSvg type="workflow-zoom-in" className={styles['btn-icon']} size={16} onClick={() => zoomOut()} />
      </Tooltip>
      <Tooltip title="放大10%">
        <IconSvg type="workflow-zoom-out" className={styles['btn-icon']} size={16} onClick={() => zoomIn()} />
      </Tooltip>
    </div>
  );
};
export default CustomLineageOperation;
