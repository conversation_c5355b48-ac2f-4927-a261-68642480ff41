/**
 * 血缘图谱 - 边详情内容
 * 需要的数据：
 * 1. 源端节点 + 目标端节点
 * 2. 所有任务节点的数据
 */
import _ from 'lodash';
import React, {useMemo, useContext, useState, useEffect, useCallback} from 'react';
import {Tooltip} from 'acud';
import {DetailCacheContext} from '@pages/Lineage/LineageList';
import {WorkspaceContext} from '@pages/index';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import TextEllipsis from '@components/TextEllipsisTooltip';
import IconSvg from '@components/IconSvg';
import moment from 'moment';
import {Link} from 'react-router-dom';
import {
  getDetailByGroup,
  getTaskLink,
  getNodeLink,
  getUserNameByValue,
  getDetailFromCache
} from '@pages/Lineage/utils';
import {lineageGroupTypeMap, LineageGroupType, nameFieldMap} from '@pages/Lineage/constants';
import CopyToClipboard from '@components/CopyToClipboard';
const cx = classNames.bind(styles);
const taskTypes = [LineageGroupType.notebooks, LineageGroupType.workflows, LineageGroupType.jobs];
const lineageNodeDetailMap = {
  [LineageGroupType.tables]: ({creatorName, nodeDetail}) => [
    {label: '创建人', value: creatorName || '-'},
    {label: '数据源格式', value: nodeDetail?.dataSourceFormat || '-'}
  ],

  [LineageGroupType.externalTables]: ({info, creatorName}) => [
    {label: '数据源', value: info?.datasourceName},
    {label: '数据源创建人', value: creatorName || '-'}
  ],

  [LineageGroupType.externalFiles]: ({info, creatorName}) => [
    {label: '数据源', value: info?.datasourceName},
    {label: '数据源创建人', value: creatorName || '-'}
  ],

  [LineageGroupType.volumes]: ({info, creatorName}) => [
    {label: '创建人', value: creatorName || '-'},
    {label: '文件路径', value: info?.path || '-', showCopy: true}
  ]
};
const EdgeDetail: React.FC<{edge: any; nodes: any[]}> = (props) => {
  const {edge, nodes} = props;
  const {detailCache, updateDetailCache, userList} = useContext(DetailCacheContext);
  const {workspaceId} = useContext(WorkspaceContext);
  const [showNotebook, setShowNotebook] = useState(true);
  const [showWorkflow, setShowWorkflow] = useState(true);
  const [showJobs, setShowJobs] = useState(true);
  const [resourceInfo, setResourceInfo] = useState({});
  useEffect(() => {
    setShowNotebook(true);
    setShowWorkflow(true);
    setShowJobs(true);
  }, [edge]);
  const getResourceInfo = useCallback(async () => {
    const result = await getDetailByGroup(edge, detailCache, updateDetailCache);
    setResourceInfo(result);
  }, [edge]);
  // 数据来源信息
  useEffect(() => {
    getResourceInfo();
  }, [getResourceInfo]);

  // 节点信息
  const nodeInfo = useMemo(() => {
    if (!edge) return {};
    const {source, target} = edge;
    const {data: sourceNode} = _.find(nodes, (node) => node.id === source);
    const sourceDetail = getDetailFromCache(sourceNode.info, detailCache);
    const sourceName = lineageGroupTypeMap[sourceNode.nodeType]?.getName(sourceNode.info);
    const {data: targetNode} = _.find(nodes, (node) => node.id === target);
    const targetDetail = getDetailFromCache(targetNode.info, detailCache);
    const targetName = lineageGroupTypeMap[targetNode.nodeType]?.getName(targetNode.info);
    // 最近活跃时间，取最近一次任务的时间戳
    let lineageTimestamp = 0;
    _.map(taskTypes, (type) => {
      if (edge?.[type]?.length) {
        _.map(edge[type], (info) => {
          info.lineageTimestamp > lineageTimestamp && (lineageTimestamp = info.lineageTimestamp);
        });
      }
    });

    return {
      sourceNode,
      sourceName,
      isSourceDeleted: !sourceDetail || sourceDetail?.isDeleted,
      targetNode,
      targetName,
      isTargetDeleted: !targetDetail || targetDetail?.isDeleted,
      lineageTimestamp: moment(lineageTimestamp).format('YYYY-MM-DD HH:mm:ss')
    };
  }, [edge, nodes]);
  const renderLinkItem = (name, node, isDeleted) => {
    return (
      <TextEllipsis tooltip={name} width={300}>
        {isDeleted ? (
          name
        ) : (
          <Link to={getNodeLink(node?.nodeType, node?.info, workspaceId)} target="_blank">
            {name}
          </Link>
        )}
      </TextEllipsis>
    );
  };
  const renderTooltip = (node) => {
    const type = node?.nodeType;
    const key = lineageGroupTypeMap[type]?.getKey(node.info);
    const detail = detailCache?.[type]?.get(key);
    const creatorName = getUserNameByValue(type, detail, userList);
    const baseInfo =
      lineageNodeDetailMap[type]({info: node?.info, nodeDetail: detail, creatorName, workspaceId}) || '';
    const tootipComp = _.map(baseInfo, (item, index) => (
      <div className={styles['tooltip-info-item']} key={index}>
        <span className={styles['tooltip-info-label']}>{item.label}</span>
        <span className={styles['tooltip-info-value']}>
          <TextEllipsis width={320} tooltip={item.value}>
            {item.value}
          </TextEllipsis>
          {item.showCopy && <CopyToClipboard text={item.value} className="ml-[10px]" showText={false} />}
        </span>
      </div>
    ));
    return (
      <Tooltip title={tootipComp} trigger="hover">
        <IconSvg type="lineage_detail" />
      </Tooltip>
    );
  };
  const renderResourceItem = (type) => {
    const list = resourceInfo?.[type];
    const nameField = nameFieldMap?.[type];

    return _.map(list, (item) => {
      const isDeleted = item?.isDeleted || !item?.detail || item?.detail?.isDeleted;
      return (
        <div className={styles['info']} key={`${type}-${nameField}-${item?.fullname}`}>
          {isDeleted ? (
            <span className={cx(styles['info-user'], styles['info-label'])}>被删除对象</span>
          ) : (
            <Link to={getTaskLink(type, item)} target="_blank">
              {item?.fullname}
            </Link>
          )}

          <span className={styles['info-user']}>{item?.creator || '-'}</span>
        </div>
      );
    });
  };
  return (
    <div className={styles['edge-lineage-content-container']}>
      <div className={styles['info-container']}>
        <div className={styles['info-item']}>
          <div className={styles['info-label']}>
            源对象
            {renderTooltip(nodeInfo?.sourceNode)}
          </div>
          <div className={styles['info-value']}>
            {renderLinkItem(nodeInfo.sourceName, nodeInfo.sourceNode, nodeInfo.isSourceDeleted)}
          </div>
        </div>
        <div className={styles['info-item']}>
          <div className={styles['info-label']}>
            目标对象
            {renderTooltip(nodeInfo?.targetNode)}
          </div>
          <div className={styles['info-value']}>
            {renderLinkItem(nodeInfo.targetName, nodeInfo.targetNode, nodeInfo.isTargetDeleted)}
          </div>
        </div>
        <div className={styles['info-item']}>
          <div className={styles['info-label']}>最近活跃时间</div>
          <div className={styles['info-value']}>{nodeInfo.lineageTimestamp}</div>
        </div>
      </div>
      <div className={styles['resource-container']}>
        <h2 className={styles['resource-title']}>血缘采集来源</h2>
        <div className={styles['resource-item']}>
          <div className={styles['icon']}>
            <IconSvg type="lineage-notebook" size={20} />
          </div>
          <div className={styles['item-content']}>
            <div className={styles['title']} onClick={() => setShowNotebook(!showNotebook)}>
              Notebook（{resourceInfo?.[LineageGroupType.notebooks]?.length}）
            </div>
            {showNotebook && renderResourceItem(LineageGroupType.notebooks)}
          </div>
        </div>
        <div className={cx(styles['resource-item'], 'mt-[24px]')}>
          <div className={styles['icon']}>
            <IconSvg type="lineage-workflow" size={20} />
          </div>
          <div className={styles['item-content']}>
            <div className={styles['title']} onClick={() => setShowWorkflow(!showWorkflow)}>
              工作流（{resourceInfo?.[LineageGroupType.workflows]?.length}）
            </div>

            {showWorkflow && renderResourceItem(LineageGroupType.workflows)}
          </div>
        </div>
        <div className={cx(styles['resource-item'], 'mt-[24px]')}>
          <div className={styles['icon']}>
            <IconSvg type="lineage-jobs" size={20} />
          </div>
          <div className={styles['item-content']}>
            <div className={styles['title']} onClick={() => setShowJobs(!showJobs)}>
              数据集成（{resourceInfo?.[LineageGroupType.jobs]?.length}）
            </div>
            {showJobs && renderResourceItem(LineageGroupType.jobs)}
          </div>
        </div>
      </div>
    </div>
  );
};
export default EdgeDetail;
