.edge-lineage-content-container {
  .info-container {
    margin-bottom: 28px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    .info-item {
      display: flex;
      gap: 32px;
      .info-label {
        color: #818999;
        font-size: 13px;
        min-width: 80px;
        svg {
          cursor: pointer;
          margin-left: 5px;
          fill: none;
        }
      }
    }
  }

  .resource-container {
    .resource-title {
      font-weight: 500;
      font-style: Medium;
      font-size: 14px;
      margin-bottom: 20px;
    }
    .resource-item {
      display: flex;
      justify-content: flex-start;
      .icon {
        width: 30px;
        height: 30px;
        background-color: #f2f5fa;
        border: 0.5px solid #e4e7eb;
        border-radius: 4px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        svg {
          fill: none;
        }
      }
      .item-content {
        display: flex;
        flex-direction: column;

        .title {
          cursor: pointer;
          margin-bottom: 8px;
          line-height: 30px;
          font-weight: 500;
          font-size: 13px;
          color: #000;
        }
        .info {
          display: flex;
          flex-direction: column;
          .info-user {
            font-size: 12px;
            color: #818999;
            font-weight: 400;
          }
          .info-label {
            font-size: 14px;
          }
        }
      }
    }
  }
}
.tooltip-info-item {
  display: flex;
  max-width: 320px;
  .tooltip-info-label {
    display: inline-block;
    min-width: 75px !important;
    margin-right: 32px;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
  .tooltip-info-value {
    display: flex;
  }
}
