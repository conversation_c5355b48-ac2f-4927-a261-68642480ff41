import _ from 'lodash';
import {getWorkspaceFileResult} from '@api/WorkArea';
import EditableContent from '@components/EditableContent';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Button, Col, Form, Input, Row, Select, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {ConnectionTypeMap} from '@pages/MetaData/Connection/constants';
import {queryConnectionList} from '@api/connection';
import {nodeMap} from '@pages/JobWorkflow/Jobs/components/EditPage/globalVar';
import {setEditNodeData, setFormIsDirty} from '@store/workflow';
import {IJsonNodeData} from '../../../../EditContent/X6EditPage/type';

const connectionTypeList = ConnectionTypeMap[0].typeList.map((item) => ({
  label: item.type,
  value: item.type
}));
// .filter((item) => item.value === 'MySQL'); // 一期只支持MySQL

const SparkTaskParams: React.FC<{form?: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);
  // 选中的 id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);

  const [connectionList, setConnectionList] = useState<any[]>([]);

  async function getConnectionList(connectType: string) {
    const res = await queryConnectionList(workspaceId, {
      type: connectType,
      pageNo: 1,
      pageSize: 100
    });
    if (res.success) {
      const connections = res.result.connections;
      setConnectionList(connections.map((item) => ({label: item.name, value: item.name})));
      return connections;
    }
  }

  // 初始化数据源
  useEffect(() => {
    const fetchConnectionList = async () => {
      const connectType = form?.getFieldValue('connectionType');
      const connectionName = form?.getFieldValue('connectionName');
      if (connectType) {
        const connections = await getConnectionList(connectType);
        // 如果没有数据源 且 有数据源列表 则设置第一个数据源
        if (!connectionName && connections.length > 0) {
          form?.setFieldValue('connectionName', connections[0].name);
          form?.validateFields();
          changeForm({
            connectionName: connections[0].name
          });
        }
        // 如果有数据源, 但是数据源列表中没有 则清空数据源
        if (connectionName && !_.find(connections, (item) => item.name === connectionName)) {
          form?.setFieldValue('connectionName', null);
          form?.validateFields();
          changeForm({
            connectionName: null
          });
        }
      }
    };
    setTimeout(() => {
      fetchConnectionList();
    }, 100);
  }, [selectedNodeId]);

  const handleConnectionTypeChange = async (value) => {
    form?.setFieldValue('connectionName', null);
    getConnectionList(value);
  };

  // 修改表单 更新全局变量
  const changeForm = async (values: any) => {
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        ...values
      }
    };
    nodeMap.set(selectedNodeId, newObj);
    nodeMap.set(selectedNodeId, newObj);
  };

  const handleSqlFilePathChange = async (value: string) => {
    if (value) {
      const res = await getWorkspaceFileResult({workspaceId, path: value});
      if (res.success) {
        const {connectionType, connectionId: connectionName} = res.result?.ext || {};
        if (connectionType) {
          form?.setFieldValue('connectionType', connectionType);
          setConnectionList([]);
          getConnectionList(connectionType);
          form?.setFieldValue('connectionName', connectionName);
          form?.validateFields();
          changeForm({
            connectionType,
            connectionName
          });
        }
      }
    }
  };

  return (
    <>
      <Form.Item
        label="代码路径"
        name="sqlFilePath"
        rules={isEditing ? [{required: true, message: '请输入程序文件'}] : []}
      >
        <EditableContent isEditing={isEditing} onChange={handleSqlFilePathChange}>
          <FilePathSelectWorkarea selectNodeType={FileNodeTypeEnum.FILE} selectFileSuffix={['sql']} />
        </EditableContent>
      </Form.Item>
      <Form.Item
        label="数据源类型"
        name="connectionType"
        initialValue="MySQL"
        rules={isEditing ? [{required: true, message: '请选择'}] : []}
      >
        <EditableContent isEditing={isEditing} onChange={handleConnectionTypeChange}>
          <Select className="w-full" options={connectionTypeList} />
        </EditableContent>
      </Form.Item>
      <Form.Item
        label="数据源"
        name="connectionName"
        rules={isEditing ? [{required: true, message: '请选择'}] : []}
      >
        <EditableContent isEditing={isEditing}>
          <Select className="w-full" options={connectionList} />
        </EditableContent>
      </Form.Item>
    </>
  );
};

export default SparkTaskParams;
