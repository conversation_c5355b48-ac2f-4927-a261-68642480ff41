import {editJob} from '@api/job';
import {getWorkspaceUserList} from '@api/permission';
import {PrincipalType} from '@api/permission/type';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {<PERSON><PERSON>, <PERSON><PERSON>, Modal, Select, toast, Tooltip} from 'acud';
import classNames from 'classnames/bind';
import React, {useContext, useEffect, useMemo, useState} from 'react';
import {IJobModalProps} from '.';
import styles from './index.module.less';
const cx = classNames.bind(styles);
const JobOwnerModal: React.FC<IJobModalProps> = (props) => {
  const {jobId, runAsOwner, onSubmit} = props;
  const {workspaceId} = useContext(WorkspaceContext);
  const [visible, setVisible] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [users, setUsers] = useState<Array<{id: string; name: string}>>([]);
  const [owner, setOwner] = useState<string>('');

  // 拉取用户列表（目前只支持USER，不支持GROUP）
  const fetchUsers = async (keyword?: string) => {
    const res = await getWorkspaceUserList({
      workspaceId,
      principalType: PrincipalType.User,
      principalName: keyword,
      pageNo: 1,
      pageSize: 100
    });
    if (res.success) {
      const list = res.result?.principalInfos || [];
      const mapped = list
        .filter((item) => item?.principal?.type === PrincipalType.User)
        .map((item) => ({id: item?.principal?.id, name: item?.principal?.name}));
      setUsers(mapped);
    }
  };
  // 负责人下拉框选项
  const userOptions = useMemo(
    () => users.map((u) => ({label: u.name, value: `${u.id}__${u.name}`})),
    [users]
  );

  useEffect(() => {
    fetchUsers();
    setVisible(true);
  }, []);

  const handleOk = async () => {
    if (!owner) {
      toast.error({message: '请选择负责人'});
      return;
    }
    const [runAsOwnerId, runAsOwner] = owner.split('__');
    setLoading(true);
    const res = await editJob({workspaceId, jobId, runAsOwner, runAsOwnerId});
    setLoading(false);
    if (res.success) {
      toast.success({message: '负责人修改成功', duration: 5});
      setVisible(false);
      onSubmit?.(true);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    onSubmit?.(false);
  };

  return (
    <Modal
      className={styles['job-owner-modal']}
      title={
        <>
          修改负责人
          <Tooltip title="负责人是为工作流运行提供权限的角色，工作流会使用负责人权限运行。">
            <IconSvg type="question" fill="none" className="ml-[4px]" />
          </Tooltip>
        </>
      }
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      footer={
        <div>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" disabled={!owner} loading={loading} onClick={handleOk}>
            确定
          </Button>
        </div>
      }
      width={500}
    >
      <Alert type="info" message="负责人修改后，需要重新开启调度状态" style={{marginBottom: 20}} />
      <div className={cx(styles['item-container'], 'mb-[20px]')}>
        <div className={styles['item-label']}>原来负责人</div>
        <Select
          className={styles['item-value']}
          disabled
          options={[{label: runAsOwner, value: runAsOwner}]}
          value={runAsOwner}
        />
      </div>
      <div className={styles['item-container']}>
        <div className={styles['item-label']}>更新负责人</div>
        <Select
          showSearch
          className={styles['item-value']}
          placeholder="请选择"
          options={userOptions}
          onChange={(v) => {
            setOwner(v);
          }}
          value={owner}
          filterOption={false}
          onSearch={(v) => fetchUsers(v)}
        />
      </div>
    </Modal>
  );
};

export default JobOwnerModal;
