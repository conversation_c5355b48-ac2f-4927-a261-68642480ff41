.page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 8px 8px 0;
  .box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 0.5px solid #eaeef2;

    border-radius: 6px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    .header {
      padding: 14px 16px 0 16px;
      height: 82px;
      box-sizing: border-box;
      .breadcrumb-link {
        font-size: 12px;
      }
      .title {
        font-size: 22px;
        line-height: 32px;
        color: #151b26;
        padding-top: 14px;
        padding-bottom: 8px;
        font-weight: 500;
        height: 32px;
        display: flex;
        justify-content: space-between;
      }

      .time-range,
      .schedule-time {
        position: relative;
        left: 16px;
        top: 16px;
        z-index: 1000000;
      }
    }

    .container {
      border-top: 1px solid #edeef5;
      flex: 1;
      overflow: hidden;
      margin-top: 8px;
    }
  }
}
