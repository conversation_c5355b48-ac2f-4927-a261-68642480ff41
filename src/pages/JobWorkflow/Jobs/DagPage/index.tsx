import useUrlState from '@ahooksjs/use-url-state';
import {detailJob, IJob} from '@api/job';
import {jobDependencyReverse} from '@api/workflow';
import Workflow from '@components/Workflow';
import {dealWorkflowNodeDependency} from '@components/Workflow/utils/translate/addWorkflow';
import {dealGroup} from '@components/Workflow/utils/translate/autoGroup';
import {jsonToWorkflow} from '@components/Workflow/utils/translate/jsonToWorkflow';
import {WorkspaceContext} from '@pages/index';
import {CronTypeEnum, JobDetailPageTypeEnum, SPLIT_STR, X6PageTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {onSetConfig, setEdges, setNodes} from '@store/workflowSlice';
import urls from '@utils/urls';
import {Breadcrumb, DatePicker, Link, Loading, toast} from 'acud';
import locale from 'acud/es/date-picker/locale/zh_CN';
import {useMemoizedFn, useRequest} from 'ahooks';
import moment from 'moment';

import {dealCronToStr} from '@pages/JobWorkflow/tools';
import React, {useContext, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';
import {IJsonData} from '../components/EditPage/EditContent/X6EditPage/type';
import FixData from './components/FixData';
import styles from './index.module.less';
const {RangePicker} = DatePicker;
export interface JobDetailPageRef {
  saveJobFn: (checkJsonFlag: boolean) => Promise<boolean>;
}
const DagPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [{jobId, type}] = useUrlState();
  const [jobObj, setJobObj] = useState<IJob>();
  const [timeRange, setTimeRange] = useState<[moment.Moment, moment.Moment]>([
    moment().subtract(1, 'days').startOf('day'),
    moment().endOf('day')
  ]);
  const {workspaceId} = useContext(WorkspaceContext);

  const {nodes, edges} = useSelector((state: IAppState) => state.workflowNewSlice);

  // 获取 工作流依赖节点
  const {run: runDagDependency} = useRequest(jobDependencyReverse, {
    manual: true,
    onSuccess(data, params) {
      console.log(data, params);

      const config = dealWorkflowNodeDependency(nodes, edges, data.result, workspaceId + SPLIT_STR + jobId);
      dispatch(setNodes(config.nodes));
      dispatch(setEdges(config.edges));
    }
  });
  // 初始化工作流
  const initData = useMemoizedFn(async (jobDetail?: IJob) => {
    setJobObj(jobDetail);
    const obj: IJsonData = JSON.parse(jobDetail?.code || '{}');

    const {nodes, edges} = await jsonToWorkflow(obj);

    // 添加分组信息
    const groupConfig = dealGroup(nodes, edges, jobDetail);

    dispatch(setNodes(groupConfig.nodes));
    dispatch(setEdges(groupConfig.edges));

    // DAG 页面 查询依赖节点
    if (type === JobDetailPageTypeEnum.JOB_DAG) {
      // 查询所有依赖节点
      runDagDependency(
        workspaceId,
        jobDetail?.jobId,
        obj?.taskDefinitionList?.map((item) => item.id),
        jobDetail?.version || -1
      );

      dispatch(onSetConfig({key: 'pageType', value: X6PageTypeEnum.JOB_DAG}));
    } else {
      dispatch(onSetConfig({key: 'pageType', value: X6PageTypeEnum.JOB_FIX_DATA}));
    }
  });
  // 获取工作流详情
  const {data: jobDetail, loading} = useRequest(() => detailJob(workspaceId, jobId), {
    onSuccess: (res) => {
      initData(res.result);

      if (type === JobDetailPageTypeEnum.JOB_FIX_DATA) {
        if (!res.result.scheduleConf) {
          navigate(urls.job);
          toast.error({
            message: '工作流未配置调度',
            duration: 5
          });
          return;
        }
        // 初始化时间范围 等待 prd 确定范围
        switch (res.result?.scheduleConf?.type) {
          case CronTypeEnum.HOUR:
            setTimeRange([moment().subtract(1, 'days').startOf('day'), moment().endOf('day')]);
            break;
          case CronTypeEnum.DAY:
            setTimeRange([moment().subtract(10, 'days').startOf('day'), moment().endOf('day')]);
            break;
          case CronTypeEnum.WEEK:
            setTimeRange([moment().subtract(10, 'weeks').startOf('day'), moment().endOf('day')]);
            break;
          case CronTypeEnum.MONTH:
            setTimeRange([moment().subtract(10, 'months').startOf('day'), moment().endOf('day')]);
            break;
          case CronTypeEnum.YEAR:
            setTimeRange([moment().subtract(10, 'years').startOf('day'), moment().endOf('day')]);
            break;
          default:
            setTimeRange([moment().subtract(30, 'days').startOf('day'), moment().endOf('day')]);
            break;
        }
      }
    }
  });

  return (
    <div className={styles['page']}>
      <div className={styles['box']} id="workflow-box">
        <Loading loading={loading} />
        {/* 顶部面包屑 + 标题 */}
        <div className={styles['header']}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link onClick={() => navigate(urls.job)} className={styles['breadcrumb-link']}>
                工作流
              </Link>
            </Breadcrumb.Item>

            <Breadcrumb.Item>{jobObj?.name}</Breadcrumb.Item>
          </Breadcrumb>
          <div className={styles['title']}>
            {type === JobDetailPageTypeEnum.JOB_DAG ? 'DAG' : '补数据'}

            {type === JobDetailPageTypeEnum.JOB_FIX_DATA && (
              <FixData timeRange={timeRange} jobDetail={jobDetail?.result} />
            )}
          </div>
          {type === JobDetailPageTypeEnum.JOB_FIX_DATA && (
            <div className={styles['time-range']}>
              业务时间范围{' '}
              <RangePicker
                defaultValue={timeRange}
                value={timeRange}
                locale={locale}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                onChange={(v) => {
                  setTimeRange(v);
                }}
              />
            </div>
          )}
          {type === JobDetailPageTypeEnum.JOB_DAG && (
            <div className={styles['schedule-time']}>调度时间：{dealCronToStr(jobObj?.scheduleConf)}</div>
          )}
        </div>

        {/* 详情/运行记录 内容 没放在tab 因为需要靠左右边上，放里面有间距 */}
        <div className={styles['container']}>
          <Workflow />
        </div>
      </div>
    </div>
  );
};

export default DagPage;
