import {AppNode, GroupNodeData} from '@components/Workflow/types/types';
import {IAppState} from '@store/index';
import {Button, Modal, Table, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import moment from 'moment';
import React, {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';

import {IJob} from '@api/job';
import {createBackfillJob, IJobBackfillSelection} from '@api/workflow';
import IconSvg from '@components/IconSvg';
import {JobTaskType, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {fixDataRunTime} from '@pages/JobWorkflow/tools';
import styles from './index.module.less';

// 最大补数据次数
const maxNum = 24;

const FixData: React.FC<{timeRange: moment.Moment[]; jobDetail?: IJob}> = ({timeRange, jobDetail}) => {
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const [selectedNodes, setSelectedNodes] = useState<AppNode[]>([]);

  const [selectWorkflow, setSelectWorkflow] = useState<IJobBackfillSelection[]>([]);

  const nodes = useSelector((state: IAppState) => state.workflowNewSlice.nodes);

  // 时间范围校验
  const checkTimeRange = useMemoizedFn(() => {
    if (timeRange?.length !== 2) {
      toast.error({
        message: '请选择业务时间范围',
        duration: 5
      });
      return false;
    }
    //  若时间范围一次也不执行过，或者超过最大补数据次数，则提示用户
    const runTimeList = fixDataRunTime(
      jobDetail?.scheduleConf,
      timeRange[0].format('YYYY-MM-DDTHH:mm:ssZ'),
      timeRange[1].format('YYYY-MM-DDTHH:mm:ssZ'),
      maxNum + 1
    );
    // TODO：时间格式问题导致后端解析错误，暂时不做这个校验
    // if (runTimeList.length === 0) {
    //   toast.error({
    //     message: '时间范围内一次也不执行，请修改时间范围',
    //     duration: 5
    //   });
    //   return false;
    // }

    if (runTimeList.length > maxNum) {
      toast.error({
        message: `时间范围内执行次数超过${maxNum}次，请修改时间范围`,
        duration: 5
      });
      return false;
    }
    return true;
  });

  /**
   * 补数据数据转换
   * 1. 将选择节点添加 工作流信息 setSelectedNodes
   * 2. 将 选择节点 转为 后端需要的数据 setSelectWorkflow
   */
  const convertData = useMemoizedFn(() => {
    // 记录所有节点对应关系
    const nodeMap = new Map<string, AppNode>();
    // 因为 id 过长 重新计算 group id
    const nodeGroupIdMap = new Map<string, string>();

    for (const node of nodes) {
      nodeMap.set(String(node.id), node);
      // 计算 group id
      if (node.type === X6ShapeTypeEnum.GROUP || node.type === X6ShapeTypeEnum.ROOT_GROUP) {
        nodeGroupIdMap.set(String(node.id), String(nodeGroupIdMap.size));
      }
    }

    // 记录每个 group 下选择的节点
    const selectGroupIdMap: Map<string, string[]> = new Map();
    const checkedNodes = nodes
      .filter((node: AppNode) => node.data?.checked)
      .map((node: AppNode) => {
        if (selectGroupIdMap.has(node.parentId)) {
          selectGroupIdMap.get(node.parentId)?.push(String(node?.data?.id));
        } else {
          selectGroupIdMap.set(node.parentId, [String(node?.data?.id)]);
        }
        return {
          ...node,
          jobName: nodeMap.get(node.parentId)?.data?.name || ''
        };
      });
    setSelectedNodes(checkedNodes);

    const result: IJobBackfillSelection[] = [];
    selectGroupIdMap.forEach((value, key) => {
      const jobDetail: GroupNodeData = nodeMap.get(key).data as GroupNodeData;
      const parentNode = nodeMap.get(jobDetail?.parentId);

      const id = nodeGroupIdMap.get(key);

      let sourceId = '';
      let sourceTaskId = '';
      if (parentNode) {
        if (parentNode.type === X6ShapeTypeEnum.TASK) {
          sourceTaskId = String(parentNode?.data?.id);
          // 先获取 父级的 依赖节点 在获取依赖节点的工作流 id
          sourceId = (nodeMap.get(parentNode.parentId) as AppNode)?.id;
          sourceId = nodeGroupIdMap.get(sourceId);
        }
      }

      result.push({
        id,
        jobId: jobDetail.jobId,
        workspaceId: jobDetail?.workspaceId,
        version: jobDetail?.version,
        taskIds: value,
        sourceId,
        sourceTaskId
      });
    });

    setSelectWorkflow(result);
  });

  const showModal = useMemoizedFn(() => {
    if (!checkTimeRange()) {
      return;
    }

    if (nodes.filter((node: AppNode) => node.data?.checked).length === 0) {
      toast.error({
        message: '请选择节点',
        duration: 5
      });
      return;
    }
    convertData();
    setIsModalVisible(true);
  });

  const handleOk = useMemoizedFn(async () => {
    try {
      const result = await createBackfillJob(jobDetail, {
        bizStart: timeRange[0].format('YYYY-MM-DDTHH:mm:ssZ'),
        bizEnd: timeRange[1].format('YYYY-MM-DDTHH:mm:ssZ'),
        selections: selectWorkflow
      });
      if (result.success) {
        toast.success({
          message: '提交成功',
          desciption: '请前往运行记录查看执行结果',
          duration: 5
        });
        setIsModalVisible(false);
      }
    } catch (e) {
      toast.error({
        message: '提交失败',
        duration: 5
      });
    }
  });

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      render: (text, record, idx: number) => <span>{idx + 1}</span>
    },
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <span>{record.data?.name}</span>
    },
    {
      title: '节点类型',
      dataIndex: 'type',
      key: 'type',
      render: (text, record) => (
        <div className={styles['workflow-node-type']}>
          <IconSvg
            color="white"
            className={styles['workflow-icon']}
            size={16}
            type={JobTaskType[record.data.type]?.icon}
          />
          {JobTaskType[record.data?.type].label}
        </div>
      )
    },
    {
      title: '所属工作流',
      dataIndex: 'jobName',
      key: 'jobName'
    }
  ];

  return (
    <>
      <div className={styles['page']}>
        <Button type="primary" onClick={() => showModal()}>
          开始补数据
        </Button>
      </div>

      <Modal title="开始补数据" visible={isModalVisible} onOk={handleOk} onCancel={handleCancel} width={800}>
        <div className={styles['fix-data-description']}>
          确定是要对
          <span className={styles['workflow-job-name']}>「{jobDetail?.name}」</span>的以下{' '}
          {selectedNodes.length} 个 节点进行补数据，本次补数据的业务数据时间范围为 ：<br />
          {timeRange?.[0]?.format('YYYY-MM-DD HH:mm:ss')} 至 {timeRange?.[1]?.format('YYYY-MM-DD HH:mm:ss')}
        </div>
        <Table columns={columns} dataSource={selectedNodes} pagination={false} />
      </Modal>
    </>
  );
};

export default FixData;
