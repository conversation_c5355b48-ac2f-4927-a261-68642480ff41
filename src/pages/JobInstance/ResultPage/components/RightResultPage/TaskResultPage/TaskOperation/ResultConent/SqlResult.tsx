import React, {useMemo} from 'react';
import {Table} from 'acud';

interface ISqlResultProps {
  content: {
    // sql执行结果
    rows: any[][];
    columns: string[];
  };
}
const SqlResult: React.FC<ISqlResultProps> = ({content}) => {
  const columns = content.columns?.map((item, index) => ({
    title: item,
    dataIndex: `${item}-${index}`,
    width: '120px',
    key: `${item}-${index}`
  }));
  const dataSource = content.rows?.map((item) => {
    return item.reduce((acc, cur, index) => {
      acc[columns[index].dataIndex] = cur;
      return acc;
    }, {});
  });
  return (
    <div>
      <Table columns={columns} dataSource={dataSource} pagination={false} scroll={{x: 'max-content'}} />
    </div>
  );
};

export default SqlResult;
