import useUrlState from '@ahooksjs/use-url-state';
import {ComputeResourceItem, deleteComputeResource, getComputeResourceDetail} from '@api/Compute';
import {Privilege, ResourceType} from '@api/permission/type';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {TooltipType} from '@components/AuthComponents/constants';
import IconSvg from '@components/IconSvg';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import PrivateMonitorTab from '@pages/Compute/components/PrivateMonitorTab';
import {privilegeList} from '@pages/Compute/config';
import {WorkspaceContext} from '@pages/index';
import {isPrivate} from '@utils/storageHelper';
import urls from '@utils/urls';
import {Breadcrumb, Button, Dropdown, Loading, Menu, Modal, Tabs, toast} from 'acud';
import {useRequest} from 'ahooks';
import classNames from 'classnames/bind';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {Link} from 'react-router-dom';
import MonitorTab from '../../components/MonitorTab';
import {PAY_TYPE, STATUS} from '../../config';
import styles from './index.module.less';
const cx = classNames.bind(styles);

const BasedInstanceDetail: React.FC = () => {
  const {TabPane} = Tabs;
  const {workspaceId} = useContext(WorkspaceContext);
  const permissionModalRef = useRef<PermissionModalRef>(null);
  const [urlState, setUrlState] = useUrlState();
  const [detail, setDetail] = useState<ComputeResourceItem | null>(null);

  const {run: getDetail, loading} = useRequest(getComputeResourceDetail, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      setDetail(res.result);
    }
  });

  useEffect(() => {
    getDetail({
      workspaceId,
      computeId: urlState.computeId
    });
  }, [workspaceId, urlState.computeId, getDetail]);

  const [activeKey, setActiveKey] = useState(urlState.tab || 'monitor');
  useEffect(() => {
    setActiveKey(urlState.tab || 'monitor');
  }, [urlState.tab]);
  const onTabChange = (key: string) => {
    setActiveKey(key);
    setUrlState({tab: key});
  };

  const canManage = detail?.privileges?.includes(Privilege.Manage);
  const onDelete = (record: ComputeResourceItem) => {
    Modal.confirm({
      title: '删除实例',
      content: '实例删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算实例。确定删除实例？',
      onOk: async () => {
        const res = await deleteComputeResource({
          workspaceId,
          computeId: record.computeId || ''
        });
        if (res.success) {
          toast.success({
            message: '删除成功',
            duration: 3
          });
        }
      }
    });
  };

  function isDeleteDisabled(detail) {
    if (detail) {
      // 预付费除失效状态外不能删除
      if (detail.chargeType === PAY_TYPE.PREPAID) {
        return ![STATUS.INVALID].includes(detail.status);
      }
      return [STATUS.DEPLOY].includes(detail.status);
    }
  }

  function getDelTip(record) {
    if (record.chargeType === PAY_TYPE.PREPAID) {
      return '未到期的包年包月的计算实例/资源池暂不支持删除，有问题请联系售后团队';
    }
    return '实例当前状态不可删除';
  }

  return (
    <div className={cx('db-workspace-wrapper', 'pool-detail-wrapper')}>
      {loading ? (
        <Loading loading />
      ) : (
        <>
          <Breadcrumb className={cx('breadcrumb')}>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess`}>计算实例</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`${urls.compute}?workspaceId=${workspaceId}&tab=dataProcess`}>数据处理实例</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>常驻实例</Breadcrumb.Item>
          </Breadcrumb>
          <div className={cx('title')}>
            <div className={cx('title-text')}>{detail?.name}</div>
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item
                    key="permission"
                    onClick={() => {
                      permissionModalRef.current?.open({
                        resourceType: ResourceType.EtlCompute,
                        resourceId: detail.computeId,
                        resourceName: detail.name,
                        privilegeList
                      });
                    }}
                  >
                    <AuthMenuItem isAuth={canManage} tooltipType={TooltipType.Resource}>
                      权限管理
                    </AuthMenuItem>
                  </Menu.Item>
                  <Menu.Item key="delete">
                    <AuthMenuItem
                      isAuth={canManage}
                      tooltipType={TooltipType.Resource}
                      disabled={isDeleteDisabled(detail)}
                      disabledTooltip={canManage && isDeleteDisabled(detail) ? getDelTip(detail) : ''}
                      onClick={() => {
                        onDelete(detail);
                      }}
                    >
                      删除
                    </AuthMenuItem>
                  </Menu.Item>
                </Menu>
              }
            >
              <Button className="db-more-button" icon={<IconSvg type="more" size={16} />}></Button>
            </Dropdown>
          </div>
          <Tabs activeKey={activeKey} onChange={onTabChange}>
            <TabPane tab="详情" key="detail">
              功能即将上线
            </TabPane>
            <TabPane tab="监控" key="monitor">
              {/* {detail && <MonitorTab detail={detail} />} */}
              {isPrivate ? detail && <PrivateMonitorTab detail={detail} /> : <MonitorTab detail={detail} />}
            </TabPane>
          </Tabs>
          <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />
        </>
      )}
    </div>
  );
};

export default BasedInstanceDetail;
