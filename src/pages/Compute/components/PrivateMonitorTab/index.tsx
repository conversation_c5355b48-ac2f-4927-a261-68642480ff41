import {request} from '@api/apiFunction';
import {ComputeResourceItem, Engine} from '@api/Compute';
import {Privilege} from '@api/permission/type';
import {MetricOwner, MultiLevelMonitorPanel} from '@baidu/fe-bigdata-monitor-sdk';
import '@baidu/fe-bigdata-monitor-sdk/dist/fe-bigdata-monitor-sdk.css';
import axios from 'axios';
import React, {useMemo} from 'react';
function cloneAxiosInstance(instance) {
  const newInstance = axios.create({...instance.defaults, baseURL: '/bma'});

  ['request', 'response'].forEach((type) => {
    instance.interceptors[type].handlers.forEach(({fulfilled, rejected}) => {
      newInstance.interceptors[type].use(fulfilled, rejected);
    });
  });

  return newInstance;
}

const privateRequest = cloneAxiosInstance(request);
privateRequest.interceptors.response.use(
  (response: any) => {
    return response && response.result;
  },
  (err) => Promise.reject(err)
);

// 创建 httpClient 适配器，符合 SDK 的 HttpClient 接口
const httpClientAdapter = {
  get(url, params) {
    // SDK 直接传递 params 对象，需要包装成 axios 的 config 格式
    return privateRequest({
      method: 'get',
      url,
      params
    }) as Promise<any>;
  },
  post(url, data, params = {}) {
    return privateRequest({
      method: 'post',
      url,
      data,
      params
    }) as Promise<any>;
  }
};

export const metricOwnerMap = {
  [Engine.Ray]: MetricOwner.DATABUILDER_RAY,
  [Engine.Spark]: MetricOwner.DATABUILDER_SPARK,
  [Engine.Doris]: MetricOwner.DATABUILDER_DORIS
};

const PrivateMonitorTab: React.FC<{detail: ComputeResourceItem}> = ({detail}) => {
  const canManage = useMemo(() => detail?.privileges?.includes(Privilege.Manage), [detail]);
  const alertButtonConfig = useMemo(
    () => ({
      disabled: !canManage,
      tooltip: !canManage ? '需要管理权限才能操作' : undefined
    }),
    [canManage]
  );
  return (
    detail && (
      <MultiLevelMonitorPanel
        computeId={detail?.computeId}
        engine={detail?.engine}
        metricOwner={metricOwnerMap[detail?.engine]}
        httpClient={httpClientAdapter}
        clusterId={detail?.computeId}
        alertConfigUrl="/#/manage/strategy"
        alertButtonConfig={alertButtonConfig}
      />
    )
  );
};

export default PrivateMonitorTab;
