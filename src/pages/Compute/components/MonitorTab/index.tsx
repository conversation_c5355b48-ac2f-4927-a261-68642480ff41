import React, {useState, ReactNode, useEffect} from 'react';
import {Col, DatePicker, Form, Row, Select, Button} from 'acud';
import RefreshButton from '@components/RefreshButton';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {
  getComputeMetricsCceNodes,
  getBasedInstanceMetrics,
  EMonitorLevelType,
  type metric
} from '@api/Compute';
import {useRegion} from '@hooks/useRegion';
import {useRequest} from 'ahooks';
import locale from 'acud/es/date-picker/locale/zh_CN';
import {BcmChartPanel} from '@baidu/bce-bcm-sdk-react';
import {useFrameworkContext} from '@baidu/bce-react-toolkit';
import {BcmSDK} from '@baidu/bce-bcm-sdk';
import {request} from '@api/apiFunction';
import moment from 'moment';
import IconSvg from '@components/IconSvg';
import axios from 'axios';
import {Privilege} from '@api/permission/type';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';

interface CustomBoxProps {
  title: string;
  children?: ReactNode;
}
function cloneAxiosInstance(instance) {
  const newInstance = axios.create(instance.defaults);

  ['request', 'response'].forEach((type) => {
    instance.interceptors[type].handlers.forEach(({fulfilled, rejected}) => {
      newInstance.interceptors[type].use(fulfilled, rejected);
    });
  });

  return newInstance;
}

const bcmRequest = cloneAxiosInstance(request);
bcmRequest.interceptors.response.use(
  (response) => {
    return response && response.result;
  },
  (err) => Promise.reject(err)
);

const cx = classNames.bind(styles);
const {RangePicker} = DatePicker;
const MAX_DAYS = 30; // 最大可选天数
const MAX_RANGE_DAYS = 7; // 最大可选区间天数

const CustomBox: React.FC<CustomBoxProps> = ({title, children}) => {
  return (
    <Col>
      <div className={styles['custom-box']}>
        <div className={styles['custom-box-title']}>
          <span>{title}</span>
        </div>
        <div className="custom-box-content">{children}</div>
      </div>
    </Col>
  );
};

const MonitorTab: React.FC<{detail: any}> = ({detail}) => {
  const {currentRegion} = useRegion();
  const {userId} = useFrameworkContext();
  const [form] = Form.useForm();
  const {privileges = []} = detail || {};
  const canManage = privileges.includes(Privilege.Manage);

  const [metricsList, setMetricsList] = useState<metric[]>([]); // 指标类型
  const [metricsTypeOptions, setMetricsTypeOptions] = useState([]);
  const [monitorObjectOptions, setMonitorObjectOptions] = useState([]);
  const [metricsShowList, setMetricsShowList] = useState<metric[]>([]); // 展示数据
  const [resourceId, setResourceId] = useState<string>('');

  const timeRange = Form.useWatch('timeRange', form);
  const monitorType = Form.useWatch('monitorType', form);
  const monitorLevel = Form.useWatch('monitorLevel', form);
  const monitorObject = Form.useWatch('monitorObject', form);
  const singleRowNum = Form.useWatch('singleRowNum', form);
  const statistics = Form.useWatch('statistics', form);

  // 获取指标列表
  const {run: getMetrics, loading} = useRequest(getBasedInstanceMetrics, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        setMetricsList(res.result.metrics);

        // 生成指标类型下拉框选项
        const options = res.result.metrics.reduce((acc, item) => {
          if (!acc.some((entry) => entry.value === item.type)) {
            acc.push({value: item.type, label: item.cnType});
          }
          return acc;
        }, []);
        setMetricsTypeOptions(options);

        // 设置指标类型默认值
        form.setFieldsValue({monitorType: [options[0]?.value]});
      }
    }
  });

  // 获取监控对象列表
  const {run: getMonitorObject} = useRequest(getComputeMetricsCceNodes, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        // 生成监控对象下拉框选项
        const options = res.result.cceNodes.map((item) => ({
          label: item.cceInstanceID,
          value: item.cceInstanceID
        }));
        setMonitorObjectOptions(options);

        // 设置监控对象默认值
        form.setFieldsValue({
          monitorObject: [options[0]?.value]
        });
      }
    }
  });

  useEffect(() => {
    if (detail) {
      getMetrics({engine: detail?.engine});
      getMonitorObject({computeInstanceId: detail?.computeId});
    }
  }, [detail, getMetrics, getMonitorObject]);

  useEffect(() => {
    setMetricsShowList([]); // 先清空
    setTimeout(() => {
      const newMetricsShowList = metricsList.filter(
        (metric) => monitorType.includes(metric.type) && monitorLevel === metric.level
      );
      setMetricsShowList(newMetricsShowList); // 再更新为新值
    }, 0);
  }, [monitorType, monitorLevel, monitorObject, metricsList, timeRange, statistics]);

  useEffect(() => {
    const computeId = `ComputeId:${detail?.computeId}`;
    const cceInstanceId = monitorObject?.map((id) => `${computeId};CceInstanceId:${id}`).join(',');
    setResourceId(monitorLevel === EMonitorLevelType.COMPUTE ? computeId : cceInstanceId);
  }, [detail?.computeId, monitorLevel, monitorObject]);

  const onRefresh = () => {
    form.setFieldsValue({
      timeRange: [moment().subtract(1, 'hour'), moment()]
    });
  };

  return (
    <>
      <div className={styles['monitor-container']}>
        <Form
          form={form}
          name="baseForm"
          className={styles['form-container']}
          labelWidth={60}
          initialValues={{
            timeRange: [moment().subtract(1, 'hour'), moment()],
            monitorType: [],
            monitorLevel: EMonitorLevelType.NODE,
            monitorObject: [],
            statistics: 'average',
            singleRowNum: '2',
            chartLink: true
          }}
        >
          <Row gutter={8}>
            <Col flex="100 0 340px">
              <Form.Item name="timeRange">
                <RangePicker
                  showTime
                  allowClear={false}
                  ranges={{
                    近1小时: [moment().subtract(1, 'hour'), moment()],
                    近一天: [moment().subtract(1, 'day'), moment()],
                    近七天: [moment().subtract(7, 'day'), moment()]
                  }}
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{width: '100%'}}
                  disabledDate={(current) => {
                    const today = moment();
                    return (
                      current.isBefore(today.clone().subtract(MAX_DAYS, 'days'), 'day') || // 禁用超过 30 天前的日期
                      current.isAfter(today.clone().add(0, 'days'), 'day')
                    );
                  }}
                  onCalendarChange={(dates) => {
                    if (dates && dates[0] && dates[1]) {
                      const rangeInDays = dates[1].diff(dates[0], 'days');
                      if (rangeInDays > MAX_RANGE_DAYS) {
                        dates[1] = dates[0].clone().add(MAX_RANGE_DAYS, 'days');
                      }
                    }
                  }}
                />
              </Form.Item>
            </Col>

            <Col flex="1 0 50px" className="flex justify-end pb-4">
              <RefreshButton onClick={onRefresh} />
              <AuthComponents isAuth={canManage} tooltipType={TooltipType.Resource}>
                <Button
                  onClick={() => {
                    window.open('/bcm/#/bcm/alarm/rule/list', '_blank');
                  }}
                  className="ml-2"
                >
                  配置报警策略
                  <IconSvg type="open" size={14} />
                </Button>
              </AuthComponents>
            </Col>
          </Row>

          <Row gutter={12}>
            <CustomBox title="指标类型">
              <Form.Item name="monitorType" style={{marginBottom: 0}}>
                <Select
                  options={metricsTypeOptions}
                  placeholder="请选择指标类型"
                  style={{width: 200}}
                  bordered={false}
                  maxTagCount={'responsive'}
                  mode="multiple"
                  showAllSelected
                />
              </Form.Item>
            </CustomBox>

            <CustomBox title="监控级别">
              <Form.Item name="monitorLevel" style={{marginBottom: 0}}>
                <Select
                  options={[
                    {value: EMonitorLevelType.COMPUTE, label: '实例'},
                    {value: EMonitorLevelType.NODE, label: '节点'}
                  ]}
                  bordered={false}
                />
              </Form.Item>
            </CustomBox>

            {monitorLevel === EMonitorLevelType.NODE && (
              <CustomBox title="监控对象">
                <Form.Item name="monitorObject" style={{marginBottom: 0}}>
                  <Select
                    mode="multiple"
                    placeholder="请选择监控对象"
                    style={{width: 210}}
                    options={monitorObjectOptions}
                    bordered={false}
                    showAllSelected
                    maxTagCount={'responsive'}
                  />
                </Form.Item>
              </CustomBox>
            )}

            <CustomBox title="性能指标">
              <Form.Item name="statistics" style={{marginBottom: 0}}>
                <Select
                  options={[
                    {value: 'average', label: '平均值'},
                    {value: 'maximum', label: '最大值'},
                    {value: 'minimum', label: '最小值'}
                  ]}
                  bordered={false}
                />
              </Form.Item>
            </CustomBox>

            <CustomBox title="单行展示数">
              <Form.Item name="singleRowNum" style={{marginBottom: 0}}>
                <Select
                  options={[
                    {value: '2', label: '2个'},
                    {value: '3', label: '3个'},
                    {value: '4', label: '4个'}
                  ]}
                  bordered={false}
                />
              </Form.Item>
            </CustomBox>

            {/* <Form.Item name="chartLink" label="图表联动">
              <Switch />
            </Form.Item> */}
          </Row>
        </Form>
        <Row gutter={[16, 16]} className="mt-4">
          {metricsShowList.map((item, index) => (
            <Col key={index} span={24 / singleRowNum}>
              <div>
                <BcmChartPanel
                  title={item.cnName}
                  isAggrMetric={false}
                  showbigable={true}
                  scope={'BCE_EDAP'}
                  width={'auto'}
                  height={250}
                  metrics={item.name}
                  sdk={
                    new BcmSDK({
                      client: bcmRequest,
                      context: {
                        getUserId: () => userId,
                        getCurrentRegion: () => currentRegion.id
                      }
                    })
                  }
                  connect-nulls={false}
                  resourceId={resourceId}
                  cycle={60}
                  precision={3}
                  unit={item.unit}
                  options={{}}
                  withFilter={false}
                  region={currentRegion.id}
                  dateRange={`${timeRange[0].unix()}, ${timeRange[1].unix()}`}
                  statistics={statistics}
                  period={60}
                  bitUnit={1000}
                />
              </div>
            </Col>
          ))}
        </Row>
      </div>
    </>
  );
};

export default MonitorTab;
