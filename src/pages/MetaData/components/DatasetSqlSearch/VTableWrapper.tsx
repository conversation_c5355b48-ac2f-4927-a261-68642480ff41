/**
 * VTable 封装组件
 * 基于 @visactor/react-vtable 实现，支持大量行列数据的高性能渲染
 * 兼容 acud Table 的 API
 * 用来替换 src/pages/MetaData/components/DatasetSqlSearch/index.tsx 中的 acud Table
 */
import {ListTable} from '@visactor/react-vtable';
import type {ListTableConstructorOptions} from '@visactor/vtable';
import * as VTable from '@visactor/vtable';
import {Loading} from 'acud';
import type {TableProps} from 'acud/lib/table';
import React, {useEffect, useMemo, useState} from 'react';

interface VTableWrapperProps extends Omit<TableProps<any>, 'columns'> {
  columns: any[];
  dataSource: any[];
  scroll?: {
    x?: number | string;
    y?: number | string;
  };
  onRow?: (
    record: any,
    index: number
  ) => {
    onClick?: (event: any) => void;
  };
  loading?: boolean;
}

const VTableWrapper: React.FC<VTableWrapperProps> = (props) => {
  const {columns = [], dataSource = [], scroll, onRow, loading = false, ...restProps} = props;

  // 提取 React 元素中的文本内容
  const extractTextFromReactElement = (element: any): string => {
    if (typeof element === 'string' || typeof element === 'number') {
      return String(element);
    }

    if (React.isValidElement(element)) {
      const props = element.props as any;

      // 跳过 br 等空元素
      if (element.type === 'br') {
        return ' ';
      }

      if (props && props.children) {
        if (Array.isArray(props.children)) {
          return props.children
            .map((child: any) => {
              if (typeof child === 'string') {
                return child.trim();
              }
              if (typeof child === 'number') {
                return String(child);
              }
              if (React.isValidElement(child)) {
                return extractTextFromReactElement(child);
              }
              return '';
            })
            .filter((text: string) => text && text.trim())
            .join(' ');
        }
        return extractTextFromReactElement(props.children);
      }
    }

    return '';
  };

  // 将 acud Table 的 columns 转换为 VTable 的 columns
  const vtableColumns = useMemo(() => {
    const result = columns.map((col) => {
      // 处理 title（可能是 React 元素）
      let titleText = col.title;
      if (React.isValidElement(col.title)) {
        titleText = extractTextFromReactElement(col.title);
      }

      // 处理 width（可能是字符串 '200px'）
      let widthValue = col.width || 200;
      if (typeof widthValue === 'string') {
        widthValue = parseInt(widthValue, 10) || 200;
      }

      const fieldName = col.dataIndex || col.key;

      const vtableCol: any = {
        field: fieldName,
        title: titleText || fieldName,
        width: widthValue,
        sort: false,
        // 添加格式化函数，确保数据正确显示
        formatter: (args: any) => {
          const {cellValue} = args;
          if (cellValue === null || cellValue === undefined) {
            return '';
          }
          // 如果是对象，转换为 JSON 字符串（调试用）
          if (typeof cellValue === 'object') {
            return JSON.stringify(cellValue);
          }
          return String(cellValue);
        },
        headerStyle: {
          bgColor: '#F8FAFC',
          fontSize: 12,
          borderColor: '#E8E8E8',
          color: '#333',
          fontWeight: 500
        },
        style: {
          bgColor: '#FFF',
          fontSize: 12,
          borderColor: '#E8E8E8',
          color: '#333',
          padding: [16, 16, 16, 16]
        }
      };

      // 处理自定义渲染
      if (col.render) {
        // 检查是否是图片列（通过检查 render 函数返回的内容）
        const testValue = 'data:image/png;base64,test';
        const testRecord = {};
        const testResult = col.render(testValue, testRecord, 0);

        // 如果 render 返回的是包含 img 标签的 React 元素，使用图片类型
        if (React.isValidElement(testResult) && testResult.type === 'div') {
          const children = (testResult.props as any).children;
          if (React.isValidElement(children) && children.type === 'img') {
            // 图片列：使用 VTable 内置的 image 类型
            delete vtableCol.formatter;

            // 设置为图片类型
            vtableCol.cellType = 'image';

            // 图片样式配置
            vtableCol.style = {
              ...vtableCol.style,
              padding: [3, 8, 3, 8] // 上下 3px，左右 8px
            };

            // 设置图片显示属性 - 限制为 48x48
            vtableCol.keepAspectRatio = true;
            vtableCol.imageAutoSizing = false;

            // 直接在列配置中设置图片尺寸
            (vtableCol as any).icon = {
              type: 'image',
              width: 48,
              height: 48,
              positionType: 'contentLeft',
              marginRight: 0,
              marginLeft: 0
            };

            console.log('检测到图片列:', fieldName);
            return vtableCol;
          }
        }

        // 普通自定义渲染：VTable 会自动显示原始数据
        // formatter 已经处理了数据格式化
      }

      return vtableCol;
    });

    // 调试：打印转换后的 columns
    console.log('VTable columns:', result.slice(0, 3));

    return result;
  }, [columns]);

  // 将 dataSource 转换为 VTable 的 records
  const vtableRecords = useMemo(() => {
    const result = dataSource.map((record, index) => {
      // 添加一个内部索引，用于行点击事件
      return {
        ...record,
        __index: index
      };
    });

    // 调试：打印转换后的 records
    console.log('VTable records:', result.slice(0, 2));
    console.log('First record keys:', result[0] ? Object.keys(result[0]).slice(0, 10) : []);

    return result;
  }, [dataSource]);

  // VTable 配置选项
  const options: ListTableConstructorOptions = useMemo(() => {
    return {
      columns: vtableColumns,
      records: vtableRecords,
      widthMode: 'standard',
      heightMode: 'autoHeight',
      autoWrapText: false,
      // 主题配置
      theme: VTable.themes.DEFAULT.extends({
        underlayBackgroundColor: '#FFF',
        scrollStyle: {
          visible: 'always',
          scrollSliderColor: 'rgba(0, 0, 0, 0.2)',
          scrollSliderCornerRadius: 4,
          scrollRailColor: 'rgba(0, 0, 0, 0.05)',
          hoverOn: true,
          barToSide: false
        },
        defaultStyle: {
          borderLineWidth: 1,
          borderColor: '#E8E8E8',
          color: '#333',
          fontSize: 12,
          fontFamily: 'PingFang SC, Microsoft YaHei, Arial, sans-serif'
        },
        headerStyle: {
          bgColor: '#F8FAFC',
          color: '#333',
          fontSize: 12,
          fontWeight: 500,
          borderColor: '#E8E8E8'
        },
        bodyStyle: {
          bgColor: '#FFF',
          color: '#333',
          fontSize: 12,
          borderColor: '#E8E8E8',
          hover: {
            cellBgColor: '#F5F5F5'
          }
        }
      }),
      // 冻结列配置（可选）
      frozenColCount: 0,
      // 行高
      defaultRowHeight: 54,
      // 列宽模式
      defaultColWidth: 200
    };
  }, [vtableColumns, vtableRecords]);

  // 计算表格高度
  const tableHeight = useMemo(() => {
    if (scroll?.y) {
      // 如果是百分比字符串（如 '100%'），返回 '100%'
      if (typeof scroll.y === 'string' && scroll.y.includes('%')) {
        return scroll.y;
      }
      // 如果是数字或数字字符串，返回数字
      return typeof scroll.y === 'number' ? scroll.y : parseInt(String(scroll.y), 10);
    }
    // 默认高度：头部 54px + 10 行数据
    return 54 + Math.min(dataSource.length, 10) * 54;
  }, [scroll, dataSource.length]);

  // 处理行点击事件
  const handleClick = (args: any) => {
    if (onRow) {
      const {row, col, targetCell} = args;
      const record = vtableRecords[row - 1]; // row 从 1 开始，0 是表头
      if (record) {
        const rowProps = onRow(record, record.__index);
        if (rowProps?.onClick) {
          rowProps.onClick(args);
        }
      }
    }
  };

  // 监听屏幕高度变化
  const [screenHeight, setScreenHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      setScreenHeight(window.innerHeight);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return loading ? (
    <Loading loading>
      <div style={{height: `${screenHeight - 100}px`}}></div>
    </Loading>
  ) : (
    <ListTable
      option={options}
      height={typeof tableHeight === 'string' ? '100%' : tableHeight}
      onClickCell={handleClick}
    />
  );
};

export default VTableWrapper;
