import React, {useRef, useCallback, useEffect, useState} from 'react';
import {useVirtualizer} from '@tanstack/react-virtual';
import classNames from 'classnames/bind';
import styles from './index.module.less';
const cx = classNames.bind(styles);

export interface LogItem {
  id: string | number;
  content: string;
  timestamp?: string;
  level?: string;
}

export interface LogVirtualScrollProps {
  logs: LogItem[];
  height?: number;
  estimateSize?: number;
  loadMore?: () => void;
  hasNextPage?: boolean;
  isLoading?: boolean;
  className?: string;
  itemClassName?: string;
  onItemClick?: (item: LogItem) => void;
  showTimestamp?: boolean;
  wrapLines?: boolean;
}

const LogVirtualScroll: React.FC<LogVirtualScrollProps> = ({
  logs,
  height = '100%',
  estimateSize = 50,
  loadMore,
  hasNextPage = false,
  isLoading = false,
  className,
  itemClassName,
  onItemClick,
  showTimestamp = true,
  wrapLines = true
}) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [itemHeightCache, setItemHeightCache] = useState<Map<number, number>>(new Map());

  const virtualizer = useVirtualizer({
    count: logs.length,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback(
      (index: number) => {
        return itemHeightCache.get(index) || estimateSize;
      },
      [itemHeightCache, estimateSize]
    ),
    measureElement: useCallback((element: HTMLElement) => {
      const index = Number(element.getAttribute('data-index'));
      const measuredHeight = element.getBoundingClientRect().height;

      setItemHeightCache((prev) => {
        const newCache = new Map(prev);
        newCache.set(index, measuredHeight);
        return newCache;
      });

      return measuredHeight;
    }, []),
    overscan: 5
  });

  const items = virtualizer.getVirtualItems();

  // 监听滚动事件，当接近底部时触发加载更多
  useEffect(() => {
    const scrollElement = parentRef.current;
    if (!scrollElement || !loadMore || !hasNextPage) return;

    const handleScroll = () => {
      const {scrollTop, scrollHeight, clientHeight} = scrollElement;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      // 当滚动到90%时触发加载更多
      if (scrollPercentage > 0.9 && !isLoading && hasNextPage) {
        loadMore();
      }
    };

    scrollElement.addEventListener('scroll', handleScroll);
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, [loadMore, hasNextPage, isLoading]);

  // 备用触发机制：基于可见项索引
  useEffect(() => {
    const visibleItems = virtualizer.getVirtualItems();
    const [lastItem] = [...visibleItems].reverse();

    if (!lastItem || !loadMore || isLoading || !hasNextPage) return;

    // 当最后一个可见项接近列表末尾时触发加载
    if (lastItem.index >= logs.length - 10) {
      loadMore();
    }
  }, [hasNextPage, loadMore, isLoading, logs.length, virtualizer]);

  const getLogLevelClass = (level?: string) => {
    switch (level) {
      case 'INFO':
        return styles.logInfo;
      case 'WARN':
        return styles.logWarn;
      case 'ERROR':
        return styles.logError;
      default:
        return styles.logInfo;
    }
  };

  const renderLogContent = (content: string) => {
    if (wrapLines) {
      return content;
    }
    return content.replace(/\n/g, ' ');
  };

  return (
    <div ref={parentRef} className={cx('log-virtual-scroll', className)} style={{height: height}}>
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {items.map((virtualItem) => {
          const log = logs[virtualItem.index];

          return (
            <div
              key={virtualItem.key}
              data-index={virtualItem.index}
              ref={virtualizer.measureElement}
              className={cx('log-item', getLogLevelClass(log.level), itemClassName, {
                [styles.clickable]: !!onItemClick,
                [styles.wrapped]: wrapLines
              })}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`
              }}
              onClick={() => onItemClick?.(log)}
            >
              <div className={cx('log-item-content')}>
                <div className={cx('log-line-number')}>{virtualItem.index + 1}</div>
                <div className={cx('log-item-line')}>
                  <div className={cx('log-item-info')}>
                    {log.level && (
                      <span className={cx('level', `level-${log.level}`)}>{log.level.toUpperCase()}</span>
                    )}
                    {showTimestamp && log.timestamp && (
                      <span className={cx('timestamp')}>{log.timestamp}</span>
                    )}
                  </div>
                  <span className={cx('content')}>{renderLogContent(log.content)}</span>
                </div>
              </div>
            </div>
          );
        })}

        {isLoading && <div className={cx('loading-indicator')}>加载中...</div>}
      </div>
    </div>
  );
};

export default LogVirtualScroll;
