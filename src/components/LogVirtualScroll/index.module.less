.log-virtual-scroll {
  overflow: auto;

  // &::-webkit-scrollbar {
  //   width: 8px;
  //   height: 8px;
  // }

  // &::-webkit-scrollbar-track {
  //   background: #f5f5f5;
  //   border-radius: 4px;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: #c1c1c1;
  //   border-radius: 4px;

  //   &:hover {
  //     background: #a8a8a8;
  //   }
  // }
}

.log-item {
  font-size: 13px;
  background-color: #fff;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  &.clickable {
    cursor: pointer;

    &:hover {
      background-color: #e8f4fd;
    }
  }

  &.wrapped {
    white-space: pre-wrap;
    word-break: break-word;
  }

  &:not(.wrapped) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.log-item-content {
  display: flex;
  min-height: 16px;
  .log-line-number {
    padding-top: 16px;
    width: 50px;
    flex: none;
    border-right: 1px solid #eaeef2;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  .log-item-line {
    padding-top: 16px;
    padding: 16px 16px 0 16px;
    flex: auto;
    min-width: 0;
    // margin-bottom: 16px;
    color: #151b26;
    .log-item-info {
      margin-bottom: 8px;
    }
  }
}

.timestamp {
  color: #818999;
  font-size: 12px;
  min-width: 80px;
  flex-shrink: 0;
  padding-top: 1px;
}

.level {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  font-size: 12px;
  padding: 0 8px;
  border-radius: 4px;
  text-align: center;
  height: 20px;
  border: 0.5px solid transparent;
  margin-right: 12px;

  &.level-INFO {
    color: #0279c0;
    background-color: #f0faff;
    border-color: #b2e5ff;
  }

  &.level-WARN {
    color: #faaf19;
    background-color: #fff9e6;
    border-color: #ffe299;
  }

  &.level-ERROR {
    color: #fa423c;
    background-color: #ffeceb;
    border-color: #ffc3bd;
  }
}

.content {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.log-info {
  border-left: 3px solid #1890ff;
}

.log-warn {
  border-left: 3px solid #faad14;
  background-color: rgba(250, 173, 20, 0.02);
}

.log-error {
  border-left: 3px solid #f5222d;
  background-color: rgba(245, 34, 45, 0.02);
}

.log-debug {
  border-left: 3px solid #722ed1;
  background-color: rgba(114, 46, 209, 0.02);
}

.loading-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  color: #666;
  font-size: 13px;
  border-top: 1px solid #f0f0f0;
}
