import React, {useState, useEffect} from 'react';
import LogVirtualScroll, {LogItem} from './index';

const LogVirtualScrollDemo: React.FC = () => {
  const [logs, setLogs] = useState<LogItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);

  // 模拟生成日志数据
  const generateLogs = (start: number, count: number): LogItem[] => {
    const levels: Array<LogItem['level']> = ['info', 'warn', 'error', 'debug'];
    const sampleMessages = [
      'Application started successfully on port 8080Error parsing configuration file config.yml line 42: invalid syntaxError parsing configuration file config.yml line 42: invalid syntaxError parsing configuration file config.yml line 42: invalid syntax',
      'Database connection established',
      'User authentication failed for user: admin',
      'Memory usage: 45% (2.1GB/4.6GB)',
      'Processing batch job #12345',
      'Cache miss for key: user_session_abc123',
      'HTTP request: GET /api/users - 200ms',
      'Background task completed: data_sync',
      'Warning: High CPU usage detected (85%)',
      'Error parsing configuration file config.yml line 42: invalid syntax',
      'New user registration: <EMAIL>',
      'Scheduled backup started at 02:00 AM',
      'SQL query executed in 150ms: SELECT * FROM users WHERE active = true',
      'WebSocket connection closed unexpectedly',
      'File upload completed: document.pdf (2.5MB)',
      'Rate limit exceeded for IP: *************',
      'Service health check passed for all endpoints',
      'Database migration completed: version 1.2.3',
      'JWT token expired for session: xyz789',
      'Log rotation completed: 5 files archived'
    ];

    return Array.from({length: count}, (_, i) => ({
      id: start + i,
      content: `[${start + i}] ${sampleMessages[Math.floor(Math.random() * sampleMessages.length)]}`,
      timestamp: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
      level: levels[Math.floor(Math.random() * levels.length)]
    }));
  };

  // 模拟滚动加载
  const loadMore = () => {
    if (isLoading || !hasNextPage) return;

    console.log('Loading more logs...', logs.length);
    setIsLoading(true);

    // 模拟网络延迟
    setTimeout(() => {
      const newLogs = generateLogs(logs.length, 50);
      setLogs((prev) => [...prev, ...newLogs]);
      setIsLoading(false);

      // 模拟达到数据末尾
      if (logs.length + 50 >= 1000) {
        setHasNextPage(false);
      }
    }, 1000);
  };

  // 初始化数据
  useEffect(() => {
    const initialLogs = generateLogs(0, 100);
    setLogs(initialLogs);
  }, []);

  return (
    <div style={{height: '100%'}}>
      <LogVirtualScroll
        logs={logs}
        estimateSize={60}
        loadMore={loadMore}
        hasNextPage={hasNextPage}
        isLoading={isLoading}
        showTimestamp={true}
        wrapLines={true}
        onItemClick={(item) => {
          console.log('Clicked log item:', item);
        }}
      />
    </div>
  );
};

export default LogVirtualScrollDemo;
