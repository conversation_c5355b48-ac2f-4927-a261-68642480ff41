import React from 'react';
import cx from 'classnames';
import './index.less';

export default function ActionBtn(props: {
  icon: React.ReactNode;
  disabled?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  children?: React.ReactNode;
  className?: string;
  type?: 'default' | 'dark';
}) {
  const {icon, disabled, onClick, children, className, type = 'default'} = props;
  const clickHandle = (e: React.MouseEvent) => {
    if (disabled) return;
    onClick?.(e);
  };

  return (
    <div
      className={cx('db-action-btn-wrap', {disabled, 'no-children': !children}, className, type)}
      onClick={clickHandle}
    >
      <div className={cx('db-action-btn-icon')}>{icon}</div>
      {children && <div className={cx('db-action-btn-text')}>{children}</div>}
    </div>
  );
}

ActionBtn.displayName = 'ActionBtn';
// 解决外层tooltip组件不生效问题
ActionBtn.__ACUD_BUTTON = true;
