### 使用 reactflow 的工作流

- ButtonList 定义按钮列表
- Node 定义节点 X6ShapeTypeEnum TASK OPERATOR GROUP ROOT_GROUP
- Edge 定义边 X6ShapeTypeEnum TASK_EDGE OPERATOR_EDGE DEP_INPUT_EDGE DEP_OUTPUT_EDGE
- type 定义类型
- utils 工具类
  - layout 布局 工具类
  - translate 将 json 格式的数据 转换为 Reactflow 格式
  - config 常用配置常量

### translate

- jsonToWorkflow 将 json 格式的数据 转换为 nodes edges 平铺模式
- autoGroup nodes edges 根据层级 添加不同分组 id的规范 workspaceId + jobId + taskId + workspaceId+ jobId + taskId
  - workspaceId + jobId 根分组
    - workspaceId + jobId + taskId 默认展示的工作流节点
    - workspaceId + jobId + taskId 默认展示的工作流节点
      - workspaceId + jobId + taskId + workspaceId + jobId 工作流展开的子分组
- dealWorkflowNodeDependency 根据 jobDependencyReverse 节点 给工作流添加 关联的前后工作流列表，这里会给工作流添加 未完整展开的工作流节点

##### DAG

- 初始化

  1. jsonToWorkflow
  2. autoGroup
  3. dealWorkflowNodeDependency
