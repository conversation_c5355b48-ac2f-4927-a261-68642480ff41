import {X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {type EdgeTypes} from '@xyflow/react';
import DepInputEdge from './DepInputEdge';
import DepOutputEdge from './DepOutputEdge';
import OperatorEdge from './OperatorEdge';
import TaskEdge from './TaskEdge';

export const edgeTypes = {
  // Add your custom edge types here!
  [X6ShapeTypeEnum.TASK_EDGE]: TaskEdge,
  [X6ShapeTypeEnum.OPERATOR_EDGE]: OperatorEdge,
  [X6ShapeTypeEnum.DEP_INPUT_EDGE]: DepInputEdge,
  [X6ShapeTypeEnum.DEP_OUTPUT_EDGE]: DepOutputEdge
} satisfies EdgeTypes;
