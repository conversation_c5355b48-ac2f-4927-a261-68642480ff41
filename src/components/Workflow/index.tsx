import {addEdge, Background, MarkerType, MiniMap, ReactFlow} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import {edgeTypes} from '@components/Workflow/Edge';
import {nodeTypes} from '@components/Workflow/Node';
import {AppNode} from '@components/Workflow/types/types';
import {WorkflowMouseType, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {onEdgesChange, onNodeDragChange, onNodesChange, setEdges} from '@store/workflowSlice';
import {useMemoizedFn} from 'ahooks';
import React from 'react';
import {useDispatch, useSelector} from 'react-redux';
import ButtonList from './ButtonList';
import './index.less';

const Workflow: React.FC = () => {
  const {nodes, edges, config} = useSelector((state: IAppState) => state.workflowNewSlice);

  const dispatch = useDispatch();

  //  拖动节点
  const onNodeDrag = useMemoizedFn((event: React.MouseEvent, node: AppNode) => {
    // 处理父节点宽度不足 若没有父节点 不处理
    if (!node.parentId) {
      return;
    }

    dispatch(onNodeDragChange(node));
  });
  // 拖动结束时触发
  const onNodeDragStop = useMemoizedFn((event: React.MouseEvent, node: AppNode) => {
    onNodeDrag(event, node);
  });
  // 节点变化
  const onNodesChangeFn = useMemoizedFn((changes) => {
    console.log('onNodesChange', changes);
    dispatch(onNodesChange(changes));
  });

  const dealMiniNodeColor = (node: AppNode) => {
    switch (node.type) {
      case X6ShapeTypeEnum.GROUP:
        return '#e8e9ff';
      case X6ShapeTypeEnum.ROOT_GROUP:
        return 'transparent';
      default:
        return '#ccc';
    }
  };

  return (
    <>
      <ReactFlow
        panOnScroll={config.mode === WorkflowMouseType.TOUCHPAD}
        zoomOnScroll={config.mode === WorkflowMouseType.MOUSE}
        zoomOnPinch={config.mode === WorkflowMouseType.TOUCHPAD}
        panOnDrag={config.mode === WorkflowMouseType.MOUSE}
        className="workflow-react-flow"
        nodes={nodes}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChangeFn}
        onNodeDrag={onNodeDrag}
        onNodeDragStop={onNodeDragStop}
        edges={edges}
        edgeTypes={edgeTypes}
        onEdgesChange={(changes) => dispatch(onEdgesChange(changes))}
        onConnect={(connection) => dispatch(setEdges(addEdge(connection, edges)))}
        fitView
        elementsSelectable={false}
        proOptions={{hideAttribution: true}} // 隐藏右下角水印
        defaultEdgeOptions={{
          markerEnd: {type: MarkerType.Arrow},
          zIndex: 100
        }}
      >
        <Background gap={12} size={2} color="#D4D6D9" bgColor="#F7F8FB" />
        {config.miniMap && (
          <MiniMap
            nodeColor={(node) => dealMiniNodeColor(node)}
            pannable={true}
            className="workflow-mini-map"
            style={{width: 230, height: 126}}
          />
        )}
        <ButtonList />
      </ReactFlow>
    </>
  );
};

export default React.memo(Workflow);
