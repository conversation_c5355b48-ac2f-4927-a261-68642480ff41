.workflow-react-flow {
  .workflow-mini-map {
    position: absolute;
    padding: 4px;
    bottom: 66px;
    right: 50%;
    transform: translateX(42px);
    margin: 0;

    border-radius: 8px;
    border: 0.6px solid #eaeef2;
    background: #fff;
    box-shadow: 0 2.4px 12px 0 rgba(0, 25, 126, 0.08);
    svg {
      border-radius: 8px;
    }
  }
  .react-flow__handle {
    border-radius: 8px;
    border: 1px solid #fff;
    background: #818999;
  }
}
.react-flow__edges svg {
  z-index: 1 !important;
}
