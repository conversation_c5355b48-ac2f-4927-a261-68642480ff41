.react-flow-root-group-node {
  min-width: 320px;
  min-height: 52px;

  width: 100%;
  height: 100%;
  flex-shrink: 0;
  border-radius: 8px;
  border: 0.5px solid #e4e7eb;
  background: #fff;
  box-shadow: 0 2px 10px 0 rgba(0, 25, 126, 0.08);
  z-index: 2;
  .node-header {
    background-color: #fff;
    border-radius: 8px 8px 0 0;
    background-image:
      linear-gradient(90deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 43.59%),
      linear-gradient(180deg, rgba(178, 185, 255, 0.16) 0%, rgba(255, 255, 255, 0) 100%);

    width: 100%;
    height: 52px;
    display: flex;
    box-sizing: border-box;
    // justify-content: space-between;
    align-items: center;
    align-self: stretch;
    padding: 14px 16px;
    .title-text {
      color: #000;
      font-size: 13px;
      font-weight: 500;
      line-height: 20px;
      height: 20px;
      flex: 1;
    }
    .dependency-btn {
      flex: 0 0 52px;
    }
    .title-collapse {
      flex: 0 0 16px;
    }
  }
  .node-content {
    width: 100%;
    height: calc(100% - 52px);

    box-sizing: border-box;
    padding: 0 16px 16px 16px;

    .node-background {
      background-color: #f7f8fb; /* 灰色背景 */
      background-image: radial-gradient(#d4d6d9 1px, transparent 1px);
      background-size: 12px 12px; /* 点间距 */
      border-radius: 8px;
      height: 100%;
      width: 100%;
    }
  }
  .node-description {
    padding: 4px 0 0 16px;
    color: #818999;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    height: 18px;
  }
}
