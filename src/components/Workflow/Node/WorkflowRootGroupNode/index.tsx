import IconSvg from '@components/IconSvg';
import {WorkflowGroupNodeProps} from '@components/Workflow/types/types';
import {autoLayoutNodes} from '@components/Workflow/utils/layout/autoLayout';
import {showHideDependentGroup} from '@components/Workflow/utils/layout/autoSize';
import {setNodes} from '@store/workflowSlice';
import {Handle, NodeProps, Position, useReactFlow} from '@xyflow/react';
import {useMemoizedFn} from 'ahooks';
import React from 'react';
import {useDispatch} from 'react-redux';
import './index.less';

/**
 * 工作流根节点分组节点
 */
const WorkflowRootGroupNode: React.FC<NodeProps<WorkflowGroupNodeProps>> = ({id, data}) => {
  const dispatch = useDispatch();
  const reactFlow = useReactFlow();

  // 打开被依赖的工作流
  const openDependencyOutput = useMemoizedFn(async () => {
    let nodes = reactFlow.getNodes().map((item) => {
      if (item.id === id) {
        return {
          ...item,
          data: {
            ...item.data,
            showDependencyOutput: !item.data.showDependencyOutput
          }
        };
      }
      return item;
    });
    const edges = reactFlow.getEdges();
    nodes = showHideDependentGroup(
      nodes,
      data.dependencyList.map((item) => item.id),
      !data.showDependencyOutput
    );
    const node = await autoLayoutNodes(nodes, edges);
    dispatch(setNodes(node));
  });
  return (
    // We add this class to use the same styles as React Flow's default nodes.
    <div className="react-flow-root-group-node">
      <div className="node-header">
        <div className="title-text">{data.name}</div>

        {/* DAG  展示被哪些节点依赖 */}
        {data?.dependencyList && data.dependencyList.length > 0 && (
          <div
            className={`dependency-btn ${data.showDependencyOutput ? 'active' : ''} `}
            onClick={() => openDependencyOutput()}
          >
            <IconSvg fill="none" className="title-icon" size={16} type={'workflow-dependency-output'} />
            {data.dependencyList.length}
          </div>
        )}
      </div>

      {/* <div className="node-description">
        {'调度时间：'} {dealCronToStr(data.scheduleConf)}
      </div> */}
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
      <div className="node-content">
        <div className="node-background"></div>
      </div>
    </div>
  );
};

export default React.memo(WorkflowRootGroupNode);
