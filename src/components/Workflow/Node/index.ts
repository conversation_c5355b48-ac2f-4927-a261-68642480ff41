import type {NodeTypes} from '@xyflow/react';

import {X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import OperatorNode from './OperatorNode';
import TaskNode from './TaskNode';
import WorkflowGroupNode from './WorkflowGroupNode';
import WorkflowRootGroupNode from './WorkflowRootGroupNode';

// 自定义节点类型
export const nodeTypes = {
  [X6ShapeTypeEnum.TASK]: TaskNode,
  [X6ShapeTypeEnum.OPERATOR]: OperatorNode,
  [X6ShapeTypeEnum.GROUP]: WorkflowGroupNode,
  [X6ShapeTypeEnum.ROOT_GROUP]: WorkflowRootGroupNode
  // Add any of your custom nodes here!
} satisfies NodeTypes;
