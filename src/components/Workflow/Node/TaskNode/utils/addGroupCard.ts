// export const addGroupCard = (
//   nodes: Node[],
//   edges: Edge[],
//   jobDependencyList: IJobDependency[],
//   job: IJob
// ): {nodes: Node[]; edges: Edge[]} => {
//   const mapDep = new Map<string, IJobDependency[]>();
//   const setIds = new Set<string>();
//   // 前缀
//   const prefix = job.workspaceId + SPLIT_STR + job.jobId + SPLIT_STR;

//   const addNodes: Node[] = [];

//   // 避免重复添加
// };
