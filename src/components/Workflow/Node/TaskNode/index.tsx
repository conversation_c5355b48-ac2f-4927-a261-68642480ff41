import IconSvg from '@components/IconSvg';
import {autoLayoutNodes} from '@components/Workflow/utils/layout/autoLayout';
import {collapseNodeAndAutoSize, showHideDependentGroup} from '@components/Workflow/utils/layout/autoSize';
import {dealDependencyNode} from '@components/Workflow/utils/translate/addWorkflow';
import {checkNode} from '@components/Workflow/utils/translate/checkNode';
import {JobNodeTypeEnum, JobTaskType, SPLIT_STR, X6PageTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {onNodeCheckedChange, setEdges, setNodes} from '@store/workflowSlice';
import {Handle, NodeProps, Position, useReactFlow} from '@xyflow/react';
import {Checkbox} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {WorkflowNodeProps} from '../../types/types';
import './index.less';

/**
 * 普通任务节点
 */
const TaskNode: React.FC<NodeProps<WorkflowNodeProps>> = ({id, data}) => {
  const dispatch = useDispatch();
  const reactFlow = useReactFlow();
  const pageType = useSelector((state: IAppState) => state.workflowNewSlice.config.pageType);

  // 切换算子节点显示隐藏
  const showHiddenOperator = useMemoizedFn(async () => {
    const nodes = reactFlow.getNodes();
    const temNodes = collapseNodeAndAutoSize(nodes, id, !data?.showChild);
    const edges = reactFlow.getEdges();
    const node = await autoLayoutNodes(temNodes, edges);
    dispatch(setNodes(node));
  });

  // 展开依赖任务
  const openDependencyInput = useMemoizedFn(async () => {
    // 打开依赖任务
    // 1. 加载依赖工作流
    // 2. 找到依赖任务的子节点
    // 3. 添加到画布中

    const dependencyGroupId =
      id + SPLIT_STR + data.taskParam?.workspaceId + SPLIT_STR + data.taskParam?.jobId;

    const dependencyGroupNode = reactFlow.getNode(dependencyGroupId);

    let nodes;
    let edges = reactFlow.getEdges();
    // 如果已经加载了节点分组
    if (dependencyGroupNode) {
      nodes = reactFlow.getNodes().map((item) => {
        if (item.id === dependencyGroupId) {
          return {
            ...item,
            hidden: !dependencyGroupNode.hidden,
            showChild: false
          };
        }
        return item;
      });
    } else {
      const {nodes: newNodes, edges: newEdges} = await dealDependencyNode(id, data);

      nodes = [...reactFlow.getNodes(), ...newNodes];
      edges = [...reactFlow.getEdges(), ...newEdges];

      dispatch(setEdges(edges));
    }

    // 处理子节点隐藏逻辑
    const tmpNodes = collapseNodeAndAutoSize(nodes, dependencyGroupId, false);

    const node = await autoLayoutNodes(tmpNodes, edges);
    dispatch(setNodes(node));
  });

  // 打开被依赖的工作流
  const clickDependencyOutput = useMemoizedFn(async () => {
    const tmpNodes = reactFlow.getNodes().map((item) => ({
      ...item,
      data: {
        ...item.data,
        showDependencyOutput: item.id === id ? !data.showDependencyOutput : item?.data?.showDependencyOutput
      }
    }));
    const nodes = showHideDependentGroup(
      tmpNodes,
      data.dependencyList.map((item) => item.id),
      !data.showDependencyOutput
    );
    const edges = reactFlow.getEdges();
    const node = await autoLayoutNodes(nodes, edges);
    dispatch(setNodes(node));
  });

  // 勾选节点
  const handChecked = useMemoizedFn((checked: boolean) => {
    const nodes = reactFlow.getNodes();
    const ids = checkNode(nodes, id, checked);
    console.log('勾选节点', ids);
    dispatch(onNodeCheckedChange({ids, checked}));
  });
  return (
    // We add this class to use the same styles as React Flow's default nodes.
    <div className="react-flow-node-task">
      <div className="node-content">
        {/* 任务类型 */}
        <IconSvg color="white" className="title-icon" size={24} type={JobTaskType[data.type]?.icon} />
        <div className="title-text">{data.name}</div>

        {/*  只有算子节点 折叠按钮 */}
        {data.type === JobNodeTypeEnum.DATAFLOW_TASK &&
          pageType !== X6PageTypeEnum.JOB_FIX_DATA &&
          pageType !== X6PageTypeEnum.JOB_DAG && (
            <div
              className="title-collapse"
              onClick={() => {
                showHiddenOperator();
              }}
            >
              <IconSvg
                fill="none"
                color="#151B26"
                className="title-icon"
                size={16}
                type={!data?.showChild ? 'arrow-down' : 'arrow-up'}
              />
            </div>
          )}

        {/* DAG 展示 依赖了哪些节点 */}
        {data?.type === JobNodeTypeEnum.DEPENDENT_TASK &&
          data?.taskParam?.jobId &&
          (pageType === X6PageTypeEnum.JOB_FIX_DATA || pageType === X6PageTypeEnum.JOB_DAG) && (
            <div className="dependency-btn" onClick={() => openDependencyInput()}>
              <IconSvg fill="none" className="title-icon" size={16} type={'workflow-dependency-input'} />1
            </div>
          )}

        {/* DAG  展示被哪些节点依赖 */}
        {data?.dependencyList && data.dependencyList.length > 0 && (
          <div className="dependency-btn" onClick={() => clickDependencyOutput()}>
            <IconSvg fill="none" className="title-icon" size={16} type={'workflow-dependency-output'} />
            {data.dependencyList.length}
          </div>
        )}
        {/* 补数据 */}
        {pageType === X6PageTypeEnum.JOB_FIX_DATA && (
          <Checkbox
            checked={data.checked}
            onChange={(e) => handChecked(e.target.checked)}
            className="checkbox-node"
          />
        )}
      </div>
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </div>
  );
};

export default React.memo(TaskNode);
