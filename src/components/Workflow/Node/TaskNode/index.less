.react-flow-node-task {
  min-width: 320px;
  min-height: 52px;
  height: 100%;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px #00197e14;
  border: 0.5px solid #e4e7eb;
  background-color: #fff;
  z-index: 10;
  .node-content {
    background-image:
      linear-gradient(90deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 43.59%),
      linear-gradient(180deg, rgba(178, 185, 255, 0.16) 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 8px;
    padding: 14px 16px;
    display: flex;
    gap: 8px;
    align-items: center;
    .title-icon {
      flex: 0 0 24px;
      svg {
        border-radius: 5px;
      }
    }
    .title-text {
      flex: 1 1 auto;
      font-size: 13px;
      font-weight: 500;
      line-height: 24px;
    }
    .title-collapse {
      flex: 0 0 16px;
      .title-icon {
        border-radius: 4px;
      }
      :hover {
        cursor: pointer;
        background-color: #e4e8f0;
      }
    }
    .dependency-btn {
      display: flex;
      padding: 0 8px;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: #e8e9ff;
      height: 20px;
      width: 42px;
      &:hover {
        cursor: pointer;
        background-color: #e8e9ff;
        color: #333aff;
      }
      &:active {
        background: #c7c9ff;
      }
    }
  }
}
