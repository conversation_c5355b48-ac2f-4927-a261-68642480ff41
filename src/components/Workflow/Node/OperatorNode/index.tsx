import {Handle, NodeProps, Position} from '@xyflow/react';
import React from 'react';
import {WorkflowNodeProps} from '../../types/types';
import './index.less';

/**
 * 普通任务节点
 */
const OperatorNode: React.FC<NodeProps<WorkflowNodeProps>> = ({id, data}) => {
  return (
    <div className="react-flow-node-operator">
      <div className="title-text">{data.name}</div>

      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};

export default React.memo(OperatorNode);
