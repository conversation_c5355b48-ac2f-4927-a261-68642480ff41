.react-flow-group-node {
  min-width: 320px;
  min-height: 52px;

  width: 100%;
  height: 100%;
  background-color: #ebecff;
  border: 1px dashed #5c61ff;
  border-radius: 8px;
  box-shadow: 0 2px 10px 0 rgba(0, 25, 126, 0.08);
  border-radius: 8px;
  z-index: 2;
  .node-content {
    width: 100%;
    display: flex;
    box-sizing: border-box;
    // justify-content: space-between;
    align-items: center;
    align-self: stretch;
    height: 20px;
    padding: 14px 16px 0 16px;
    .title-text {
      color: #000;
      font-size: 13px;
      font-weight: 500;
      line-height: 20px;
      height: 20px;
      flex: 1;
    }
    .dependency-btn {
      flex: 0 0 52px;
    }
    .title-collapse {
      flex: 0 0 16px;
    }
  }
  .node-description {
    padding: 4px 0 0 16px;
    color: #818999;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    height: 18px;
  }
}
