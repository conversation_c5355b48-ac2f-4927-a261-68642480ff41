import {jobDependencyReverse} from '@api/workflow';
import IconSvg from '@components/IconSvg';
import {WorkflowGroupNodeProps} from '@components/Workflow/types/types';
import {autoLayoutNodes} from '@components/Workflow/utils/layout/autoLayout';
import {collapseNodeAndAutoSize, showHideDependentGroup} from '@components/Workflow/utils/layout/autoSize';
import {dealUnInitNode, dealWorkflowNodeDependency} from '@components/Workflow/utils/translate/addWorkflow';
import {dealCronToStr} from '@pages/JobWorkflow/tools';
import {setEdges, setNodes} from '@store/workflowSlice';
import {Handle, NodeProps, Position, useReactFlow} from '@xyflow/react';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useMemo} from 'react';
import {useDispatch} from 'react-redux';
import './index.less';

/**
 * 工作流分组节点
 */
const WorkflowGroupNode: React.FC<NodeProps<WorkflowGroupNodeProps>> = ({id, data}) => {
  const dispatch = useDispatch();
  const reactFlow = useReactFlow();

  // 获取 工作流依赖节点
  const {run: runDagDependency} = useRequest(jobDependencyReverse, {
    manual: true,
    onSuccess(data, params) {
      // console.log(data, params);

      const config = dealWorkflowNodeDependency(reactFlow.getNodes(), reactFlow.getEdges(), data.result, id);
      dispatch(setNodes(config.nodes));
      dispatch(setEdges(config.edges));
    }
  });

  // 切换节点显示隐藏
  const showHiddenNode = useMemoizedFn(async () => {
    let newNodes = reactFlow.getNodes();
    let newEdges = reactFlow.getEdges();
    // 如果没有初始化
    if (!data?.isInit) {
      const {nodes, edges, sourceNodeIs} = await dealUnInitNode(
        data?.workspaceId,
        data?.jobId,
        data.parentId,
        newNodes
      );

      newNodes = [...newNodes, ...nodes];
      newEdges = [...newEdges, ...edges];

      runDagDependency(data?.workspaceId, data?.jobId, sourceNodeIs, data?.version || -1);
      dispatch(setEdges(newEdges));
    } else {
      newNodes = collapseNodeAndAutoSize(newNodes, id, !data?.showDependencyOutput);
    }
    newNodes = await autoLayoutNodes(newNodes, newEdges);
    // 修改当前节点的显示隐藏状态
    newNodes = newNodes.map((item) => ({
      ...item,
      data: {
        ...item.data,
        showDependencyOutput: item.id === id ? !data?.showDependencyOutput : data?.showDependencyOutput
      }
    }));
    dispatch(setNodes(newNodes));
  });

  const scheduleTime = useMemo(() => {
    return dealCronToStr(data?.scheduleConf);
  }, [data?.scheduleConf]);

  // 打开被依赖的工作流
  const openDependencyOutput = useMemoizedFn(async () => {
    const nodes = showHideDependentGroup(
      reactFlow.getNodes(),
      data.dependencyList.map((item) => item.id),
      !data.showDependencyOutput
    );
    const edges = reactFlow.getEdges();
    const node = await autoLayoutNodes(nodes, edges);
    dispatch(setNodes(node));
  });

  return (
    // We add this class to use the same styles as React Flow's default nodes.
    <div className="react-flow-group-node">
      <div className="node-content">
        <div className="title-text">{data.name}</div>

        {/* DAG  展示被哪些节点依赖 */}
        {data?.dependencyList && data.dependencyList.length > 0 && (
          <div className="dependency-btn" onClick={() => openDependencyOutput()}>
            <IconSvg fill="none" className="title-icon" size={16} type={'workflow-dependency-output'} />
            {data.dependencyList.length}
          </div>
        )}

        {/*  折叠按钮 */}
        <div
          className="title-collapse"
          onClick={() => {
            showHiddenNode();
          }}
        >
          <IconSvg
            fill="none"
            color="#151B26"
            className="title-icon"
            size={16}
            type={!data?.showDependencyOutput ? 'arrow-down' : 'arrow-up'}
          />
        </div>
      </div>
      <div className="node-description">调度时间：{scheduleTime}</div>
      {/* <Handle type="target" position={Position.Left} /> */}
      <Handle type="source" position={Position.Right} />
    </div>
  );
};

export default React.memo(WorkflowGroupNode);
