.workflow-btn-list {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);

  z-index: 1000;
  display: inline-flex;
  padding: 4px;
  align-items: center;
  gap: 4px;
  padding: 4px;
  box-sizing: border-box;
  border-radius: 8px;
  border: 0.5px solid #eaeef2;
  background: #fff;
  box-shadow: 0 2px 10px 0 rgba(0, 25, 126, 0.08);
  height: 40px;
  // width: 394px;
  width: 280px;

  .btn-item {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: #fff;
    // 内容垂直居中
    display: flex;
    align-items: center;
    justify-content: center;
    .tooltip-content {
      // 内容垂直居中
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      border-radius: 6px;
    }
    &:hover {
      background: #f6f6ff;
      cursor: pointer;
    }
    // 点击伪类
    &:active {
      color: #333aff;
      background: #c7c9ff;
    }
    & .active {
      background: #c7c9ff;
    }
  }
  .btn-dropdown {
    display: flex;
    padding: 8px;
    box-sizing: border-box;
    align-items: center;
    gap: 4px;
    border-radius: 6px;
    border: 0.5px solid #eaeef2;
    background: #f2f5fa;
    height: 32px;
    width: 52px;
    .acud-dropdown-trigger {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  // 分割线
  .btn-item-spacer {
    width: 1px;
    height: 16px;
    background: #edeef5;
  }
  .btn-add button {
    width: 104px;
    border: none;
    background: #e8e9ff;
    color: #333aff;
    &:hover {
      background: #f6f6ff;
    }
    &:active {
      background: #c7c9ff;
    }
  }
}
.workflow-btn-dropdown-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
