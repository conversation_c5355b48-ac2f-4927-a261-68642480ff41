import useUrlState from '@ahooksjs/use-url-state';
import IconSvg from '@components/IconSvg';
import {JobDetailPageTypeEnum, WorkflowMouseType} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {onSetConfig, setNodes} from '@store/workflowSlice';
import {useReactFlow} from '@xyflow/react';
import {Dropdown, Menu, Tooltip} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useMemo, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {exitFullscreen, goToFullscreen} from '../utils';
import {autoLayoutNodes} from '../utils/layout/autoLayout';
import './index.less';

const ButtonList: React.FC = () => {
  const {zoomIn, zoomOut, fitView, getNodes, getEdges} = useReactFlow();
  const {config} = useSelector((state: IAppState) => state.workflowNewSlice);
  const [{type}] = useUrlState();
  const disabledCreate = useMemo(() => {
    return [JobDetailPageTypeEnum.JOB_DAG, JobDetailPageTypeEnum.JOB_FIX_DATA].includes(type);
  }, [type]);
  const dispatch = useDispatch();
  // 是否全屏
  const [fullScreenFlag, setFullScreenFlag] = useState<boolean>(false);

  // 自动布局
  const clickLayout = useMemoizedFn(async () => {
    const node = await autoLayoutNodes(getNodes(), getEdges());
    console.log('自动布局', node);
    dispatch(setNodes(node));
  });

  // 全屏
  const fullScreen = useMemoizedFn(() => {
    if (fullScreenFlag) {
      exitFullscreen();
    } else {
      goToFullscreen('workflow-box');
    }
    setFullScreenFlag(!fullScreenFlag);
  });
  // 小地图
  const miniMap = useMemoizedFn(() => {
    dispatch(onSetConfig({key: 'miniMap', value: !config.miniMap}));
  });

  const menu = useMemo(
    () => (
      <Menu style={{width: 120}} selectedKeys={[config.mode]}>
        <Menu.Item
          key={WorkflowMouseType.TOUCHPAD}
          onClick={() => {
            dispatch(onSetConfig({key: 'mode', value: WorkflowMouseType.TOUCHPAD}));
          }}
        >
          <div className="workflow-btn-dropdown-item">
            <IconSvg size={16} type="workflow-btn-touchpad" fill="none" />
            触摸板模式
          </div>
        </Menu.Item>
        <Menu.Item
          key={WorkflowMouseType.MOUSE}
          onClick={() => {
            dispatch(onSetConfig({key: 'mode', value: WorkflowMouseType.MOUSE}));
          }}
        >
          <div className="workflow-btn-dropdown-item">
            <IconSvg size={16} type="workflow-btn-mouse" fill="none" />
            鼠标模式
          </div>
        </Menu.Item>
      </Menu>
    ),
    [config.mode, dispatch]
  );

  return (
    <div className="workflow-btn-list">
      <div
        className="btn-dropdown"
        onClick={() => {
          // fitView();
        }}
      >
        <Dropdown
          trigger={['click']}
          overlay={menu}
          labelIcon
          label={<IconSvg size={16} type={'workflow-btn-' + config.mode} fill="none" />}
        ></Dropdown>
      </div>
      <div
        className="btn-item"
        onClick={() => {
          clickLayout();
        }}
      >
        <Tooltip title="自动布局">
          <div className="tooltip-content">
            <IconSvg size={16} type="workflow-btn-layout" fill="none" />
          </div>
        </Tooltip>
      </div>
      <div
        className="btn-item"
        onClick={() => {
          fitView();
        }}
      >
        <Tooltip title="恢复默认视图">
          <div className="tooltip-content">
            <IconSvg size={16} type="workflow-btn-fix-view" fill="none" />
          </div>
        </Tooltip>
      </div>

      <div
        className="btn-item"
        onClick={() => {
          fullScreen();
        }}
      >
        <Tooltip title={fullScreenFlag ? '退出全屏' : '全屏'}>
          <div className="tooltip-content">
            <IconSvg
              size={16}
              type={fullScreenFlag ? 'workflow-btn-exit-full-screen' : 'workflow-btn-full-screen'}
              fill="none"
            />
          </div>
        </Tooltip>
      </div>

      <div className="btn-item-spacer" />
      <div
        className="btn-item"
        onClick={() => {
          zoomOut();
        }}
      >
        <Tooltip title={'缩小10%'}>
          <div className="tooltip-content">
            <IconSvg size={16} type={'workflow-btn-zoom-out'} fill="none" />
          </div>
        </Tooltip>
      </div>
      <div
        className="btn-item"
        onClick={() => {
          zoomIn();
        }}
      >
        <Tooltip title={'放大10%'}>
          <div className="tooltip-content">
            <IconSvg size={16} type={'workflow-btn-zoom-in'} fill="none" />
          </div>
        </Tooltip>
      </div>

      <div
        className="btn-item"
        onClick={() => {
          miniMap();
        }}
      >
        <Tooltip title={'小地图'}>
          <div className={`tooltip-content workflow-btn-mini-map ${config.miniMap ? 'active' : ''}`}>
            <IconSvg
              size={16}
              type={'workflow-btn-mini-map'}
              fill="none"
              color={config.miniMap ? '#333aff' : '#000'}
            />
          </div>
        </Tooltip>
      </div>

      {/* <div className="btn-item-spacer" /> */}

      {/* <div className="btn-add" onClick={() => {}}>
        <Button disabled={disabledCreate} icon={<IconSvg size={16} type={'add'} fill="none" />}>
          添加节点
        </Button>
      </div> */}
    </div>
  );
};
export default React.memo(ButtonList);
