//  全屏
export const goToFullscreen = (id: string) => {
  const el: any = document.getElementById(id);
  if (el.requestFullscreen) {
    el.requestFullscreen();
  } else if (el.webkitRequestFullscreen) {
    // Safari
    el.webkitRequestFullscreen();
  } else if (el.msRequestFullscreen) {
    // IE11
    el.msRequestFullscreen();
  }
};
//  退出全屏
export const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
};
