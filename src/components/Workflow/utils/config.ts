import {X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';

// 默认配置
export const defaultSize = {
  task: {
    width: 320,
    height: 52
  },
  group: {
    width: 320,
    height: 52
  }
};

// 节点大小 位置
export const NODE_DEFAULT_SIZE = {
  [X6ShapeTypeEnum.ROOT_GROUP]: {
    width: 384,
    height: 154,
    // 任务节点 上下左右 预留间距
    padding: {top: 70, bottom: 32, left: 32, right: 32},
    // 垂直间距
    verticalSpacing: 40,
    // 水平间距
    horizontalSpacing: 60
  },
  [X6ShapeTypeEnum.GROUP]: {
    width: 352,
    height: 70,
    // 任务节点 上下左右 预留间距
    padding: {top: 70, bottom: 16, left: 16, right: 16},
    // 垂直间距
    verticalSpacing: 40,
    // 水平间距
    horizontalSpacing: 60
  },
  [X6ShapeTypeEnum.TASK]: {
    width: 320,
    height: 52,
    // 任务节点 上下左右 预留间距
    padding: {top: 52, bottom: 16, left: 16, right: 16},
    // 垂直间距
    verticalSpacing: 30,
    // 水平间距
    horizontalSpacing: 30,
    zIndex: 1
  },
  [X6ShapeTypeEnum.OPERATOR]: {
    width: 288,
    height: 28,
    zIndex: 40
  }
};
