import {X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {Edge, Node} from '@xyflow/react';
import ELK, {LayoutOptions} from 'elkjs/lib/elk.bundled.js';
import {NODE_DEFAULT_SIZE} from '../config';

const rootGroupConfig = NODE_DEFAULT_SIZE[X6ShapeTypeEnum.ROOT_GROUP];
const groupConfig = NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP];
const taskConfig = NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK];

//  根任务组布局配置
const layoutRootGroupOptions: LayoutOptions = {
  'elk.algorithm': 'layered',
  'elk.hierarchyHandling': 'INCLUDE_CHILDREN', // 支持跨层连线
  'elk.direction': 'RIGHT', // 方向：DOWN (上下), RIGHT (左右)
  'elk.padding': `[top=${rootGroupConfig.padding.top},left=${rootGroupConfig.padding.left},bottom=${rootGroupConfig.padding.bottom},right=${rootGroupConfig.padding.right}]`,

  'elk.layered.spacing.baseValue': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.spacing.nodeNode': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.layered.spacing.nodeNodeBetweenLayers': String(
    NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].horizontalSpacing
  )
};

//  任务组布局配置
const layoutGroupOptions: LayoutOptions = {
  'elk.algorithm': 'layered',
  'elk.hierarchyHandling': 'INCLUDE_CHILDREN', // 支持跨层连线
  'elk.direction': 'RIGHT', // 方向：DOWN (上下), RIGHT (左右)
  'elk.padding': `[top=${groupConfig.padding.top},left=${groupConfig.padding.left},bottom=${groupConfig.padding.bottom},right=${groupConfig.padding.right}]`,

  'elk.layered.spacing.baseValue': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.spacing.nodeNode': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.layered.spacing.nodeNodeBetweenLayers': String(
    NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].horizontalSpacing
  )
};
//  任务布局配置
const layoutTaskOptions: LayoutOptions = {
  'elk.algorithm': 'layered',
  'elk.hierarchyHandling': 'INCLUDE_CHILDREN', // 支持跨层连线
  'elk.direction': 'RIGHT', // 方向：DOWN (上下), RIGHT (左右)
  'elk.padding': `[top=${groupConfig.padding.top},left=${groupConfig.padding.left},bottom=${groupConfig.padding.bottom},right=${groupConfig.padding.right}]`,

  'elk.layered.spacing.baseValue': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.spacing.nodeNode': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].verticalSpacing),
  'elk.layered.spacing.nodeNodeBetweenLayers': String(
    NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP].horizontalSpacing
  )
};

//  算子布局配置
const layoutOperatorOptions: LayoutOptions = {
  'elk.algorithm': 'layered',
  'elk.direction': 'DOWN', // 方向：DOWN (上下), RIGHT (左右)
  'elk.padding': `[top=${taskConfig.padding.top},left=${taskConfig.padding.left},bottom=${taskConfig.padding.bottom},right=${taskConfig.padding.right}]`,
  'elk.spacing.nodeNode': String(NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].verticalSpacing),
  'elk.layered.spacing.nodeNodeBetweenLayers': String(
    NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].horizontalSpacing
  )
};

const layoutOptions = {
  [X6ShapeTypeEnum.ROOT_GROUP]: layoutRootGroupOptions,
  [X6ShapeTypeEnum.GROUP]: layoutGroupOptions,
  [X6ShapeTypeEnum.TASK]: layoutTaskOptions
};

const elk = new ELK();

type ElkNodeType = {
  id: string;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  layoutOptions: LayoutOptions;
  children: ElkNodeType[];
  edges: ElkEdgeType[];
};
type ElkEdgeType = {
  id: string;
  sources: string[];
  targets: string[];
};

/**
 * 过滤隐藏节点
 * @param nodes 节点列表
 * @param edges 边列表
 * @returns 过滤后的节点列表和边列表
 */
const delHiddenNode = (nodes: Node[], edges: Edge[]) => {
  const hiddenSet = new Set<string>();
  nodes.forEach((node) => {
    if (node.hidden) {
      hiddenSet.add(node.id);
    }
  });
  return {
    nodes: nodes.filter((node) => !hiddenSet.has(node.id)),
    edges: edges.filter((edge) => !hiddenSet.has(edge.source) && !hiddenSet.has(edge.target))
  };
};
/**
 * 将列表转为树结构
 * @param sourceNodes 节点列表
 * @param sourceEdges 边列表
 * @returns 树结构
 */
const listToTree = (sourceNodes: Node[], sourceEdges: Edge[]) => {
  console.log(sourceNodes, sourceEdges);
  const idToNodeMap = new Map<string, Node>();
  const temTreeNodes = new Map<string, ElkNodeType>();
  // 父节点的所有子节点
  const childrenMap = new Map<string, ElkNodeType[]>();
  const childrenEdgeMap = new Map<string, ElkEdgeType[]>();

  // 无层级连线
  const edgeArr: ElkEdgeType[] = [];
  // 所有根节点
  const rootList: ElkNodeType[] = [];

  const {nodes, edges} = delHiddenNode(sourceNodes, sourceEdges);

  // 预处理节点 分为多层
  nodes.forEach((node) => {
    idToNodeMap.set(node.id, node);
    // 临时节点
    const treeNode = {
      id: node.id,
      children: [],
      edges: [],
      layoutOptions: layoutOptions[node.type],
      width: NODE_DEFAULT_SIZE[node.type].width,
      height: NODE_DEFAULT_SIZE[node.type].height
    };
    temTreeNodes.set(node.id, treeNode);
    if (node.parentId && node.type === X6ShapeTypeEnum.TASK) {
      if (childrenMap.has(node.parentId)) {
        childrenMap.get(node.parentId)!.push(treeNode);
      } else {
        childrenMap.set(node.parentId, [treeNode]);
      }
    } else {
      rootList.push(treeNode);
    }
  });

  // 处理连线关系
  edges.forEach((edge) => {
    const edgeObj = {
      id: edge.id,
      sources: [edge.source],
      targets: [edge.target]
    };

    const sourceNode = idToNodeMap.get(edge.source);
    const targetNode = idToNodeMap.get(edge.target);
    const sourceParentId = sourceNode?.parentId;
    const targetParentId = targetNode?.parentId;

    // 如果 来源 和 目标 都是同一个 父节点
    if (sourceParentId && sourceParentId === targetParentId) {
      if (childrenEdgeMap.has(sourceParentId)) {
        childrenEdgeMap.get(sourceParentId)!.push(edgeObj);
      } else {
        childrenEdgeMap.set(sourceParentId, [edgeObj]);
      }
    } else {
      // } else if (!sourceParentId && !targetParentId) {
      edgeArr.push(edgeObj);
    }
  });

  // 处理每层的节点
  nodes.forEach((node) => {
    const treeNode = temTreeNodes.get(node.id);
    if (childrenMap.has(treeNode.id)) {
      treeNode.children = childrenMap.get(node.id);
      // delete treeNode.width;
      // delete treeNode.height;
    } else {
      delete treeNode.layoutOptions;
    }
    if (childrenEdgeMap.has(treeNode.id)) {
      treeNode.edges = childrenEdgeMap.get(node.id)!;
    }
  });

  return {
    id: 'root',
    layoutOptions: layoutTaskOptions,
    children: rootList,
    edges: edgeArr
  };
};

// 将自动布局后的 tree 提取出来
const treeToMap = (nodes: ElkNodeType[], sizeMap: Map<string, ElkNodeType>) => {
  nodes?.forEach((item) => {
    sizeMap.set(item.id, item);
    treeToMap(item.children, sizeMap);
  });
};
/**
 * 自动布局
 * @param nodes 节点列表
 * @param edges 边列表
 * @returns
 */
export const autoLayoutNodes = async (nodes: Node[], edges: Edge[]) => {
  // 调用elk布局算法进行布局
  const result: any = await elk.layout(listToTree(nodes, edges));

  const sizeMap = new Map<string, ElkNodeType>();
  treeToMap(result.children, sizeMap);

  return nodes.map((node) => {
    if (sizeMap.has(node.id)) {
      const {x, y, width, height} = sizeMap.get(node.id)!;
      return {
        ...node,
        style: {
          width: node.type === X6ShapeTypeEnum.TASK ? NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].width : width,
          height: node.type === X6ShapeTypeEnum.TASK ? NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].height : height
        },
        position: {
          x,
          y
        }
      };
    }
    return node;
  });
};
