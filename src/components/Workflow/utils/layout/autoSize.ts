import {X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {Node} from '@xyflow/react';
import {NODE_DEFAULT_SIZE} from '../config';

// 节点类型映射  任务节点 使用 group的 padding 扩展父节点高度
const typeMap = {
  [X6ShapeTypeEnum.OPERATOR]: NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK],
  [X6ShapeTypeEnum.TASK]: NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP],
  [X6ShapeTypeEnum.GROUP]: NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP],
  [X6ShapeTypeEnum.ROOT_GROUP]: NODE_DEFAULT_SIZE[X6ShapeTypeEnum.ROOT_GROUP]
};
/**
 * 拖动节点 自动调整父节点宽高,只处理 group 节点大小
 * @param nodes 节点数组
 * @param node 拖动节点
 * @returns nodes 节点数组
 */
export function fixParentSize(nodes: Node[], node: Node) {
  const parentId = node?.parentId;
  const parentIndex = nodes.findIndex((n) => n?.id === parentId);
  // 如果没有父节点  直接返回 分组节点也不处理
  if (parentIndex === -1 || node.type === X6ShapeTypeEnum.GROUP) return nodes;
  const parentNode = nodes[parentIndex];
  // 节点配置
  const parentConfig = NODE_DEFAULT_SIZE[parentNode.type];
  const nodeConfig = NODE_DEFAULT_SIZE[node.type];

  // 最小的宽高
  let groupWidth = parentConfig?.width || 0;
  let groupHeight = parentConfig?.height || 0;

  nodes
    .filter((n) => n.type === X6ShapeTypeEnum.TASK && !n.hidden && n.parentId === parentId)
    .forEach((n) => {
      groupWidth = Math.max(groupWidth, n.position.x + nodeConfig.width + parentConfig.padding.right);
      groupHeight = Math.max(groupHeight, n.position.y + nodeConfig.height + parentConfig.padding.bottom);
    });

  const result = nodes.map((n) => {
    if (n.id === parentId) {
      return {
        ...n,
        style: {
          width: Number(groupWidth),
          height: Number(groupHeight)
        }
      };
    } else if (n.id === node.id) {
      // 限制 拖动的节点  避免太靠左和顶部
      return {
        ...n,
        position: {
          x: Math.max(parentConfig.padding.left, node.position.x),
          y: Math.max(parentConfig.padding.top, node.position.y)
        }
      };
    }
    return n;
  });

  return result;
}

/**
 * 折叠节点 自动调整父节点宽高
 * @param nodes 节点列表
 * @param id 节点id
 * @param showChild 是否隐藏子节点
 * @returns 节点列表
 */
export function collapseNodeAndAutoSize(nodes: Node[], id: string, showChild: boolean) {
  const temNodes = nodes.map((node) => {
    if (node.parentId === id && !node.data?.autoShow && node.type === X6ShapeTypeEnum.TASK) {
      return {
        ...node,
        hidden: !showChild
      };
    } else if (node.id === id) {
      return {
        ...node,
        data: {
          ...node.data,
          showChild
        }
      };
    }

    return node;
  });
  const node = nodes.find((node) => node.parentId === id);

  return fixParentSize(temNodes, node);
}

// 递归隐藏所有子节点
export const hideAllChildNodes = (nodes: Node[], parentIds: string[]) => {
  let temNodes = nodes;

  parentIds.forEach((pId) => {
    const temIds = [];
    temNodes = temNodes.map((node) => {
      if (node.parentId === pId || node.data?.parentId === pId) {
        temIds.push(node.id);

        return {
          ...node,
          hidden: true
        };
      }
      return node;
    });
    temNodes = hideAllChildNodes(temNodes, temIds);
  });

  return temNodes;
};

// 展示 group
export const showGroupNodes = (nodes: Node[], ids: string[]) => {
  const set = new Set<string>(ids);
  // 展示分组
  let temNodes = nodes.map((node) => {
    if (set.has(node.id)) {
      return {
        ...node,
        hidden: false
      };
    }
    return node;
  });
  // 展示分组下 默认需要展示的节点
  temNodes = temNodes.map((node) => {
    if (set.has(node.parentId) && node?.data?.autoShow && node.type === X6ShapeTypeEnum.TASK) {
      return {
        ...node,
        hidden: false
      };
    }
    return node;
  });

  return temNodes;
};

/**
 * 展示隐藏依赖分组
 * @param nodes 节点列表
 * @param ids 节点id
 * @param show 是否展示
 * @returns 节点列表
 */
export function showHideDependentGroup(nodes: Node[], ids: string[], show: boolean) {
  // 需要展示节点
  if (show) {
    return showGroupNodes(nodes, ids);
  }

  return hideAllChildNodes(
    nodes.map((item) => {
      if (ids.includes(item.id)) {
        return {
          ...item,
          hidden: true
        };
      }
      return item;
    }),
    ids
  );
}
