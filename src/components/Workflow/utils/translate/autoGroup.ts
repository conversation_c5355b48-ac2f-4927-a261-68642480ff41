/**
 * 处理分组信息方法
 */
import {IJob} from '@api/job';
import {SPLIT_STR, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {Edge, Node} from '@xyflow/react';
import {NODE_DEFAULT_SIZE} from '../config';
/**
 * 自动计算分组大小
 * @param nodes 节点列表
 * @returns
 */
const autoGroupSize = (nodes: Node[], type: X6ShapeTypeEnum) => {
  let maxX = 0;
  let maxY = 0;
  nodes.forEach((node) => {
    if (node.position?.x > maxX && !node.hidden) {
      maxX = node.position?.x;
    }
    if (node.position?.y > maxY && !node.hidden) {
      maxY = node.position?.y;
    }
  });
  return {
    width: maxX + NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].width + NODE_DEFAULT_SIZE[type].padding.right,
    height: maxY + NODE_DEFAULT_SIZE[X6ShapeTypeEnum.TASK].height + NODE_DEFAULT_SIZE[type].padding.bottom
  };
};
/**
 * 添加普通工作流,包括上游节点 和下游节点（DAG+补数据使用）
 * @param nodes 需要分组的节点列表,这里是 处理工作流转为 nodes 数据
 * @param edges 边列表
 * @param job 工作流信息
 * @param sourceId 父节点 id 根节点 为 空字符串
 * @returns
 */
export const dealGroup = (
  nodes: Node[],
  edges: Edge[],
  job: IJob,
  sourceId = ''
): {nodes: Node[]; edges: Edge[]} => {
  const groupId = (sourceId ? sourceId + SPLIT_STR : '') + job.workspaceId + SPLIT_STR + job.jobId;
  // 处理节点
  const temNodes: Node[] = nodes.map((node) => {
    let parentId;
    // 如果有父节点
    if (node.parentId) {
      parentId = groupId + SPLIT_STR + node.parentId;
    } else {
      parentId = groupId;
    }
    return {
      ...node,
      id: groupId + SPLIT_STR + node.id,
      extent: 'parent',
      parentId
    };
  });
  // 添加组节点 需要在前面添加
  temNodes.unshift({
    id: groupId,
    type: !sourceId ? X6ShapeTypeEnum.ROOT_GROUP : X6ShapeTypeEnum.GROUP,
    position: {x: 0, y: 0},
    data: {...job, showChild: !sourceId, isInit: true, parentId: sourceId},
    style: autoGroupSize(temNodes, sourceId ? X6ShapeTypeEnum.GROUP : X6ShapeTypeEnum.ROOT_GROUP)
  });
  return {
    nodes: temNodes,
    edges: edges.map((edge) => {
      const source = groupId + SPLIT_STR + edge.source;
      const target = groupId + SPLIT_STR + edge.target;
      return {
        ...edge,
        id: source + SPLIT_STR + target,
        source,
        target
      };
    })
  };
};
