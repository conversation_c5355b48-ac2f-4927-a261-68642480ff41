import {detailJob} from '@api/job';
import {IJobDependency} from '@api/workflow';
import {NodeData, NodeEdge} from '@components/Workflow/types/types';
import {JobNodeTypeEnum, SPLIT_STR, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {IJsonData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import {Edge, Node} from '@xyflow/react';
import _ from 'lodash';
import {NODE_DEFAULT_SIZE} from '../config';
import {dealGroup} from './autoGroup';
import {jsonToWorkflow} from './jsonToWorkflow';

const GroupConfig = NODE_DEFAULT_SIZE[X6ShapeTypeEnum.GROUP];
/**
 * 处理工作流的依赖 添加 节点 依赖数量 添加依赖节点的工资流分组
 * nodes: Node[] 包含 group 节点
 * jobDependencyList: IJobDependency[] 依赖列表
 * job: IJob 工作流信息
 *
 * 返回
 * nodes: Node[] 包含 group 节点
 */
export const dealWorkflowNodeDependency = (
  nodes: Node[],
  edges: Edge[],
  jobDependencyList: IJobDependency[],
  sourceGroupId: string
): {nodes: Node[]; edges: Edge[]} => {
  // // 获取父节点
  // const parentObj = nodes[0];
  // // 源端的 group ID
  // const sourceGroupId = parentObj.id;

  const mapDep = new Map<string, IJobDependency[]>();

  const newNodes: Node[] = [];
  const newEdges: Edge[] = [];

  const groupSet = new Set<string>();

  const nodeGroupNumMap = new Map<string, number>();
  // 依赖列表
  jobDependencyList.forEach((item) => {
    // 新的工作流分组 每一个item代表一个分组
    let depGroupId = sourceGroupId;
    // 连线起点 原始工作流分组 后者 里面的节点
    let source = sourceGroupId;

    if (item.taskId) {
      depGroupId =
        sourceGroupId +
        SPLIT_STR +
        item.taskId +
        SPLIT_STR +
        item.dependencyWorkspaceId +
        SPLIT_STR +
        item.dependencyJobId;
      source = sourceGroupId + SPLIT_STR + item.taskId;
    } else {
      depGroupId = sourceGroupId + SPLIT_STR + item.dependencyWorkspaceId + SPLIT_STR + item.dependencyJobId;
    }

    item.id = depGroupId;
    // 避免  一个工作流多个节点依赖同一个节点
    if (!groupSet.has(depGroupId)) {
      const groupNum = nodeGroupNumMap.get(source) || 0;
      nodeGroupNumMap.set(source, groupNum + 1);
      newNodes.push({
        id: depGroupId,
        type: X6ShapeTypeEnum.GROUP,
        position: {
          x: 0 + groupNum * (GroupConfig.width + GroupConfig.horizontalSpacing),
          y: GroupConfig.height + GroupConfig.verticalSpacing
        },
        hidden: true,
        style: {
          width: GroupConfig.width,
          height: GroupConfig.height
        },
        data: {
          parentId: source,
          name: item.dependencyJobName,
          workspaceId: item.dependencyWorkspaceId,
          jobId: item.dependencyJobId,
          version: item?.dependencyJobVersion,
          type: X6ShapeTypeEnum.GROUP,
          isInit: false,
          showChild: false
        }
      } as Node);
    }
    groupSet.add(depGroupId);

    // 目标节点  依赖组件节点
    const target = depGroupId + SPLIT_STR + item.dependencyTaskId;
    newNodes.push({
      id: depGroupId + SPLIT_STR + item.dependencyTaskId,
      type: X6ShapeTypeEnum.TASK,
      position: {x: GroupConfig.padding.left, y: GroupConfig.padding.top},
      parentId: depGroupId,
      hidden: true,
      data: {
        id: item.dependencyTaskId,
        name: item.dependencyTaskName,
        autoShow: true,
        type: JobNodeTypeEnum.DEPENDENT_TASK
      }
    } as Node);

    // 连线
    newEdges.push({
      id: source + SPLIT_STR + target,
      source,
      target,
      type: X6ShapeTypeEnum.DEP_OUTPUT_EDGE
    } as Edge);

    const nodeId = item.taskId ? sourceGroupId + SPLIT_STR + item.taskId : sourceGroupId;
    if (mapDep.has(nodeId)) {
      mapDep.get(nodeId)!.push(item);
    } else {
      mapDep.set(nodeId, [item]);
    }
  });

  return {
    nodes: [
      ...nodes.map((item: Node) => {
        const id = String(item?.id);
        if (mapDep.has(id)) {
          return {
            ...item,
            data: {
              ...item.data,
              dependencyList: mapDep.get(id)
            }
          };
        }
        return item;
      }),
      ...newNodes
    ],
    edges: [...edges, ...newEdges]
  };
};

// 加载依赖节点依赖的工作流
// 1. 加载依赖工作流
// 2. 添加分组工作流
// 3. 添加连线
export const dealDependencyNode = async (id: string, data: NodeData) => {
  console.log(data);
  const {result: jobDetail} = await detailJob(data?.taskParam?.workspaceId, data?.taskParam?.jobId);

  const obj: IJsonData = JSON.parse(jobDetail?.code || '{}');

  const {nodes, edges} = await jsonToWorkflow(obj);

  const source =
    id +
    SPLIT_STR +
    data?.taskParam?.workspaceId +
    SPLIT_STR +
    data?.taskParam?.jobId +
    (data?.taskParam?.depTaskId ? SPLIT_STR + data?.taskParam?.depTaskId : '');
  const target = id;

  nodes.forEach((node) => {
    // 隐藏非连线节点
    if (node.id === data?.taskParam?.depTaskId) {
      node.data.autoShow = true;
    } else {
      node.hidden = true;
    }
  });
  // 添加分组信息
  const {nodes: groupNodes, edges: groupEdges} = dealGroup(nodes, edges, jobDetail, id);

  // 展开节点 默认 向上展开
  groupNodes[0].position.y = GroupConfig.height + GroupConfig.verticalSpacing;

  // 添加连线

  return {
    nodes: groupNodes,
    edges: [
      ...groupEdges,
      {id: source + SPLIT_STR + target, source, target, type: X6ShapeTypeEnum.DEP_INPUT_EDGE}
    ]
  };
};

// 未初始化工作流 完善工作流
export const dealUnInitNode = async (
  workspaceId: string,
  jobId: string,
  sourceId: string,
  oldNodes: Node[]
): Promise<NodeEdge & {sourceNodeIs: string[]}> => {
  const {result: jobDetail} = await detailJob(workspaceId, jobId);

  const obj: IJsonData = JSON.parse(jobDetail?.code || '{}');

  const {nodes, edges} = await jsonToWorkflow(obj);

  // 添加分组信息
  const {nodes: groupNodes, edges: groupEdges} = dealGroup(nodes, edges, jobDetail, sourceId);

  const groupDependencyId = groupNodes[0].id;

  // 折叠也需要展示的节点
  const showNodes = oldNodes
    .filter((item) => item.type === X6ShapeTypeEnum.TASK)
    .filter((item) => item.parentId === groupDependencyId || item.data.autoShow)
    .map((item) => {
      return item.id;
    });

  // 需要额外处理的节点 未初始化 但是已添加到工作流中
  const dealNodeArr = [...showNodes, groupDependencyId];
  const dealNodeMap = new Map();

  groupNodes.forEach((item) => {
    if (dealNodeArr.includes(item.id)) {
      dealNodeMap.set(item.id, item);
    }
  });
  // 更新节点数据
  const temNodes = oldNodes.map((item) => {
    if (dealNodeMap.has(item.id)) {
      return {
        ...item,
        data: {
          isInit: true,
          autoShow: true,
          ...dealNodeMap.get(item.id)?.data
        }
      };
    }
    return item;
  });
  // 删除已经初始化的节点
  const newNodes = groupNodes.filter((item) => !dealNodeArr.includes(item.id));

  console.log(newNodes);
  console.log(temNodes);

  return {
    nodes: [...temNodes, ...newNodes],
    edges: [...groupEdges],
    sourceNodeIs: obj.taskDefinitionList.map((item) => item.id)
  };
};

// 避免重复添加id相同的节点
export const deRepeatedNodes = (nodes: Node[]) => {
  const result = new Map();
  _.forEach(nodes, (node) => {
    result.set(node.id, node);
  });
  return Array.from(result.values());
};
