import {AppNode, CommonGroupNode} from '@components/Workflow/types/types';
import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';

/**
 * 若取消节点选择状态 并且当前节点是依赖节点，取消依赖节点所有上游节点（节点的子节点）
 * ids 需要取消的普通节点
 */
const uncheckChildren = (nodes: AppNode[], ids: string[]): string[] => {
  if (ids.length === 0) {
    return [];
  }
  let uncheckArr: string[] = [];
  const depIds: string[] = [];
  // 处理需要取消勾选的 节点  和 操作的依赖节点
  nodes.forEach((node: AppNode) => {
    // 如果是依赖节点  需要隐藏依赖节点所有子节点
    if (ids.includes(node?.id)) {
      if (node.data.type === JobNodeTypeEnum.DEPENDENT_TASK) {
        depIds.push(node.id);
      }
      uncheckArr.push(node.id);
    }
  });
  // 如果 处理的包含依赖节点
  if (depIds.length > 0) {
    const groupIds = [];
    nodes.forEach((node) => {
      if (depIds.includes((node as CommonGroupNode)?.data?.parentId)) {
        groupIds.push(node.id);
      }
    });
    const temArr = [];
    // 遍历所有子节点 取消勾选
    nodes.forEach((node) => {
      if (groupIds.includes(node?.parentId) && node?.data?.checked) {
        temArr.push(node.id);
      }
    });
    uncheckArr = uncheckArr.concat(uncheckChildren(nodes, temArr));
  }

  return uncheckArr;
};

// 若选择节点，需要选择上游所有节点的 依赖节点
const checkParentDependNode = (nodes: AppNode[], id: string): string[] => {
  if (!id) {
    return [];
  }
  const node = nodes.find((item) => item.id === id);
  const pGroup = nodes.find((item) => item.id === node?.parentId);

  if (!pGroup?.data?.parentId) {
    return [id];
  }

  const pDependNode = nodes.find((item) => item.id === pGroup?.data?.parentId);

  return [id, ...checkParentDependNode(nodes, pDependNode?.id)];
};

// 选择节点 需要选择父节点的 依赖节点
// 取消节点 需要取消依赖节点的所有子节点
export const checkNode = (nodes: AppNode[], id: string, checked: boolean): string[] => {
  console.log('checkNode', id, checked);
  if (checked) {
    return checkParentDependNode(nodes, id);
  } else {
    return uncheckChildren(nodes, [id]);
  }
};
