import {SPLIT_STR, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {IJsonData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import {nodeMap} from '@pages/JobWorkflow/Jobs/components/EditPage/globalVar';
import {Edge, Node} from '@xyflow/react';
import {isNumber} from 'lodash';
import {autoLayoutNodes} from '../layout/autoLayout';

/**
 * 判断所有节点是否都配置了 position 信息 不需要查看算子位置
 * @param jsonData json 数据
 * @returns 是否都配置了 position 信息
 *
 * @example
 * isAllNodePositionConfig({taskDefinitionList: [{id: '1', name: '任务1'}, {id: '2', name: '算子1'}]}) // false
 */
const isAllNodePositionConfig = (jsonData: IJsonData): boolean => {
  // 是否需要自动布局
  let autoLayout = false;
  // 判断所有节点是否都配置了 position 信息
  for (const item of jsonData?.taskDefinitionList || []) {
    const position = item.location?.position;
    if (!position || !isNumber(position.x) || !isNumber(position.y)) {
      autoLayout = true;
      break;
    }
    // zIndex 配置 重新布局
    if (item.location?.zIndex && item.location?.size) {
      autoLayout = true;
      break;
    }
  }
  return autoLayout;
};
/**
 * 处理后端 json 格式到 工作流 数据
 * @param jsonData json 数据
 * @returns x6 数据
 *
 * @example
 * dealJsonToX6({taskDefinitionList: [{id: '1', name: '任务1'}, {id: '2', name: '算子1'}]})
 *
 * {cells: [
 *   {id: '1', shape: X6ShapeTypeEnum.TASK, data: {name: '任务1'}},
 *   {id: '2', shape: X6ShapeTypeEnum.OPERATOR, data: {name: '算子1'}},
 * ]})
 */
export const jsonToWorkflow = async (jsonData: IJsonData): Promise<{nodes: Node[]; edges: Edge[]}> => {
  // 是否自动布局
  const autoLayout = isAllNodePositionConfig(jsonData);

  const nodeIdSet: Set<string> = new Set<string>();

  // 任务节点
  const nodeArr: Node[] = [];
  // 任务 算子节点
  const nodeOperatorArr: Node[] = [];
  const edgeArr: Edge[] = [];
  // 算子连线关系
  const edgeOperatorArr: Edge[] = [];

  // 节点 zIndex 记录 任务节点 每层增加 10
  const zIndexMap = new Map<string, number>();
  let zIndex = 1;
  jsonData?.taskDefinitionList?.forEach((item) => {
    zIndexMap.set(item.id, zIndex++ * 10);
  });
  // 处理任务 连线
  jsonData?.taskRelationList?.forEach((item) => {
    edgeArr.push({
      id: item.source + SPLIT_STR + item.target,
      type: X6ShapeTypeEnum.TASK_EDGE,
      ...item
    });
  });
  // 处理 算子连线关系
  jsonData?.taskDefinitionList?.forEach((item) => {
    item.operatorRelationList?.forEach((operate) => {
      const source = item.id + SPLIT_STR + operate.source;
      const target = item.id + SPLIT_STR + operate.target;
      edgeOperatorArr.push({
        source,
        target,
        type: X6ShapeTypeEnum.OPERATOR_EDGE,
        id: source + SPLIT_STR + target
      });
    });
  });
  // 清空节点map
  nodeMap.clear();
  // 处理节点
  jsonData?.taskDefinitionList?.forEach((item) => {
    // 避免任务 id 重复
    if (nodeIdSet.has(item.id)) {
      return;
    }
    nodeIdSet.add(item.id);
    const operatorArr: Node[] = [];
    // 处理算子
    if (item.operatorList) {
      item.operatorList?.forEach((operate) => {
        const operatorId = item.id + SPLIT_STR + operate.id;
        // 避免算子 id 重复
        if (nodeIdSet.has(operatorId)) {
          return;
        }
        nodeIdSet.add(operatorId);
        operatorArr.push({
          id: operatorId,
          parentId: item.id,
          extent: 'parent',
          hidden: true,
          type: X6ShapeTypeEnum.OPERATOR,
          position: {
            x: operate.location?.position?.x,
            y: operate.location?.position?.y
          },
          data: {
            ...operate
          }
        });

        // 记录算子
        nodeMap.set(operatorId, operate);
      });
    }

    nodeArr.push({
      id: item.id,
      type: X6ShapeTypeEnum.TASK,
      position: {
        x: item.location?.position?.x,
        y: item.location?.position?.y
      },
      data: {
        ...item
      }
    });
    nodeOperatorArr.push(...operatorArr);

    // 记录任务
    nodeMap.set(item.id, item);
  });
  const result = {
    nodes: [...nodeArr, ...nodeOperatorArr],
    edges: [...edgeArr, ...edgeOperatorArr]
  };
  if (autoLayout) {
    result.nodes = await autoLayoutNodes(result.nodes, result.edges);
  }

  return result;
};
