import {<PERSON><PERSON><PERSON>} from '@api/job';
import {IJobDependency} from '@api/workflow';
import {JobNodeTypeEnum, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {IJsonNodeParamsData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import type {Edge, Node} from '@xyflow/react';

export type NodeData = {
  parentId?: string;
  // 真实的 节点 id
  id: string;
  name: string;
  type: JobNodeTypeEnum;
  status?: string;
  // 被哪些节点依赖
  dependencyList?: IJobDependency[];
  // 是否展示子节点（算子 ）
  showChild?: boolean;
  // 自动展示(和 group 一起展示隐藏 与折叠无关)
  autoShow?: boolean;
  // 参数
  taskParam?: IJsonNodeParamsData;
  // 是否选择
  checked?: boolean;
  showDependencyInput?: boolean;
  showDependencyOutput?: boolean;
};

export type GroupNodeData = {
  parentId?: string;
  // 真实的 节点 id
  id: string;
  workspaceId: string;
  jobId: string;
  version?: number;
  name: string;
  type: X6ShapeTypeEnum.GROUP | X6ShapeTypeEnum.ROOT_GROUP;
  // 被哪些节点依赖
  dependencyList?: IJobDependency[];
  // 是否展示子节点（group 下的 task）
  showChild?: boolean;
  // 是否初始化
  isInit?: boolean;
  // 调度时间
  scheduleConf?: ICron;
  // 是否选择
  checked?: boolean;
  showDependencyOutput?: boolean;
};

export type WorkflowNodeProps = Node<NodeData>;
export type WorkflowGroupNodeProps = Node<GroupNodeData>;

export type CommonTaskNode = Node<NodeData, X6ShapeTypeEnum.TASK>;
export type CommonGroupNode = Node<GroupNodeData, X6ShapeTypeEnum.GROUP>;
export type AppNode = CommonGroupNode | CommonTaskNode | Node;
export type AppNodeType = AppNode['type'];

export type NodeEdge = {
  nodes: Node[];
  edges: Edge[];
};
