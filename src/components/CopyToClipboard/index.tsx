/**
 * 复制到剪贴板组件
 * @param text 需要复制的文本
 * @param style 自定义样式
 * @param className 自定义类名
 * @param iconClassName 自定义图标类名
 * @author: <EMAIL>
 */
import React from 'react';
import IconSvg from '@components/IconSvg';
import {toast} from 'acud';
interface CopyParams {
  // 需要copy到剪贴板的文本
  text: string;
  showText?: boolean;
  style?: React.CSSProperties;
  className?: string;
  iconClassName?: string;
}
const CopyToClipboard: React.FC<CopyParams> = ({text, showText = true, style, className, iconClassName}) => {
  const handleCopy2Clipboard = async (text: string) => {
    if (!text) return;
    try {
      await navigator.clipboard.writeText(text);
      toast.open({message: '复制成功', duration: 3});
    } catch (e) {
      console.error('复制失败', e);
    }
  };
  return (
    <div style={style} className={className}>
      {showText && text}
      <IconSvg type="copy" onClick={() => handleCopy2Clipboard(text)} className={iconClassName} />
    </div>
  );
};
export default CopyToClipboard;
