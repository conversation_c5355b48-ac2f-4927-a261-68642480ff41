import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {GetWorkspaceFileListResult} from '@api/WorkArea';

export interface EditorFile extends GetWorkspaceFileListResult {
  metaData?: any;
}

// 定义初始状态
interface NotebookState {
  tabList: EditorFile[];
  activeTabId: string;
  curFolderId: string;
  curFolder: GetWorkspaceFileListResult;
}

const initialState: NotebookState = {
  activeTabId: '',
  tabList: [],
  curFolderId: '',
  curFolder: null
};

// 创建 slice
const workEditorSlice = createSlice({
  name: 'workEditor',
  initialState,
  reducers: {
    resetState: (state) => {
      state.activeTabId = initialState.activeTabId;
      state.tabList = initialState.tabList;
      state.curFolderId = initialState.curFolderId;
    },
    setCurFolderId: (state, action: PayloadAction<string>) => {
      state.curFolderId = action.payload;
    },
    setCurFolder: (state, action: PayloadAction<GetWorkspaceFileListResult>) => {
      state.curFolder = action.payload;
    },
    setActiveTabId: (state, action: PayloadAction<string>) => {
      state.activeTabId = action.payload;
    },
    setTabList: (state, action: PayloadAction<EditorFile[]>) => {
      state.tabList = action.payload;
    },
    pushTab: (state, action: PayloadAction<EditorFile>) => {
      state.tabList.push(action.payload);
    },
    removeTab: (state, action: PayloadAction<string>) => {
      state.tabList = state.tabList.filter((item) => item.id !== action.payload);
    },
    updateTabProperty: (state, action: PayloadAction<{id: string; property: any}>) => {
      state.tabList = state.tabList.map((item) => {
        if (item.id === action.payload.id) {
          return {...item, ...action.payload.property};
        }
        return item;
      });
    }
  }
});

// 导出 actions
export const {
  resetState,
  setCurFolderId,
  setCurFolder,
  setActiveTabId,
  setTabList,
  pushTab,
  removeTab,
  updateTabProperty
} = workEditorSlice.actions;

// 导出 reducer
export default workEditorSlice.reducer;
