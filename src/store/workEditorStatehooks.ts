import {useDispatch, useSelector} from 'react-redux';
import {IAppState, IAppDispatch} from './index';
import {
  EditorFile,
  setActiveTabId as setActiveTabIdAction,
  pushTab as pushTabAction,
  removeTab as removeTabAction,
  updateTabProperty as updateTabPropertyAction,
  resetState as resetStateAction,
  setCurFolderId as setCurFolderIdAction,
  setTabList as setTabListAction,
  setCurFolder as setCurFolderAction
} from './workEditorSlice';
import {GetWorkspaceFileListResult} from '@api/WorkArea';
import {FilePrivilege, getFilePrivilege} from '@pages/WorkArea/utils';
import {useMemo} from 'react';

// 使用 TypeScript 类型增强的 hooks
export const useAppDispatch = () => useDispatch<IAppDispatch>();
export const useAppSelector = <T>(selector: (state: IAppState) => T): T =>
  useSelector<IAppState, T>(selector);

// Notebook 相关的自定义 hook
export const useWorkEditor = () => {
  const dispatch = useAppDispatch();

  const tabList = useAppSelector((state) => state.workEditorSlice.tabList);
  const activeTabId = useAppSelector((state) => state.workEditorSlice.activeTabId);
  const curFolderId = useAppSelector((state) => state.workEditorSlice.curFolderId);
  const curFolder = useAppSelector((state) => state.workEditorSlice.curFolder);
  const curFolderPrivilege = useMemo(() => {
    if (!curFolder) {
      return {
        privilege: null,
        canManage: false,
        canModify: false,
        canExecute: false,
        canView: false
      };
    }
    return getFilePrivilege(curFolder?.privileges || []);
  }, [curFolder]);

  const resetState = () => {
    dispatch(resetStateAction());
  };

  const setActiveTabId = (id: string) => {
    dispatch(setActiveTabIdAction(id));
  };

  const pushTab = (file: EditorFile) => {
    dispatch(pushTabAction(file));
  };

  const removeTab = (id: string) => {
    dispatch(removeTabAction(id));
  };

  const updateTabProperty = (id: string, property: any) => {
    dispatch(updateTabPropertyAction({id, property}));
  };

  const setCurFolderId = (id: string) => {
    dispatch(setCurFolderIdAction(id));
  };

  const setTabList = (list: EditorFile[]) => {
    dispatch(setTabListAction(list));
  };

  const setCurFolder = (folder: GetWorkspaceFileListResult) => {
    dispatch(setCurFolderAction(folder));
  };

  return {
    tabList,
    activeTabId,
    curFolderId,
    curFolder,
    curFolderPrivilege,
    resetState,
    setCurFolderId,
    setActiveTabId,
    pushTab,
    removeTab,
    updateTabProperty,
    setTabList,
    setCurFolder
  };
};
