import {AppNode, NodeData} from '@components/Workflow/types/types';
import {fixParentSize} from '@components/Workflow/utils/layout/autoSize';
import {deRepeatedNodes} from '@components/Workflow/utils/translate/addWorkflow';
import {WorkflowMouseType, X6PageTypeEnum} from '@pages/JobWorkflow/constants';
import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Connection,
  Edge,
  EdgeChange,
  NodeChange
} from '@xyflow/react';

// 工作流状态 这里只放置影响页面展示的数据
interface IWorkflowState {
  // 节点列表
  nodes: AppNode[];
  // 边列表
  edges: Edge[];
  config: {
    fullscreen: boolean;
    mode: WorkflowMouseType;
    miniMap: boolean;
    pageType: X6PageTypeEnum;
  };
}

const initialState: IWorkflowState = {
  config: {
    fullscreen: false,
    mode: WorkflowMouseType.MOUSE,
    miniMap: true,
    pageType: X6PageTypeEnum.JOB_EDIT
  },
  nodes: [],
  edges: []
};

const workflowSlice = createSlice({
  name: 'workflow1',
  initialState,
  reducers: {
    // 工作流全量更新
    setNodes: (state, action: PayloadAction<AppNode[]>) => {
      state.nodes = deRepeatedNodes(action.payload);
    },
    // 更新边列表
    setEdges: (state, action: PayloadAction<Edge[]>) => {
      console.log('setEdges', action.payload);
      state.edges = action.payload;
    },
    // 更新节点数据
    onNodeDataChange(state, action: PayloadAction<{id: string; data: NodeData}>) {
      state.nodes = state.nodes.map((n) => {
        if (n.id === action.payload.id) {
          return {
            ...n,
            data: {
              ...n.data,
              ...action.payload.data
            }
          };
        }
        return n;
      });
    },

    // 更新节点选中状态
    onNodeCheckedChange(state, action: PayloadAction<{ids: string[]; checked: boolean}>) {
      state.nodes = state.nodes.map((n) => {
        if (action.payload.ids.includes(n.id)) {
          return {
            ...n,
            data: {
              ...n.data,
              checked: action.payload.checked
            }
          };
        }
        return n;
      });
    },

    // 拖动节点 动态计算父节点大小
    onNodeDragChange(state, action: PayloadAction<AppNode>) {
      state.nodes = fixParentSize(state.nodes, action.payload);
    },

    onNodesChange(state, action: PayloadAction<NodeChange[]>) {
      state.nodes = applyNodeChanges(action.payload, state.nodes);
    },
    onEdgesChange(state, action: PayloadAction<EdgeChange[]>) {
      state.edges = applyEdgeChanges(action.payload, state.edges);
    },
    onConnect(state, action: PayloadAction<Connection>) {
      console.log('onConnect', action.payload);
      state.edges = addEdge(action.payload, state.edges);
    },

    onAddNode(state, action: PayloadAction<AppNode>) {
      state.nodes.push(action.payload);
    },

    onAddEdge(state, action: PayloadAction<Edge>) {
      state.edges = addEdge(action.payload, state.edges);
    },

    // 设置配置
    onSetConfig(state, action: PayloadAction<{key: string; value: any}>) {
      state.config[action.payload.key] = action.payload.value;
    }
  }
});

export const {
  setNodes,
  setEdges,
  onNodeDataChange,
  onNodeCheckedChange,
  onNodesChange,
  onNodeDragChange,
  onEdgesChange,
  onConnect,
  onAddNode,
  onAddEdge,
  onSetConfig
} = workflowSlice.actions;
export default workflowSlice.reducer;
