import {useEffect, useRef} from 'react';
import {CommandRegistry} from '@lumino/commands';

export const useHotkey = (commandId: string, {keys, selector, execute}) => {
  const commands = useRef<CommandRegistry>(new CommandRegistry());

  useEffect(() => {
    const cmds = commands.current;
    const cmd = cmds.addCommand(commandId, {
      execute: execute
    });
    const hotkey = cmds.addKeyBinding({
      selector: selector,
      keys: keys,
      command: commandId
    });

    function keydownHandler(event: KeyboardEvent) {
      cmds.processKeydownEvent(event);
    }
    document.addEventListener('keydown', keydownHandler, true);
    return () => {
      document.removeEventListener('keydown', keydownHandler, true);
      cmd.dispose();
      hotkey.dispose();
    };
  }, [execute]);

  return commands;
};
