import React, {useRef} from 'react';
import {Privilege} from '@api/permission/type';
import {Menu, Modal} from 'acud';
import {Clipboard} from '@baidu/bce-react-toolkit';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import CreateFolderModal, {CreateFolderModalRef} from '@pages/WorkArea/components/modals/CreateFolderModal';
import ImportFileModal, {ImportFileModalRef} from '@pages/WorkArea/components/modals/ImportFileModal';
import MoveModal, {MoveModalRef} from '@pages/WorkArea/components/modals/MoveModal';
import RenameModal, {RenameModalRef} from '@pages/WorkArea/components/modals/RenameModal';
import RestoreModal, {RestoreModalRef} from '@pages/WorkArea/components/modals/RestoreModal';
import CopyModal, {CopyModalRef} from '@pages/WorkArea/components/modals/CopyModal';
import {PRIVILEGE_LIST} from '@pages/WorkArea/config';
import {deleteFile, deleteFolder, permanentDeleteFile, permanentDeleteFolder} from '@api/WorkArea';
import {GetWorkspaceFileListResult} from '@api/WorkArea';
import {getSqlFileName} from '@pages/WorkArea/utils';
import {createFile} from '@api/workEditor';

interface UseFileMenuParams {
  workspaceId: string;
  folderMenuHide?: string[];
  fileMenuHide?: string[];
  onOpenBlank?: (record: GetWorkspaceFileListResult) => void;
  onDeleteSuccess?: () => void;
  onPermanentDeleteSuccess?: () => void;
  onCreateFileSuccess?: (record: GetWorkspaceFileListResult) => void;
  onCopySuccess?: () => void;
  onCreateFolderSuccess?: () => void;
  onImportFileSuccess?: () => void;
  onMoveSuccess?: () => void;
  onRenameSuccess?: () => void;
}

export function useFileMenu({
  workspaceId,
  folderMenuHide = [],
  fileMenuHide = [],
  onOpenBlank,
  onDeleteSuccess,
  onPermanentDeleteSuccess,
  onCreateFileSuccess,
  onCopySuccess,
  onCreateFolderSuccess,
  onImportFileSuccess,
  onMoveSuccess,
  onRenameSuccess
}: UseFileMenuParams) {
  const copyModalRef = useRef<CopyModalRef>(null);
  const createFolderModalRef = useRef<CreateFolderModalRef>(null);
  const importFileModalRef = useRef<ImportFileModalRef>(null);
  const moveModalRef = useRef<MoveModalRef>(null);
  const renameModalRef = useRef<RenameModalRef>(null);
  const restoreModalRef = useRef<RestoreModalRef>(null);
  const permissionModalRef = useRef<PermissionModalRef>(null);

  // 临时删除文件、文件夹
  function handleDelete(record: GetWorkspaceFileListResult) {
    Modal.confirm({
      title: '删除',
      content: `“${record.name}” 删除后，将被移动至回收站，默认保留30天，确认是否删除？`,
      onOk: async () => {
        let api;
        switch (record?.nodeType) {
          case 'FOLDER':
            api = deleteFolder;
            break;
          default:
            api = deleteFile;
            break;
        }
        await api({id: record.id, workspaceId});
        onDeleteSuccess?.();
      }
    });
  }

  // 永久删除文件、文件夹
  function handlePermanentDelete(record: GetWorkspaceFileListResult) {
    Modal.confirm({
      title: '永久删除',
      content: `“${record.name}” 将被永久删除，此操作无法撤销，所有相关数据将被清空，请谨慎操作。`,
      onOk: async () => {
        let api;
        switch (record?.nodeType) {
          case 'FOLDER':
            api = permanentDeleteFolder;
            break;
          default:
            api = permanentDeleteFile;
            break;
        }
        await api({id: record.id, workspaceId});
        onPermanentDeleteSuccess?.();
      }
    });
  }

  async function handleCreateFile(record: GetWorkspaceFileListResult) {
    const res = await createFile(workspaceId, {
      parentId: record.id,
      name: getSqlFileName()
    });
    if (res.success && res.result) {
      onCreateFileSuccess?.(res.result);
    }
  }

  const renderFileMenu = (record: GetWorkspaceFileListResult) => {
    // 包含导入/删除/移动/重命名/修改权限
    const isManage = record?.privileges?.includes(Privilege.Manage);
    // 复制权限
    const isCopy = [Privilege.Modify, Privilege.Manage].some((item) => record?.privileges?.includes(item));
    const isView = record?.privileges?.length > 0;
    const copyPath = (
      <Menu.Item key="copyPath">
        <Clipboard text={record.path} successMessage="复制路径成功">
          <span>复制路径</span>
        </Clipboard>
      </Menu.Item>
    );

    const copy = (
      <AuthMenuItem
        isAuth={isCopy}
        key="copy"
        onClick={() => {
          copyModalRef.current?.open(record);
        }}
      >
        复制
      </AuthMenuItem>
    );

    const rename = (
      <AuthMenuItem
        isAuth={isManage}
        key="rename"
        onClick={() => {
          renameModalRef.current?.open(record);
        }}
      >
        重命名
      </AuthMenuItem>
    );

    const move = (
      <AuthMenuItem
        isAuth={isManage}
        key="move"
        onClick={() => {
          moveModalRef.current?.open(record);
        }}
      >
        移动
      </AuthMenuItem>
    );

    const deleteItem = (
      <AuthMenuItem
        isAuth={isManage}
        key="delete"
        onClick={() => {
          handleDelete(record);
        }}
      >
        删除
      </AuthMenuItem>
    );

    const openBlank = (
      <AuthMenuItem isAuth={isView} key="openBlank" onClick={() => onOpenBlank?.(record)}>
        在新窗口打开
      </AuthMenuItem>
    );

    const newFolder = (
      <AuthMenuItem
        isAuth={isManage}
        key="newFolder"
        onClick={() => {
          createFolderModalRef.current?.open(record);
        }}
      >
        创建文件夹
      </AuthMenuItem>
    );

    const importFile = (
      <AuthMenuItem
        isAuth={isManage}
        key="importFile"
        onClick={() => {
          importFileModalRef.current?.open(record);
        }}
      >
        导入文件
      </AuthMenuItem>
    );

    const restore = (
      <AuthMenuItem
        isAuth={isManage}
        key="restore"
        onClick={() => {
          restoreModalRef.current?.open(record);
        }}
      >
        恢复
      </AuthMenuItem>
    );

    const permanentDelete = (
      <AuthMenuItem
        isAuth={isManage}
        key="permanentDelete"
        onClick={() => {
          handlePermanentDelete(record);
        }}
      >
        永久删除
      </AuthMenuItem>
    );

    const newFile = (
      <AuthMenuItem
        isAuth={isManage}
        key="newFile"
        onClick={() => {
          handleCreateFile(record);
        }}
      >
        创建文件
      </AuthMenuItem>
    );

    const permission = (
      <AuthMenuItem
        isAuth={isManage}
        key="permission"
        onClick={() => {
          const resourceTypeMap = {
            FOLDER: 'DIRECTORY',
            FILE: 'FILE',
            NOTEBOOK: 'NOTEBOOK',
            WORKFLOW: 'FILE'
          };
          permissionModalRef.current?.open({
            resourceType: resourceTypeMap[record.nodeType],
            resourceId: record.id,
            resourceName: record.name,
            privilegeList: PRIVILEGE_LIST
          });
        }}
      >
        权限管理
      </AuthMenuItem>
    );
    const createSubMenu = (
      <Menu.SubMenu key="create" title="创建">
        {newFolder}
        {newFile}
      </Menu.SubMenu>
    );

    let menuItems: React.ReactNode[] = [];
    // 文件夹
    if (record.nodeType === 'FOLDER') {
      const menuItemsMap = {
        HOME: [openBlank, createSubMenu, importFile, copyPath],
        USER: [openBlank, createSubMenu, importFile, copyPath],
        USERS: [openBlank, copyPath],
        TRASH: [openBlank],
        NORMAL: [openBlank, createSubMenu, importFile, copy, copyPath, rename, permission, move, deleteItem],
        SHARED: [openBlank, createSubMenu, importFile, copyPath]
      };
      menuItems = menuItemsMap[record.type] || menuItemsMap.NORMAL;
      menuItems = menuItems.filter((item) => !folderMenuHide.includes(item.key));
    }
    // 普通文件
    if (record.nodeType === 'FILE' || record.nodeType === 'NOTEBOOK' || record.nodeType === 'WORKFLOW') {
      menuItems = [copy, copyPath, rename, permission, move, deleteItem];
      menuItems = menuItems.filter((item) => !fileMenuHide.includes(item.key));
    }
    // 回收站内容
    const isInTrash = record.status === 'DELETED';
    if (isInTrash) {
      menuItems = [restore, permanentDelete];
    }

    const menu = <Menu>{menuItems}</Menu>;
    return menu;
  };

  const renderModals = () => {
    return (
      <>
        <CopyModal ref={copyModalRef} workspaceId={workspaceId} onSuccess={onCopySuccess} />
        <CreateFolderModal
          ref={createFolderModalRef}
          workspaceId={workspaceId}
          onSuccess={onCreateFolderSuccess}
        />
        <ImportFileModal ref={importFileModalRef} workspaceId={workspaceId} onSuccess={onImportFileSuccess} />
        <MoveModal ref={moveModalRef} workspaceId={workspaceId} onSuccess={onMoveSuccess} />
        <RenameModal ref={renameModalRef} workspaceId={workspaceId} onSuccess={onRenameSuccess} />
        <RestoreModal ref={restoreModalRef} workspaceId={workspaceId} />
        <PermissionModal ref={permissionModalRef} workspaceId={workspaceId} />
      </>
    );
  };

  return {
    renderFileMenu,
    renderModals
  };
}
