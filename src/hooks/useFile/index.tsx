import React, {useMemo} from 'react';
import {Tooltip} from 'acud';
import IconSvg from '@components/IconSvg';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {GetWorkspaceFileListResult} from '@api/WorkArea';
import {FileNodeTypeEnum} from '@components/WorkareaPathSelect';
import FolderIcon from './icons/folder.svg';
import NotebookIcon from './icons/notebook.svg';
import FileIcon from './icons/file.svg';
import WorkflowIcon from './icons/workflow.svg';
import AllIcon from './icons/all.svg';
import HomeIcon from './icons/home.svg';
import TrashIcon from './icons/trash.svg';
import UsersIcon from './icons/users.svg';
import UserIcon from './icons/user.svg';
import SharedIcon from './icons/shared.svg';
const cx = classNames.bind(styles);

export function useFileIcon() {
  return {
    renderIcon: (record: GetWorkspaceFileListResult) => {
      let icon = null;
      if (record.nodeType === 'FOLDER') {
        const folderMap = {
          ALL: <AllIcon className={cx('file-icon')} />,
          HOME: <HomeIcon className={cx('file-icon')} />,
          TRASH: <TrashIcon className={cx('file-icon')} />,
          USERS: <UsersIcon className={cx('file-icon')} />,
          USER: <UserIcon className={cx('file-icon')} />,
          NORMAL: <FolderIcon className={cx('file-icon')} />,
          SHARED: <SharedIcon className={cx('file-icon')} />
        };
        icon = folderMap[record.type || 'NORMAL'];
      } else {
        const iconMap = {
          FILE: <FileIcon className={cx('file-icon')} />,
          NOTEBOOK: <NotebookIcon className={cx('file-icon')} />,
          WORKFLOW: <WorkflowIcon className={cx('file-icon')} style={{color: '#00BFBF'}} />
        };
        icon = iconMap[record.nodeType || 'FILE'];
      }

      return icon;
    }
  };
}

interface UseFileTextProps {
  onFolderClick?: (record: GetWorkspaceFileListResult) => void;
  onNotebookClick?: (record: GetWorkspaceFileListResult) => void;
  onSqlClick?: (record: GetWorkspaceFileListResult) => void;
  onWorkflowClick?: (record: GetWorkspaceFileListResult) => void;
}
export function useFileText(config?: UseFileTextProps) {
  const {onFolderClick, onNotebookClick, onSqlClick, onWorkflowClick} = config || {};
  return {
    renderText: (record: GetWorkspaceFileListResult) => {
      const isInTrash = record.status === 'DELETED';
      if (record.nodeType === 'FOLDER') {
        return (
          <Tooltip placement="topLeft" title={record.name}>
            {isInTrash ? record.name : <a onClick={() => onFolderClick?.(record)}>{record.name}</a>}
          </Tooltip>
        );
      }
      if (record.nodeType === 'NOTEBOOK') {
        return (
          <Tooltip placement="topLeft" title={record.name}>
            {isInTrash ? record.name : <a onClick={() => onNotebookClick?.(record)}>{record.name}</a>}
          </Tooltip>
        );
      }

      if (record.nodeType === 'FILE' && record.name.endsWith('.sql')) {
        return (
          <Tooltip placement="topLeft" title={record.name}>
            {isInTrash ? record.name : <a onClick={() => onSqlClick?.(record)}>{record.name}</a>}
          </Tooltip>
        );
      }

      if (record.nodeType === FileNodeTypeEnum.WORKFLOW) {
        return (
          <Tooltip placement="topLeft" title={record.name}>
            {isInTrash ? record.name : <a onClick={() => onWorkflowClick?.(record)}>{record.name}</a>}
          </Tooltip>
        );
      }
      return (
        <Tooltip placement="topLeft" title={record.name}>
          {record.name}
        </Tooltip>
      );
    }
  };
}

export {useFileMenu} from './useFileMenu';

export function useAddBtnDisabled(folder: GetWorkspaceFileListResult) {
  return useMemo(() => ['ALL', 'TRASH', 'USERS'].includes(folder?.type), [folder]);
}
