/**
 * @file 存储路径辅助工具
 * @description 用于处理私有化场景下 HDFS/S3 存储路径的统一逻辑
 */

import flags from '@/flags';

/**
 * 判断是否为私有化环境
 */
export const isPrivate = flags.DatabuilderPrivateSwitch;

/**
 * 判断是否启用 S3
 */
export const isEnableS3 = () => {
  return window.PRIVATE_STATIC?.mainPageConfig?.enableS3 || false;
};

/**
 * 获取存储类型名称
 * @returns 'S3' | 'HDFS' | 'BOS'
 */
export const getStorageTypeName = (): string => {
  if (!isPrivate) {
    return 'BOS';
  }
  return isEnableS3() ? 'S3' : 'HDFS';
};

/**
 * 获取存储路径前缀
 * @returns 's3://' | 'hdfs://' | 'bos://'
 */
export const getStoragePrefix = (): string => {
  if (!isPrivate) {
    return 'bos://';
  }
  return isEnableS3() ? 's3://' : 'hdfs://';
};

/**
 * 拼接完整的存储路径
 * @param path 路径（不含前缀）
 * @returns 完整路径
 */
export const buildStorageLocation = (path: string): string => {
  if (!path) {
    return '';
  }
  return `${getStoragePrefix()}${path}`;
};

/**
 * 获取存储路径输入框的配置
 * @returns 输入框配置对象
 */
export const getStorageInputConfig = () => {
  const enableS3 = isEnableS3();
  const storageType = getStorageTypeName();

  return {
    // 输入框前缀
    addonBefore: getStoragePrefix(),
    // 占位符文本
    placeholder: `请输入${storageType}路径`,
    // 必填提示
    requiredMessage: `请输入${storageType}路径`,
    // 提示文本
    extraText: enableS3
      ? '填写S3存储路径'
      : isPrivate
        ? '填写hdfs://fs.defaultFS参数值/目录名称，其中"fs.defaultFS参数值"从HDFS的core-site.xml文件中获取'
        : 'BOS路径需要和工作空间在同一个地域'
  };
};
