<svg width="74" height="74" viewBox="0 0 74 74" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.3855 0.932158C36.3845 0.355336 37.6155 0.355337 38.6145 0.932158L67.4284 17.5678C68.4275 18.1447 69.0429 19.2107 69.0429 20.3643V53.6357C69.0429 54.7893 68.4275 55.8553 67.4284 56.4322L38.6145 73.0678C37.6155 73.6447 36.3845 73.6447 35.3855 73.0678L6.5716 56.4322C5.57252 55.8553 4.95706 54.7893 4.95706 53.6357V20.3643C4.95706 19.2107 5.57252 18.1447 6.57161 17.5678L35.3855 0.932158Z" fill="url(#paint0_linear_2837_7531)"/>
<path d="M34.1406 1.2627C35.9098 0.24124 38.0902 0.241241 39.8594 1.2627L66.5205 16.6553C68.2895 17.6768 69.3789 19.5646 69.3789 21.6074V52.3926C69.3789 54.4354 68.2895 56.3232 66.5205 57.3447L39.8594 72.7373C38.0902 73.7588 35.9098 73.7588 34.1406 72.7373L7.47949 57.3447C5.71045 56.3232 4.62109 54.4354 4.62109 52.3926V21.6074C4.62109 19.5646 5.71045 17.6768 7.47949 16.6553L34.1406 1.2627Z" fill="url(#paint1_linear_2837_7531)" stroke="#E8E9EB" stroke-width="0.672727"/>
<mask id="mask0_2837_7531" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="4" y="0" width="66" height="74">
<path d="M34.1406 1.2627C35.9098 0.24124 38.0902 0.241241 39.8594 1.2627L66.5205 16.6553C68.2895 17.6768 69.3789 19.5646 69.3789 21.6074V52.3926C69.3789 54.4354 68.2895 56.3232 66.5205 57.3447L39.8594 72.7373C38.0902 73.7588 35.9098 73.7588 34.1406 72.7373L7.47949 57.3447C5.71045 56.3232 4.62109 54.4354 4.62109 52.3926V21.6074C4.62109 19.5646 5.71045 17.6768 7.47949 16.6553L34.1406 1.2627Z" fill="white" stroke="white" stroke-width="0.672727"/>
</mask>
<g mask="url(#mask0_2837_7531)">
<foreignObject x="8.93152" y="15.5912" width="55.3967" height="63.5373"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.19px);clip-path:url(#bgblur_0_2837_7531_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_diii_2837_7531)" data-figma-bg-blur-radius="4.38879">
<rect x="13.3203" y="19.98" width="46.62" height="54.76" rx="5.38182" fill="url(#paint2_linear_2837_7531)"/>
<rect x="13.6073" y="20.2669" width="46.0461" height="54.1861" rx="5.09485" stroke="#151B26" stroke-width="0.573929"/>
</g>
<circle cx="20.35" cy="27.0102" r="1.85" fill="#151B26"/>
<circle cx="26.2699" cy="27.0102" r="1.85" fill="#151B26"/>
<circle cx="32.1898" cy="27.0102" r="1.85" fill="#151B26"/>
<g filter="url(#filter1_ii_2837_7531)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.6202 41.4399C41.7159 41.4399 37.7402 45.4157 37.7402 50.3199C37.7402 55.2242 41.7159 59.1999 46.6202 59.1999C51.5245 59.1999 55.5002 55.2242 55.5002 50.3199H46.6202V41.4399Z" fill="url(#paint3_linear_2837_7531)"/>
</g>
<mask id="mask1_2837_7531" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="37" y="41" width="19" height="19">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.6202 41.4399C41.7159 41.4399 37.7402 45.4157 37.7402 50.3199C37.7402 55.2242 41.7159 59.1999 46.6202 59.1999C51.5245 59.1999 55.5002 55.2242 55.5002 50.3199H46.6202V41.4399Z" fill="white"/>
</mask>
<g mask="url(#mask1_2837_7531)">
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M56.2394 49.5802C56.2394 44.6759 52.2637 40.7002 47.3594 40.7002L47.3594 49.5802H56.2394Z" fill="#E7E9ED"/>
<rect x="18.5" y="35.52" width="27.38" height="2.96" rx="0.741749" fill="#BFC7D4"/>
<rect x="18.5" y="42.9199" width="12.58" height="2.96" rx="0.741749" fill="#BFC7D4"/>
<rect x="18.5" y="50.3198" width="8.14" height="2.96" rx="0.741749" fill="#BFC7D4"/>
</g>
<defs>
<filter id="filter0_diii_2837_7531" x="8.93152" y="15.5912" width="55.3967" height="63.5373" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.74"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.070071 0 0 0 0 0.0904324 0 0 0 0 0.123345 0 0 0 0.0954257 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2837_7531"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2837_7531" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2837_7531"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.48"/>
<feGaussianBlur stdDeviation="1.85"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2837_7531" result="effect3_innerShadow_2837_7531"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.85"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_2837_7531" result="effect4_innerShadow_2837_7531"/>
</filter>
<clipPath id="bgblur_0_2837_7531_clip_path" transform="translate(-8.93152 -15.5912)"><rect x="13.3203" y="19.98" width="46.62" height="54.76" rx="5.38182"/>
</clipPath><filter id="filter1_ii_2837_7531" x="37.7402" y="39.9599" width="17.7598" height="19.2398" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.74"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2837_7531"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.48"/>
<feGaussianBlur stdDeviation="0.74"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0613773 0 0 0 0 0.146988 0 0 0 0 0.247447 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2837_7531" result="effect2_innerShadow_2837_7531"/>
</filter>
<linearGradient id="paint0_linear_2837_7531" x1="-4.38986" y1="-8.77973" x2="-4.38986" y2="74" gradientUnits="userSpaceOnUse">
<stop stop-color="#F0F2F4" stop-opacity="0.5"/>
<stop offset="1" stop-color="#CED3DB" stop-opacity="0.500081"/>
</linearGradient>
<linearGradient id="paint1_linear_2837_7531" x1="-4.38986" y1="-8.77973" x2="-4.38986" y2="74" gradientUnits="userSpaceOnUse">
<stop stop-color="#F0F2F4" stop-opacity="0.5"/>
<stop offset="1" stop-color="#CED3DB" stop-opacity="0.500081"/>
</linearGradient>
<linearGradient id="paint2_linear_2837_7531" x1="35.1015" y1="83.2111" x2="35.1015" y2="57.1892" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.306845"/>
<stop offset="1" stop-color="white" stop-opacity="0.903745"/>
</linearGradient>
<linearGradient id="paint3_linear_2837_7531" x1="36.8051" y1="50.4945" x2="47.7193" y2="60.7334" gradientUnits="userSpaceOnUse">
<stop stop-color="#0764FF"/>
<stop offset="1" stop-color="#4DC5FE"/>
</linearGradient>
</defs>
</svg>
