# API方法生成

## 要求
- 根据提供的接口定义文档，生成接口调用方法。
- 生成到src/api文件夹内。
- 使用src/api/apiFunction.tsx中的request方法，以及urlPrefix请求路径前缀。

## 示例
```ts
import {request, BaseResponseType, urlPrefix} from './apiFunction';
interface GetComputeResourceListParams {
  workspaceId: string;
  pageNo?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
  // 计算实例类型
  engine?: Engine;
}

export interface ComputeResourceItem {
  workspaceId: string; // 工作空间id
  name: string; // 计算实例名字
  status: ComputeStatus;
  creator: string; // 创建者名字
  createdAt: string; // 创建时间
  computeId?: string; // 计算实例 id
  region?: string; // 地域
  runtime?: string; // 运行环境
  clusterType?: string; // 集群资源规格
  engine?: string; // 集群计算引擎
  accountId?: string; // 账户id
  updatedAt?: string; // 更新时间
  nodeCount?: number; // 节点数量
  mirrorVersion?: string; // 镜像版本
}

export interface GetComputeResourceListResult {
  computes: ComputeResourceItem[];
  total: number;
}

export function getComputeResourceList(
  params: GetComputeResourceListParams
): BaseResponseType<GetComputeResourceListResult> {
  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/instances`,
    method: 'GET',
    params
  });
}
```

# Coding Standards

## Language Requirements

**All code, comments, documentation, variable names, function names, class names, file names.**

### Code Style
- Use TypeScript for all new code
- Follow consistent naming conventions:
  - camelCase for variables and functions
  - PascalCase for classes and components
  - UPPER_SNAKE_CASE for constants
  - camelCase for file names
- Use meaningful and descriptive English names
- Write clear and concise English comments
- Follow ESLint and Prettier configurations

### File Naming
- All files must use English names
- Use camelCase for utility files (e.g., `apiClient.ts`)
- Use descriptive English names that clearly indicate the file's purpose

### Documentation
- All documentation must be in English
- Use clear and professional English in README files
- Write English comments for complex logic
- API documentation should be in English


# Development Guide

## component usage
The current project uses the acud component library as the UI component library. The acud components are used first to meet the requirements. The document of the acud component library is the acud-docs.md file in the project root directory.

## component creation
The newly created component needs to contain the index.tsx file and the index.module.less file, which store the logic and style of the component respectively. Use a folder to contain these two files, and the folder name is the component name.
