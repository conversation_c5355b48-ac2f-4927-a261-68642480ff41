{"name": "@baidu/db-jupyter-react", "version": "1.0.1", "description": "Jupyter React - React.js components 100% compatible with Jupyter.", "license": "MIT", "main": "lib/index.js", "files": ["lib/**/*.{css,d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,js,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json"], "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "react"], "repository": {"type": "git", "url": "https://github.com/datalayer/jupyter-ui.git", "directory": "packages/react"}, "sideEffects": ["style/*.css", "src/**/*.css", "style/index.js"], "styleModule": "style/index.js", "publishConfig": {"access": "public"}, "scripts": {"build": "npm run clean && gulp resources-to-lib && tsc", "build:lib": "tsc", "build:prod": "gulp resources-to-lib && tsc && npm run clean && npm run build:lib", "build:tsc:watch:res": "gulp resources-to-lib-watch", "build:tsc:watch:tsc": "tsc --watch", "build:webpack": "cross-env BUILD_APP=true webpack-cli build", "clean": "rimraf lib dist build tsconfig.tsbuildinfo", "clean:all": "npm run clean:lib && npm run clean:labextension && npm run clean:lintcache", "clean:labextension": "rimraf datalayer/labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "clean:lintcache": "rimraf .eslintcache .stylelintcache", "eslint": "npm eslint:check --fix", "eslint:check": "eslint . --cache --ext .ts,.tsx", "install:extension": "npm run build", "kill": "./../../dev/sh/kill.sh || true", "lint": "npm stylelint && npm prettier && npm eslint", "lint:check": "npm stylelint:check && npm prettier:check && npm eslint:check", "prettier": "npm prettier:base --write --list-different", "prettier:base": "prettier \"**/*{.ts,.tsx,.js,.jsx,.css,.json,.md}\"", "prettier:check": "npm prettier:base --check", "start": "run-p -c 'start:*'", "start:webpack": "webpack serve", "start-noconfig": "cross-env NO_CONFIG=true webpack serve", "start-local": "run-p -c 'start-local:*'", "start-local:webpack": "cross-env LOCAL_JUPYTER_SERVER=true webpack serve", "start-local:jupyter-server": "cd ./../.. && make start-jupyter-server", "start-server": "jupyter server --config jupyter_server_config.py", "stylelint": "npm stylelint:check --fix", "stylelint:check": "stylelint --cache \"style/**/*.css\"", "test": "jest --coverage", "test:visual": "playwright test", "typedoc": "typedoc ./src", "watch": "run-p watch:src", "watch:src": "tsc -w"}, "dependencies": {"@codemirror/lang-python": "^6.0.1", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.7.0", "@datalayer/primer-addons": "^0.3.2", "@jupyter-widgets/base": "^6.0.0", "@jupyter-widgets/controls": "^5.0.0", "@jupyter-widgets/html-manager": "^1.0.0", "@jupyter-widgets/jupyterlab-manager": "^5.0.0", "@jupyter-widgets/output": "^6.0.0", "@jupyter/web-components": "^0.15.3", "@jupyter/ydoc": "3.0.2", "@jupyterlab/application": "^4.0.0", "@jupyterlab/application-extension": "^4.0.0", "@jupyterlab/apputils": "^4.0.0", "@jupyterlab/apputils-extension": "^4.0.0", "@jupyterlab/cell-toolbar-extension": "^4.0.0", "@jupyterlab/cells": "^4.0.0", "@jupyterlab/codemirror": "^4.0.0", "@jupyterlab/codemirror-extension": "^4.0.0", "@jupyterlab/completer": "^4.0.0", "@jupyterlab/completer-extension": "^4.0.0", "@jupyterlab/console": "^4.0.0", "@jupyterlab/console-extension": "^4.0.0", "@jupyterlab/coreutils": "^6.0.0", "@jupyterlab/csvviewer-extension": "^4.0.0", "@jupyterlab/docmanager": "^4.0.0", "@jupyterlab/docmanager-extension": "^4.0.0", "@jupyterlab/docregistry": "^4.0.0", "@jupyterlab/documentsearch": "^4.0.0", "@jupyterlab/documentsearch-extension": "^4.0.0", "@jupyterlab/filebrowser": "^4.0.0", "@jupyterlab/filebrowser-extension": "^4.0.0", "@jupyterlab/fileeditor": "^4.0.0", "@jupyterlab/fileeditor-extension": "^4.0.0", "@jupyterlab/javascript-extension": "^4.0.0", "@jupyterlab/json-extension": "^4.0.0", "@jupyterlab/launcher": "^4.0.0", "@jupyterlab/launcher-extension": "^4.0.0", "@jupyterlab/lsp-extension": "^4.0.0", "@jupyterlab/mainmenu-extension": "^4.0.0", "@jupyterlab/markdownviewer-extension": "^4.0.0", "@jupyterlab/markedparser-extension": "^4.0.0", "@jupyterlab/mathjax-extension": "^4.0.0", "@jupyterlab/metadataform": "^4.0.0", "@jupyterlab/nbconvert-css": "^4.0.0", "@jupyterlab/nbformat": "^4.0.0", "@jupyterlab/notebook": "^4.0.0", "@jupyterlab/notebook-extension": "^4.0.0", "@jupyterlab/observables": "^5.0.0", "@jupyterlab/outputarea": "^4.0.0", "@jupyterlab/property-inspector": "^4.0.0", "@jupyterlab/rendermime": "^4.0.0", "@jupyterlab/rendermime-extension": "^4.0.0", "@jupyterlab/rendermime-interfaces": "^3.9.0", "@jupyterlab/services": "^7.0.0", "@jupyterlab/settingregistry": "^4.0.0", "@jupyterlab/shortcuts-extension": "^4.0.0", "@jupyterlab/statusbar-extension": "^4.0.0", "@jupyterlab/terminal": "^4.0.0", "@jupyterlab/theme-dark-extension": "^4.0.0", "@jupyterlab/theme-light-extension": "^4.0.0", "@jupyterlab/toc-extension": "^6.0.0", "@jupyterlab/translation-extension": "^4.0.0", "@jupyterlab/ui-components-extension": "^4.0.0", "@jupyterlite/pyodide-kernel-extension": "^0.4.0", "@jupyterlite/server": "^0.4.0", "@jupyterlite/server-extension": "^0.4.0", "@lumino/default-theme": "^2.0.0", "@primer/react": "^36.27.0", "assert": "^2.0.0", "bufferutil": "^4.0.8", "codemirror": "^6.0.1", "encoding": "^0.1.13", "lodash": "^4.17.4", "marked": "^4.0.10", "plotly.js": "^2.35.2", "react-error-boundary": "^3.1.3", "react-inspector": "^6.0.2", "react-sparklines": "^1.7.0", "rxjs": "^6.6.0", "styled-components": "^5.3.10", "ulid": "^2.3.0", "usehooks-ts": "^2.9.1", "utf-8-validate": "^6.0.3", "wildcard-match": "^5.1.2", "y-protocols": "^1.0.5", "y-websocket": "^2.1.0", "yjs": "^13.5.40", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-typescript": "^7.22.10", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@jupyterlab/builder": "^4.0.0", "@jupyterlab/geojson-extension": "^3.4.0", "@jupyterlab/running": "^4.0.0", "@jupyterlab/running-extension": "^4.0.0", "@jupyterlab/testutils": "^4.0.0", "@jupyterlab/vega3-extension": "^3.3.0", "@jupyterlite/javascript-kernel-extension": "^0.3.0", "@mermaid-js/mermaid-zenuml": "0.2.0", "@playwright/test": "^1.40.1", "@primer/octicons-react": "^19.8.0", "@types/codemirror": "^5.60.4", "@types/jest": "^29.4.0", "@types/marked": "^4.0.1", "@types/node": "^18.15.3", "@types/plotly.js": "^2.12.31", "@types/react": "^18.2.12", "@types/react-dom": "^18.2.5", "@types/react-sparklines": "^1.7.5", "@types/semver": "^7.5.6", "@types/styled-components": "^5.1.26", "@types/uuid": "^8.3.0", "@types/webpack-env": "^1.18.2", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "babel-loader": "^9.1.2", "bundle-loader": "^0.5.6", "cross-env": "^7.0.3", "css-loader": "^6.9.1", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "gulp": "^4.0.2", "gulp-append-prepend": "^1.0.8", "gulp-filter": "^6.0.0", "gulp-watch": "^5.0.1", "html-webpack-plugin": "^5.3.1", "html-webpack-tags-plugin": "^2.0.17", "jest": "^29.4.3", "jupyterlab-plotly": "^5.17.0", "mdx-mermaid": "^2.0.0", "mermaid": "^10.9.0", "mkdirp": "^1.0.3", "npm-run-all": "^4.1.5", "prettier": "3.3.2", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "source-map-loader": "^5.0.0", "stream": "^0.0.2", "stream-browserify": "^2.0.2", "style-loader": "^2.0.0", "stylelint": "^15.10.1", "stylelint-config-recommended": "^13.0.0", "stylelint-config-standard": "^34.0.0", "stylelint-csstree-validator": "^3.0.0", "stylelint-prettier": "^4.0.0", "svg-url-loader": "^7.1.1", "ts-jest": "29.0.5", "ts-loader": "^9.4.3", "typedoc": "^0.25.7", "typescript": "^5.0.3", "url-loader": "^3.0.0", "watch": "^1.0.2", "webpack": "^5.74.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.9.3", "whatwg-fetch": "^3.6.2"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "eslintIgnore": ["node_modules", "dist", "coverage", "**/*.d.ts", "tests", "**/__tests__", "playwright.config.ts"], "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"args": "none"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": false}], "curly": ["error", "all"], "eqeqeq": "error", "prefer-arrow-callback": "error"}}, "prettier": {"singleQuote": true, "arrowParens": "avoid", "bracketSpacing": true, "jsxSingleQuote": false, "printWidth": 80, "trailingComma": "es5"}, "stylelint": {"extends": ["stylelint-config-recommended", "stylelint-config-standard", "stylelint-prettier/recommended"], "plugins": ["stylelint-csstree-validator"], "rules": {"csstree/validator": true, "property-no-vendor-prefix": null, "selector-class-pattern": "^([a-z][A-z\\d]*)(-[A-z\\d]+)*$", "selector-no-vendor-prefix": null, "value-no-vendor-prefix": null}}, "packageManager": "yarn@3.5.0"}